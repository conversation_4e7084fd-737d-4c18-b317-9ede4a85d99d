import React, { useEffect, useState } from "react";
import { Content } from "antd/lib/layout/layout";
import {
  Button,
  Drawer,
  Form,
  MenuProps,
  Modal,
  Progress,
  Row,
  Tooltip,
  Upload,
} from "antd";
import Header, { supportedLocales } from "../../components/header";
import { FormTextInput, SingleDateInput } from "@/components/input";
import {
  BackButtonUI,
  PrimaryButtonUI,
  SecondaryButtonUI,
} from "@/components/buttonUI";
import { useRouter } from "next/router";
import { ArrowUpOutlined, EyeOutlined, PlusOutlined } from "@ant-design/icons";
import {
  ListingTableUI,
  MessageErrorUI,
  MessageSuccessUI,
  ModalConfirmUI,
  ModalInfoUI,
  statusApproval,
} from "@/components/ui";
import {
  DataSource,
  encodeParams,
  NumberThousandSeparator,
  setParamsFromLocalStorage,
  getParamsFromLocalStorage,
  setFilterForm,
  PicSignedUrl,
  convertFieldsToString,
  convertFieldsToNumber,
  convertFieldsToTime,
  DataSourceWithPageNumber,
  getCoordinateFromAddress,
} from "@/stores/utilize";
import {
  Outlet,
  Product,
  UOM,
  ProductOrdered,
  SalesOrder,
  OutletShippingAddress,
  Retailer,
  Staff,
  ConfigurableField,
} from "@/components/type";
import { ModalUI } from "@/components/modalUI";
import moment from "moment";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import FilterFormComponent from "@/components/filter";
import {
  SalesOrderFilterOption,
  statusFilterOption1,
} from "@/components/config";
import useRetailerStore from "@/stores/store";
import { getRetailerData } from "@/stores/authContext";
import { ComponentFilterSelect } from "@/components/filterSelectInput";
import AppFooter from "@/components/footer";
import apiHelper from "../api/apiHelper";
import { PDFDocument } from "pdf-lib";
import {
  exportExcelFunction,
  isEmptyObject,
  recheckError,
  transformDataDynamic,
  validateMissingOrInvalidFields,
  validateRequiredFields,
} from "@/components/dynamicExcel";
import * as XLSX from "xlsx";

function SalesOrderListing() {
  const { t } = useTranslation("common");
  const router = useRouter();
  const [filterForm] = Form.useForm();
  const [filterModalForm] = Form.useForm();
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({});
  const [activeAt, setActiveAt] = useState<string>(moment().startOf("day").add(1, "millisecond").toISOString());

  const [showScrollButton, setShowScrollButton] = useState(false);
  const [showButtonLoader, setShowButtonLoader] = useState(false);
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [cursor, setCursor] = useState("");
  const [tempCursor, setTempCursor] = useState("");
  const [tableLoading, setTableLoading] = useState(false);

  const [data, setData] = useState<any[]>([]);
  // const [companyMap, setCompanyMap] = useState(new Map());
  const [outletMap, setOutletMap] = useState(new Map());
  const [productMap, setProductMap] = useState(new Map());
  const [shippingAddressMap, setShippingAddressMap] = useState(new Map());
  const [uomMap, setUomMap] = useState(new Map());
  const [staffMap, setStaffMap] = useState(new Map());
  const [retailerMap, setRetailerMap] = useState(new Map());
  // const [productSkuOption, setProductSkuOption] = useState<SelectOption[]>([]);
  // const [productNameOption, setProductNameOption] = useState<SelectOption[]>(
  //   []
  // );

  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRowData, setSelectedRowData] = useState<any[]>([]);
  // const [pdfLoading, setPDFLoading] = useState(false);
  // ======================================================================
  // filter function - state()
  // ======================================================================
  const [showClearFilter, setShowClearFilter] = useState(false);
  const [fuzzySearchFilter, setFuzzySearchFilter] = useState("");
  const [modalFilter, setModalFilter] = useState<any>({});
  const [filterSetting, setFilterSetting] = useState("");
  const [statusValue, setStatusValue] = useState("All");
  const [statusKey, setStatusKey] = useState("ALL");
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [fieldName, setFieldName] = useState("");
  const [pdfLoading, setPDFLoading] = useState(false);

  const headerItems = [
    {
      label: t("Header.dashboard"),
      route: "/profile/dashboard",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("Header.salesOrder"),
      route: "/salesOrder/salesOrderListing",
      className: "labelTextStyle",
    },
  ];

  const [pathName] = router?.pathname?.split("/").filter(Boolean);

  // use the retailStoreState;
  const outletId = useRetailerStore((state) => state.currentOutletData?.id);
  const [outletInfo, setOutletInfo] = useState<Outlet>();

  // excel state
  const [productList, setProductList] = useState<any[]>([]);
  const [myData, setMyData] = useState<any[]>([]);
  const [number, setNumber] = useState(0);
  const [isUpload, setIsUpload] = useState<boolean>(false);
  const [precent, setPrecent] = useState<number>();

  const [checkoutSetting, setCheckoutSetting] = useState<ConfigurableField>({
    id: '',
    name: '',
    value: '',
    status: '',
  })

  // *************************************************************************************
  // *** EXCEL FILE - Sample Sheet ***
  // *************************************************************************************
  const readMe = [
    {
      Note: "1. newKeyMapping used to map data between salesOrder sheet, productOrdered sheet, and also alternativeShippingAddress sheet.",
    },
    {
      Note: "2. newKeyMapping must be exactly same and exist between two sheet.",
    },
    {
      Note: "3. region field has 4 selections which is CENTRAL, NORTH, SOUTH, and EAST COAST.",
    },
    {
      Note: "4. stateCode can be retrieve frim selectOption sheet, which only required to paste stateCode.",
    },
    {
      Note: "5. Please DO NOT change the Sheet Name! Else, it will not able to read.",
    },
  ];
  const salesOrder = [
    {
      estimatedDeliveredDate: "example: MM/DD/YYYY or DD.MM.YYYY",
      shippingCode: "example: ABC123",
      cpo: "example: 123",
      customerPO: "example: 123",
      remarks: "example: Bundle order",
      newKeyMapping: "example: order1",
    },
    {
      estimatedDeliveredDate: "example: MM/DD/YYYY or DD.MM.YYYY",
      shippingCode: "example: ABC456",
      cpo: "example: 123",
      customerPO: "example: 123",
      remarks: "example: Bundle order",
      newKeyMapping: "example: order2",
    },
  ];
  const productOrdered = [
    {
      newKeyMapping: "example: order1",
      productSKU: "example: FEBFEB20242211",
      productUOM: "example: UNT",
      quantity: "example: 2",
    },
    {
      newKeyMapping: "example: order2",
      productSKU: "example: RITWQ20242211",
      productUOM: "example: UNT",
      quantity: "example: 4",
    },
    {
      newKeyMapping: "example: order2",
      productSKU: "example: RITRIT20242211",
      productUOM: "example: UNT",
      quantity: "example: 1",
    },
  ];
  const alternativeShippingAddress = [
    // { newKeyMapping: "example: order1", shippingContactPerson: "example: Andy", shippingOfficePhone: "example: 60391992093", shippingMobilePhone: "example: 60123456789", shippingLongitude: "example: 123", shippingLatitude: "example: 123", unitNo: "example: 12", address1: "example:Jalan kuching", address2: "example: Kampung melas", city: "example: Seremban", stateCode: "example: 10", postalCode: "example: 55100", region: "example: CENTRAL" },
    // { newKeyMapping: "example: order2", shippingContactPerson: "example: Jeremy", shippingOfficePhone: "example: -", shippingMobilePhone: "example: 60133223344", shippingLongitude: "example: 123", shippingLatitude: "example: 123", unitNo: "example: 2", address1: "example:Jalan merah", address2: "example: -", city: "example: Seremban", stateCode: "example: 10", postalCode: "example: 55100", region: "example: CENTRAL" },
    {
      newKeyMapping: "example: order1",
      shippingContactPerson: "example: Andy",
      shippingOfficePhone: "example: 60391992093",
      shippingMobilePhone: "example: 60123456789",
      address: "example:Jalan kuching",
      stateCode: "example: 10",
      region: "example: CENTRAL",
    },
    {
      newKeyMapping: "example: order2",
      shippingContactPerson: "example: Jeremy",
      shippingOfficePhone: "example: -",
      shippingMobilePhone: "example: 60133223344",
      address: "example:Jalan merah",
      stateCode: "example: 10",
      region: "example: CENTRAL",
    },
  ];
  const selectOptions = [
    { stateCode: "00", stateName: "ALL STATES", "": "", region: "CENTRAL" },
    { stateCode: "01", stateName: "JOHOR", "": "", region: "NORTH" },
    { stateCode: "02", stateName: "KEDAH", "": "", region: "SOUTH" },
    { stateCode: "03", stateName: "KELANTAN", "": "", region: "EAST COAST" },
    { stateCode: "04", stateName: "MELAKA", "": "", region: "" },
    { stateCode: "05", stateName: "NEGERI SEMBILAN", "": "", region: "" },
    { stateCode: "06", stateName: "PAHANG", "": "", region: "" },
    { stateCode: "07", stateName: "PULAU PINANG", "": "", region: "" },
    { stateCode: "08", stateName: "PERAK", "": "", region: "" },
    { stateCode: "09", stateName: "PERLIS", "": "", region: "" },
    { stateCode: "10", stateName: "SELANGOR", "": "", region: "" },
    { stateCode: "11", stateName: "TERENGGANU", "": "", region: "" },
    { stateCode: "12", stateName: "SABAH", "": "", region: "" },
    { stateCode: "13", stateName: "SARAWAK", "": "", region: "" },
    {
      stateCode: "14",
      stateName: "wilayah persekutuan kuala lumpur",
      "": "",
      region: "",
    },
    {
      stateCode: "15",
      stateName: "wilayah persekutuan labuan",
      "": "",
      region: "",
    },
    {
      stateCode: "16",
      stateName: "wilayah persekutuan putrajaya",
      "": "",
      region: "",
    },
    { stateCode: "17", stateName: "NOT APPLICABLE", "": "", region: "" },
  ];
  const keyType = [
    {
      string: [
        // "shippingCode", "cpo", "customerPO", "newKeyMapping", "remarks", "productSKU", "productUOM", "shippingContactPerson", "shippingOfficePhone", "shippingMobilePhone", "shippingLongitude", "shippingLatitude", "unitNo", "address1", "address2", "city", "stateCode", "postalCode"
        "shippingCode",
        "cpo",
        "customerPO",
        "newKeyMapping",
        "remarks",
        "productSKU",
        "productUOM",
        "shippingContactPerson",
        "shippingOfficePhone",
        "shippingMobilePhone",
        "address",
        "region",
      ],
      number: ["quantity"],
      time: ["estimatedDeliveredDate"],
      "salesOrder(RP!)": [
        "estimatedDeliveredDate",
        "shippingCode",
        "cpo",
        "newKeyMapping",
        "remarks",
      ],
      "alternativeShippingAddress(RP!)": [
        // "newKeyMapping", "shippingContactPerson", "shippingMobilePhone", "address1", "city", "stateCode", "region"
        "newKeyMapping",
        "shippingContactPerson",
        "shippingMobilePhone",
        "address",
        "region",
      ],
      "productOrdered(RP!)": [
        "newKeyMapping",
        "productSKU",
        "productUOM",
        "quantity",
      ],
      layering: [
        "salesOrder-productOrdered-newKeyMapping-array",
        "salesOrder-alternativeShippingAddress-newKeyMapping-object",
      ],
    },
  ];

  const excelDataSource = {
    "ReadMe(P!)": readMe,
    salesOrder: salesOrder,
    productOrdered: productOrdered,
    alternativeShippingAddress: alternativeShippingAddress,
    "SelectionOptions(P!)": selectOptions,
    "productList(P!)": productList,
    "keyType(RP!)": keyType,
  };

  const getSellingProductAPI = async (outletId: string) => {
    let cursor: string | null = null;
    let productListMapped: any[] = [];

    do {
      const params: any = {
        maxResultsPerPage: 100,
        sellingType: "SELLING",
        companyId: retailerAccess.companyId,
        outletId: outletId,
        activeAt: activeAt,
      };

      if (cursor) {
        params.pageNumber = cursor; // Use the cursor for pagination
      } else {
        params.pageNumber = "1";
      }

      const dataSource = new DataSourceWithPageNumber(
        "productTradeInfo/aggregate",
        encodeParams(params),
        false
      );

      const res: any = await dataSource.load();
      if (res?.items && res?.items.length) {
        productListMapped = [
          ...productListMapped,
          ...res.items.map((item: Product) => ({
            productSKU: item.sku,
            name: item.name,
          })),
        ];
      }

      cursor = res?.cursor || null; // Update cursor for the next iteration
    } while (cursor !== "0"); // Continue fetching if there is a cursor

    setProductList(productListMapped);
  };

  interface Cpos {
    outletCode?: string | number;
    estimatedDeliveredDate?: number;
    estimatedDeliveredAt?: string;
    staffCode?: string | number;
    retailerCode?: string | number;
    currencyCode?: string;
    companyCode?: string;
    shippingCode?: string | number;
    cpo?: string | number;
    // shippingAddressId: string;
    productOrdered?: CpoProductOrdered[];
    productOrderedCode?: string;
    remarks?: string;
    routePlanEntryId?: string;
    shippingAddressManual?: string;
  }

  interface CpoProductOrdered {
    productOrderedCode?: string;
    barcode?: string | number;
    productSKU?: string | number;
    productUOM?: string;
    price?: number;
    originalPrice?: number;
    quantity?: number;
    originalQuantity?: number;
    type?: string;
    taxId?: string;
  }

  useEffect(() => {
    // Function to check screen size and update state
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth <= 950); // Define your breakpoint for small screens
    };

    // Add event listener for window resize
    window.addEventListener("resize", handleResize);

    // Call handleResize initially to set initial screen size
    handleResize();

    // Clean up the event listener on component unmount
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollButton(window.pageYOffset > 0);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // useEffect(() => {
  //   if (Object.keys(retailerAccess).length) {
  //     getSalesOrder();
  //   }
  // }, [retailerAccess]);

  useEffect(() => {
    if (Object.keys(useRetailerStore.getState()).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }

      // get outlet info to check autoCheckout eligibility
      const outletNameData: any = useRetailerStore.getState().outletNameData || {}
      if (!Object.keys(outletNameData).length) {
        getOutletData().then((value: any) => {
          setActiveAt(value?.nextQualifiedDate ?? moment().startOf("day").add(1, "millisecond").toISOString())
        })
      } else {
        setActiveAt(outletNameData?.nextQualifiedDate ?? moment().startOf("day").add(1, "millisecond").toISOString())
      }
    }
  }, [Object.keys(useRetailerStore.getState()).length]);

  useEffect(() => {
    if (retailerAccess && Object.keys(retailerAccess).length > 0) {
      // getSalesOrder();
      // getProductData();

      // Get from localStorage
      const filterParams: any = getParamsFromLocalStorage(
        router.pathname,
        "salesOrderFilter"
      );

      const filterKey: any = {
        fuzzySearch: "",
        salesOrderNo: null,
        createdDate: null,
        productName: null,
        productSku: null,
      };

      const clonedFilterKey = { ...filterKey };
      delete clonedFilterKey.fuzzySearch;

      Object.keys(clonedFilterKey).forEach((key) => {
        const capitalizedKey = key
          .split(/(?=[A-Z])|\s+/)
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
        clonedFilterKey[capitalizedKey] = clonedFilterKey[key];
        delete clonedFilterKey[key];
      });

      const keysAsString: string = Object.keys(clonedFilterKey).join(", ");
      setFieldName(keysAsString);

      if (filterParams) {
        // Initialize variables to store the values
        // Follow search params' key

        setFilterSetting(filterParams);
        setFilterForm(filterParams, filterKey);
        filterForm.setFieldValue(["fuzzySearch"], filterKey.fuzzySearch);
        filterModalForm.setFieldsValue(filterKey);
        setFuzzySearchFilter(filterKey.fuzzySearch);

        let data = {
          salesOrderNo: filterKey.salesOrderNo || "",
          createdDate: filterKey.createdDate || "",
          productSku: filterKey.productSku || "",
          productName: filterKey.productName || "",
        };
        setModalFilter(data);
        setStatusKey(filterKey.status ?? "ALL");
        const filterStatusLabel: any = statusFilterOption1.find(
          (item: any) => item.key === filterKey.status
        )?.label;
        setStatusValue(filterStatusLabel ?? "All");
      } else {
        getSalesOrder(true);
      }
    }
  }, [retailerAccess]);

  useEffect(() => {
    if (Object.keys(retailerAccess).length) {
      const data = {
        fuzzySearch: fuzzySearchFilter || "",
        salesOrderNo: modalFilter.salesOrderNo || "",
        productSku: modalFilter.productSku || "",
        productName: modalFilter.productName || "",
        createdDate: modalFilter.createdDate || "",
        status: statusKey === "ALL" ? "" : statusKey,
      };

      const allPropertiesEmpty = Object.values(data).every(
        (value) => value === ""
      );

      if (!allPropertiesEmpty) {
        searchSalesOrder(data);
      } else {
        setFilterSetting("");
      }
    }
  }, [fuzzySearchFilter, statusKey, modalFilter, retailerAccess]);

  useEffect(() => {
    if (Object.keys(retailerAccess).length) {
      // Check params whether is same
      const filterParams = getParamsFromLocalStorage(
        router.pathname,
        "salesOrderFilter"
      );
      if (filterSetting) {
        getSalesOrder(true, false);

        if (filterSetting !== filterParams) {
          setParamsFromLocalStorage(
            router.pathname,
            filterSetting,
            "salesOrderFilter"
          );
        }
        setShowClearFilter(true);
      } else {
        setShowClearFilter(false);
        if (data.length > 0) {
          localStorage.removeItem("salesOrderFilter");
        }
        getSalesOrder(true, false);
      }
    }
  }, [filterSetting, retailerAccess]);

  useEffect(() => {
    if (retailerAccess && Object.keys(retailerAccess).length > 0 && outletId) {
      getOutletData(outletId);
      getSellingProductAPI(outletId);
    }
  }, [retailerAccess, outletId]);

  // excel useEffect
  useEffect(() => {
    if (number === 1) {
      setIsUpload(true);
      submitCPOBulk(myData);
    }
  }, [number]);

  // *************************************************************************************
  // *** Scolling Function - useEffect ***
  // *************************************************************************************
  // Check scrolling position
  useEffect(() => {
    if (cursor === "") return;
    const handleScroll = () => {
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight - 50;
      const scrollPosition = window.scrollY;
      if (windowHeight + scrollPosition >= documentHeight) {
        // Stop API calling when cursor is equal to '0'
        if (cursor !== "0") {
          getSalesOrder();
        }
      }
    };
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [cursor]);

  const handleScrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  let isLoading = false;

  const getSalesOrder = (isRefresh = false, isClearFilter = false) => {
    let currentOutletId = localStorage.getItem("currentOutletId");
    if (isLoading) return;
    setTableLoading(true);
    setShowButtonLoader(true);
    // const accessBranches = otherViewBranchAccess === false ? retailerAccess.companyBranchIds : otherCompanyBranchesIds;
    isLoading = true;
    setTimeout(() => {
      let params: any = {
        sort: "createdAt",
        sortOrder: "-1",
        companyId: retailerAccess.companyId,
        // companyBranchId: retailerAccess.companyBranchId,
        outletId: currentOutletId,
      };

      if (isRefresh === false) {
        params.cursor = cursor;
      }

      // if (!isAdmin) {
      //   params.companyBranchIds = accessBranches;
      // }
      // const checkAdminRights = isAdmin ? params : params + "&companyId=" + retailerAccess.companyId;
      const checkFilterRights =
        filterSetting && !isClearFilter
          ? filterSetting +
          (cursor && cursor !== "0" ? `&cursor=${cursor}` : "")
          : encodeParams(params);

      const dataSource = new DataSource(
        "salesOrders",
        checkFilterRights,
        false
      );

      // !isAdmin && !otherViewBranchAccess ? filterForm.setFieldValue("companyId", retailerAccess.companyId) : null;

      // if ((accessBranches && accessBranches.length > 0) || isAdmin) {
      dataSource
        .load()
        .then(async (res: any) => {
          if (res && res.items !== null) {
            let data = res.items;
            const objectMap = data.reduce(
              (accumulator: any, current: SalesOrder) => {
                accumulator["outletId"] = accumulator["outletId"] || [];
                if (
                  current.outletId &&
                  !outletMap.has(current.outletId) &&
                  !accumulator["outletId"].includes(current.outletId)
                ) {
                  accumulator["outletId"].push(current.outletId ?? "");
                }

                accumulator["shippingAddressId"] =
                  accumulator["shippingAddressId"] || [];
                if (
                  current.shippingAddressId &&
                  !shippingAddressMap.has(current.shippingAddressId) &&
                  !accumulator["shippingAddressId"].includes(
                    current.shippingAddressId
                  )
                ) {
                  accumulator["shippingAddressId"].push(
                    current.shippingAddressId ?? ""
                  );
                }

                accumulator["staffId"] = accumulator["staffId"] || [];
                if (
                  current.staffId &&
                  !staffMap.has(current.staffId) &&
                  !accumulator["staffId"].includes(current.staffId)
                ) {
                  accumulator["staffId"].push(current.staffId ?? "");
                }

                accumulator["retailerId"] = accumulator["retailerId"] || [];
                if (
                  current.retailerId &&
                  !retailerMap.has(current.retailerId) &&
                  !accumulator["retailerId"].includes(current.retailerId)
                ) {
                  accumulator["retailerId"].push(current.retailerId ?? "");
                }

                current.productOrdered?.reduce(
                  (acc: any, product: ProductOrdered) => {
                    accumulator["productId"] = accumulator["productId"] || [];
                    if (
                      product.productId &&
                      !productMap.has(product.productId) &&
                      !accumulator["productId"].includes(product.productId)
                    ) {
                      accumulator["productId"].push(product.productId ?? "");
                    }

                    accumulator["productUOMId"] =
                      accumulator["productUOMId"] || [];
                    if (
                      product.productUOMId &&
                      !uomMap.has(product.productUOMId) &&
                      !accumulator["productUOMId"].includes(
                        product.productUOMId
                      )
                    ) {
                      accumulator["productUOMId"].push(
                        product.productUOMId ?? ""
                      );
                    }

                    return acc;
                  },
                  {}
                );

                return accumulator;
              },
              {}
            );

            // getCompany(objectMap["companyId"]);
            getOutlets(objectMap["outletId"]);
            getProduct(objectMap["productId"]);
            getUOM(objectMap["productUOMId"]);
            getShippingAddress(objectMap["shippingAddressId"]);
            const staff = await getStaff(objectMap["staffId"]);
            const retailer = await getRetailer(objectMap["retailerId"]);
            // getCompanyBranch(objectMap["companyBranchId"]);

            // create new key for display name and applicant type
            data.map((item: SalesOrder) => {
              if (item.staffId && item.staffId !== "") {
                item.applicantNameNType = `Staff | ${staff.get(item.staffId)?.lastName ?? ""
                  } ${staff.get(item.staffId)?.firstName ?? ""}`;
              }

              if (item.retailerId && item.retailerId !== "") {
                item.applicantNameNType = `Retailer | ${retailer.get(item.retailerId)?.lastName ?? ""
                  } ${retailer.get(item.retailerId)?.firstName ?? ""}`;
              }

              return item;
            });

            const nextCursor = res.cursor; // Get the cursor from the last item in the response
            if (nextCursor !== cursor || isRefresh) {
              // Avoid duplicates
              if (!isRefresh) {
                // setFullData((prevData) => [...prevData, ...data]);
                setData((prevData) => [...prevData, ...data]);
                setTableLoading(false);
                setShowButtonLoader(false); //path to check
              } else {
                // setFullData([...data]);
                setData([...data]);
                setTableLoading(false);
                setShowButtonLoader(false); //path to check
              }
              // cursor = nextCursor;
              setCursor(nextCursor);
              setTempCursor(nextCursor);
            }
            isLoading = false;
          }
        })
        .catch(() => {
          isLoading = false;
          //* This Part need re-edit*//
        });
      // } else {
      //   setGoodsReturnListingInfoData([]);
      // }
    }, 500);
  };

  // const getCompany = async (id: string[] = []) => {
  //   const dataSource = new DataSource(
  //     "companies",
  //     encodeParams({ id: id }),
  //     false
  //   );
  //   dataSource
  //     .load()
  //     .then((res: any) => {
  //       if (res !== null && res.items.length > 0) {
  //         setCompanyMap((prevDataMap) => {
  //           const newDataMap = new Map(prevDataMap);
  //           res.items.forEach((item: CompanyGeneralInfo) => {
  //             if (!newDataMap.has(item.id)) {
  //               newDataMap.set(item.id, item);
  //             }
  //           });
  //           return newDataMap;
  //         });
  //       }
  //     })
  //     .catch(() => {
  //       //* This Part need re-edit*//
  //     });
  // };

  const getOutlets = async (id: string[] = []) => {
    let tempProductMap = new Map(outletMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "outlets",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });
        if (res !== null && res.items.length > 0) {
          setOutletMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Outlet) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Outlet) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getProduct = async (id: string[] = []) => {
    let tempProductMap = new Map(productMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      //   status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "productCatalogues",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setProductMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Product) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Product) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getUOM = async (id: string[] = []) => {
    let tempProductMap = new Map(uomMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource("uoms", encodeParams(params), false);
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setUomMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: UOM) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: UOM) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getShippingAddress = async (id: string[] = []) => {
    let tempProductMap = new Map(shippingAddressMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "shippingAddresses",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setShippingAddressMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: OutletShippingAddress) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: OutletShippingAddress) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getStaff = async (id: string[] = []) => {
    let tempStaffMap = new Map(staffMap);
    if (!id?.length) return tempStaffMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "staffs",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setStaffMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Staff) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Staff) => {
            tempStaffMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempStaffMap;
    }
    return tempStaffMap;
  };

  const getRetailer = async (id: string[] = []) => {
    let tempRetailerMap = new Map(retailerMap);
    if (!id?.length) return tempRetailerMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "retailers",
          encodeParams(params),
          false,
          "v2"
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setRetailerMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Retailer) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Retailer) => {
            tempRetailerMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempRetailerMap;
    }
    return tempRetailerMap;
  };

  const getOutletData = async (id: string[] = []) => {
    if (!id || id.length === 0) return;

    const dataSource = new DataSource(
      "outlets",
      encodeParams({ id: id }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setOutletInfo(res.items?.[0]);
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  // get configureSetting  
  const getConfigureSetting = async (companyId: string = "") => {
    let params: any = {
      companyId: companyId,
      name: `RetailerWebsiteCheckoutSetting-${retailerAccess?.companyCode}`,
      status: "ACTIVE"
    }

    const response: any = await apiHelper.GET(`configurableFields?${encodeParams(params)}`)
    if (response?.items?.length) {
      const items: ConfigurableField = response.items[0];
      if (items) {
        setCheckoutSetting(items)
        return items
      }
    }

    return {}
  }

  // submit CPO
  const submitCPOBulk = async (data: Cpos[]) => {
    const batchSize = 10;
    const totalEntries = data.length;
    const numBatches = Math.ceil(totalEntries / batchSize);
    let successfulCount = 0; // Variable to track successful API calls
    try {
      for (let i = 0; i < numBatches; i++) {
        const startIdx = i * batchSize;
        const endIdx = (i + 1) * batchSize;
        const batchData = data.slice(startIdx, endIdx);

        const coverWithKey = {
          cpos: batchData,
        };

        await apiHelper.POST("cpo/retailer", coverWithKey, "", "v2");
        successfulCount += batchData.length; // Increment successful count
        let precent = (successfulCount / totalEntries) * 100;

        setPrecent(Math.ceil(precent));
      }
      setIsUpload(false);
      MessageSuccessUI(t("SalesOrder.createSuccess"));
    } catch (err) {
      setIsUpload(false);
      MessageErrorUI(t("SalesOrder.createUnsuccess"));
    }

    getSalesOrder(true);
    setMyData([]);
    setNumber(0);
    setPrecent(0);
    MessageSuccessUI(successfulCount + " " + t("Excel.excelDoneCreate"));
  };

  // *************************************************************************************
  // *** EXCEL FILE
  // *************************************************************************************
  //bulk update Modal
  const bulkUpdateModal = () => {
    ModalConfirmUI({
      title: "Import Sales Order Data by Excel",
      content: (
        <div className="flex flex-col justify-center items-center gap-y-2">
          <p>
            Please ensure everytime upload CPO excel file, must be using the
            "excel sample" generated, else data will not be able to read.
          </p>
          <Row className="mt-3">
            <Upload
              onChange={(info) =>
                handleFileChange(info, excelDataSource, pathName)
              }
              showUploadList={{
                showPreviewIcon: false,
              }}
              className="uploaderRoutePlan"
              beforeUpload={(file) => {
                if (
                  file.type !==
                  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" &&
                  file.type !== "application/vnd.ms-excel"
                ) {
                  MessageErrorUI(
                    `${file.name} is an invalid file format. Please change the file extension to .xlsx or .xls`
                  );
                  return Upload.LIST_IGNORE;
                } else if (file.size > 5242880) {
                  MessageErrorUI(
                    `${file.name} is too large. Please upload another document that is smaller than 5MB.`
                  );
                  return Upload.LIST_IGNORE;
                } else {
                  return false;
                }
              }}
              maxCount={1}
            >
              <div className="flex items-center gap-x-4 text-[14px] p-2 border-dashed border-2 text-brightBlue500 bg-lightWhite font-semibold">
                <PlusOutlined />
                <p>{t("UploadExcel")}</p>
              </div>
            </Upload>
          </Row>
          <Row>
            <Button
              // icon={<BulbFilled className="text-yellow-400" />}
              className="flex flex-1 p-0 text-[11px]"
              type="link"
              onClick={() => {
                exportExcelFunction({
                  fileName: pathName,
                  dataSource: excelDataSource,
                  isProtect: "AUTO",
                  isDynamicColumn: true,
                  isAutoFilterOn: true,
                });
              }}
            >
              {/* <span className="pl-[8px]"> </span> */}
              {t("Excel.excelSample")}
            </Button>
          </Row>
        </div>
      ),
      okText: "Yes",
      cancelText: "No",
      onOk: () => {
        setNumber(number + 1);
      },
      onCancel: () => {
        // isModalVisible = false;
        setMyData([]);
      },
    });
  };

  const handleFileChange = (
    info: any,
    dataSource: { [key: string]: any[] },
    pathName: string
  ) => {
    const file = info.file;
    const reader = new FileReader();
    if (file) {
      reader.onload = async (event: any) => {
        const data: ArrayBuffer | null = event.target
          ? event.target.result
          : null;
        if (data) {
          // check if excel uploaded is empty
          // if (isEmptyObject(dataSource)) {
          // MessageErrorUI(t("ErrExcelSheetorDataNotFound"));
          //     return;
          // }

          const workbook = XLSX.read(data, { type: "array" });
          // use to store data read from excel
          let jsonData: { [key: string]: any[] } = {};
          let keyType: { [key: string]: any[] } = {};

          // use to store data after processed
          let modifiedData: { [key: string]: any[] } = {};
          let errorData: { [key: string]: any } = {};

          // determine whether remap needed
          let isMappingNeeded: boolean = false;
          let errorMappingCode: string[] = [];

          // Convert data from Excel to JSON format
          const sheetNames = Object.keys(dataSource);
          if (
            sheetNames &&
            sheetNames.length > 0 &&
            pathName !== sheetNames[1]
          ) {
            MessageErrorUI(t("ErrExcelSheetorDataNotFound"));
          }

          // Pump in to respective variables
          for (let i = 0; i < sheetNames.length; i++) {
            const sheetName = sheetNames[i];
            const worksheet = workbook.Sheets[sheetName];
            // Skip protected sheet
            if (
              sheetName.includes("ReadMe") ||
              sheetName.includes("SelectionOptions") ||
              sheetName.includes("productList(P!)")
            )
              continue;

            // Special handle for keys conversion
            if (sheetName.includes("keyType") && worksheet) {
              const keyTypeJson: any[] = XLSX.utils.sheet_to_json(worksheet);
              for (let j = 0; j < keyTypeJson.length; j++) {
                const row = keyTypeJson[j];
                for (const key in row) {
                  if (Object.prototype.hasOwnProperty.call(row, key)) {
                    const value = row[key];
                    keyType[key] = keyType[key]
                      ? [...keyType[key], value]
                      : [value];
                  }
                }
              }
            } else if (worksheet) {
              jsonData[sheetName] = XLSX.utils.sheet_to_json(worksheet);
            }
          }

          // Remap the data type to pre-defined type
          if (!isEmptyObject(jsonData) && !isEmptyObject(keyType)) {
            for (const [sheetName, rows] of Object.entries(jsonData)) {
              for (let val = 0; val < rows.length; val++) {
                const rowData = rows[val];
                // assigning index for better display when in error
                rowData.indexNo = val + 1;

                Object.keys(keyType).forEach((key) => {
                  switch (key) {
                    case "string":
                      // Convert fields to string
                      convertFieldsToString(rowData, keyType["string"]);
                      break;

                    case "number":
                      // Convert fields to number
                      convertFieldsToNumber(rowData, keyType["number"]);
                      break;

                    case "time":
                      // Convert fields to time
                      convertFieldsToTime(rowData, keyType["time"]);
                      break;

                    case `${sheetName}(RP!)`:
                      // Check which required keys are missing or invalid
                      validateRequiredFields(sheetName, rowData, keyType);
                      break;

                    default:
                      validateMissingOrInvalidFields(rowData);
                      // No action for unknown keys
                      break;
                  }
                });

                // Check if data mapping is require
                if ("newKeyMapping" in rowData) {
                  isMappingNeeded = true;
                }

                // Handling the productList option which if available for the outlet
                if (sheetName === "productOrdered") {
                  const productIndex = productList.findIndex(
                    (val) => val.productSKU === rowData.productSKU
                  );

                  if (productIndex < 0) {
                    rowData.hasAllRequiredFields = false;

                    const existingMissingFields = rowData?.missingFields ?? [];
                    rowData.missingFields = Array.from(
                      new Set([
                        ...existingMissingFields,
                        "productSKU are invalid.",
                      ])
                    );
                  }
                }

                // Handling the alternativeShippingAddress option which if available for the outlet
                if (sheetName === "alternativeShippingAddress") {
                  // const stateSelected = selectOptions.filter((val) => val.stateCode === rowData.stateCode)

                  // if (stateSelected.length === 0) {
                  //   rowData.hasAllRequiredFields = false

                  //   const existingMissingFields = rowData?.missingFields ?? [];
                  //   rowData.missingFields = Array.from(new Set([...existingMissingFields, "stateCode are invalid."]));
                  // } else {
                  // rowData.state = stateSelected[0].stateName

                  let addressDetailsFrmAPI: any = [];
                  if (rowData?.address && rowData?.address !== "-") {
                    addressDetailsFrmAPI = await getCoordinateFromAddress(
                      rowData.address
                    );
                  }

                  if (addressDetailsFrmAPI && addressDetailsFrmAPI?.length) {
                    const addressDetails = addressDetailsFrmAPI?.[0];
                    rowData.shippingLongitude =
                      addressDetails?.longitude?.toString();
                    rowData.shippingLatitude =
                      addressDetails?.latitude?.toString();
                    rowData.address1 = addressDetails?.address?.address1;
                    rowData.address2 = addressDetails?.address?.address2;
                    rowData.unitNo = addressDetails?.address?.unitNo;
                    rowData.city = addressDetails?.address?.city;
                    rowData.postalCode = addressDetails?.address?.postalCode;
                    rowData.state = addressDetails?.address?.state;
                    rowData.stateCode = selectOptions.find(
                      (item) =>
                        item.stateName ===
                        addressDetails?.address?.state?.toLowerCase()
                    )?.stateCode;
                    rowData.country = addressDetails?.address?.country;

                    // hardcode malaysia
                    // rowData.country = "MALAYSIA"
                    rowData.countryCode = "MYS";
                  } else if (rowData?.address === "-") {
                    rowData.shippingLongitude = "";
                    rowData.shippingLatitude = "";
                    rowData.address1 = "";
                    rowData.address2 = "";
                    rowData.unitNo = "";
                    rowData.city = "";
                    rowData.postalCode = "";
                    rowData.state = "";
                    rowData.stateCode = "";
                    rowData.country = "";
                  } else {
                    rowData.hasAllRequiredFields = false;

                    const existingMissingFields = rowData?.missingFields ?? [];
                    rowData.missingFields = Array.from(
                      new Set([
                        ...existingMissingFields,
                        "address are invalid.",
                      ])
                    );
                  }
                  // }
                }

                if (
                  !rowData.hasAllRequiredFields ||
                  (rowData?.newKeyMapping &&
                    errorMappingCode.includes(rowData?.newKeyMapping))
                ) {
                  errorData[`${sheetName}`] = errorData[`${sheetName}`]
                    ? [...errorData[`${sheetName}`], rowData]
                    : [rowData];

                  // Add all mappingCode which related to error data
                  if (isMappingNeeded) {
                    const existingErrMappingCode = Array.from(
                      new Set([...errorMappingCode, rowData?.newKeyMapping])
                    );
                    errorMappingCode = existingErrMappingCode;
                  }
                } else {
                  modifiedData[`${sheetName}`] = modifiedData[`${sheetName}`]
                    ? [...modifiedData[`${sheetName}`], rowData]
                    : [rowData];
                }
              }
            }

            // Recheck on whether there related data from errMappingKey
            if (isMappingNeeded) {
              recheckError(modifiedData, errorMappingCode);
              for (const [sheetName, rows] of Object.entries(modifiedData)) {
                for (let val = 0; val < rows.length; val++) {
                  const rowData = rows[val];
                  if (
                    !rowData.hasAllRequiredFields ||
                    (rowData?.newKeyMapping &&
                      errorMappingCode.includes(rowData?.newKeyMapping))
                  ) {
                    errorData[`${sheetName}`] = errorData[`${sheetName}`]
                      ? [...errorData[`${sheetName}`], rowData]
                      : [rowData];
                    modifiedData[sheetName].splice(val, 1);
                  }
                }
              }
            }
          }
          // remap undergoing from here
          let expectedResult: any = [];
          if (isMappingNeeded) {
            expectedResult = transformDataDynamic(
              modifiedData,
              keyType[`layering`]
            );
          }
          for (let i = 0; i < expectedResult.length; i++) {
            expectedResult[i].estimatedDeliveredAt =
              expectedResult[i].estimatedDeliveredDate;
            expectedResult[i].retailerCode = retailerAccess?.retailerRefCode;
            expectedResult[i].retailerId = retailerAccess?.id;
            expectedResult[i].outletCode = outletInfo?.outletCode;
            expectedResult[i].outletId = outletInfo?.id;
            expectedResult[i].companyCode = outletInfo?.companyCode;
            expectedResult[i].companyId = outletInfo?.companyId;
            expectedResult[i].shippingCode =
              outletInfo?.shippingCode && outletInfo?.shippingCode !== "-"
                ? outletInfo?.shippingCode
                : "";

            if (
              !("productOrdered" in expectedResult[i]) ||
              !("alternativeShippingAddress" in expectedResult[i])
            ) {
              const missingFields = [];
              if (!("productOrdered" in expectedResult[i])) {
                missingFields.push("productOrdered");
              }

              if (!("alternativeShippingAddress" in expectedResult[i])) {
                missingFields.push("alternativeShippingAddress");
              }

              const sheetField =
                missingFields.length > 0 ? missingFields.join(" and ") : "";
              errorData["mappingCode"] = errorData["mappingCode"]
                ? [
                  ...errorData["mappingCode"],
                  {
                    note: `Missing or Invalid newKeyMapping in ${sheetField} sheet(s) for line ${i + 1
                      }`,
                  },
                ]
                : [
                  {
                    note: `Missing or Invalid newKeyMapping in ${sheetField} sheet(s) for line ${i + 1
                      }`,
                  },
                ];
            }
          }

          // handling if error data exist, then export in excel
          if (errorData && Object.keys(errorData).length) {
            let errorDataDisplay: { [key: string]: any } = {};
            for (const [sheetName, data] of Object.entries(errorData)) {
              // process line by line to read the error data
              for (let i = 0; i < data.length; i++) {
                if (data[i].missingFields && data[i].missingFields.length > 0) {
                  const missingFields = {
                    note:
                      `Missing or Invalid field(s) for data line ${data[i]?.indexNo} included: ` +
                      data[i].missingFields.join(", "),
                  };
                  // If no entry exists for the sheetName, initialize it
                  if (!errorDataDisplay[sheetName]) {
                    errorDataDisplay[sheetName] = [];
                    errorDataDisplay[sheetName] = [missingFields];
                  } else {
                    // Append missingFields to the existing entry
                    errorDataDisplay[sheetName] = [
                      ...errorDataDisplay[sheetName],
                      missingFields,
                    ];
                  }
                }

                if (sheetName === "mappingCode") {
                  errorDataDisplay[sheetName] = errorData["mappingCode"];
                }
              }
            }
            ModalConfirmUI({
              title: t("Excel.errorDataInserted"),
              content: t("Excel.printErrorExcel"),
              okText: t("Excel.yes"),
              cancelText: t("Excel.no"),
              onOk: () => {
                // Export excel data
                exportExcelFunction({
                  fileName: "ErrorData",
                  dataSource: errorDataDisplay,
                  isProtect: "AUTO",
                  isDynamicColumn: false,
                  isAutoFilterOn: false,
                });
              },
              onCancel: () => { },
            });
          } else {
            // upsertExcelData(expectedResult, "cpo/retailer", "v2")
            setMyData(expectedResult);
          }
        }
      };
      reader && reader.readAsArrayBuffer(file);
    }
  };

  // const getProductData = async () => {
  //   const dataSource = new DataSource(
  //     "productCatalogues",
  //     "status=ACTIVE",
  //     true
  //   );
  //   const res: any = await dataSource.load();

  //   if (res !== null) {
  //     let nameList: SelectOption[] = [];
  //     let skuList: SelectOption[] = [];
  //     res.map((value: any) => {
  //       nameList.push({
  //         value: value.id,
  //         label: value.name,
  //       });
  //       skuList.push({
  //         value: value.id,
  //         label: value.sku,
  //       });
  //     });
  //     setProductNameOption(nameList);
  //     setProductSkuOption(skuList);
  //   }
  // };

  const column = [
    {
      width: 150,
      title: t("SalesOrder.applicantName"),
      dataIndex: "applicantNameNType",
      // onFilter: (value: string, record: any) =>
      //   record.applicantNameNType.indexOf(value) === 0,
      sorter: (a: any, b: any) =>
        a?.applicantNameNType?.localeCompare(b?.applicantNameNType),
      showSorterTooltip: false,
      key: "applicantNameNType",
      render: (_: any, record: SalesOrder) => {
        return (
          <p className="tableRowNameDesign">
            {record.applicantNameNType ? (
              record.applicantNameNType
            ) : (
              <p className="tableRowNameDesign text-center">—</p>
            )}{" "}
          </p>
        );
      },
    },
    {
      width: 120,
      title: t("SalesOrder.salesOrder") + " " + t("Common.no"),
      dataIndex: "salesOrderNo",
      onFilter: (value: string, record: any) =>
        record.salesOrderNo.indexOf(value) === 0,
      sorter: (a: any, b: any) => a.salesOrderNo.localeCompare(b.salesOrderNo),
      showSorterTooltip: false,
      key: "salesOrderNo",
      render: (_: any, record: SalesOrder) => {
        return <p className="tableRowNameDesign">{record.salesOrderNo}</p>;
      },
    },
    {
      width: 120,
      title: t("SalesOrder.orderDate"),
      dataIndex: "createdAt",
      onFilter: (value: string, record: any) =>
        record.createdAt.indexOf(value) === 0,
      sorter: (a: any, b: any) => a.createdAt.localeCompare(b.createdAt),
      showSorterTooltip: false,
      key: "createdAt",
      render: (_: any, record: SalesOrder) => {
        return (
          <p className="tableRowNameDesign">
            {moment(record.createdAt).format("DD/MM/YYYY h:mm A")}
          </p>
        );
      },
    },
    // {
    //   width: 120,
    //   title: t("OutletCode"),
    //   dataIndex: "outletCode",
    //   onFilter: (value: string, record: any) => record.outletCode.indexOf(value) === 0,
    //   sorter: (a: any, b: any) => {
    //     const first = outletMap.get(a.outletId)?.outletCode || "";
    //     const second = outletMap.get(b.outletId)?.outletCode || "";

    //     return first?.localeCompare(second);
    //   },
    //   showSorterTooltip: false,
    //   key: "outletCode",
    //   render: (_: any, record: SalesOrder) => {
    //     return <p className="tableRowNameDesign">{outletMap.get(record.outletId)?.outletCode}</p>;
    //   },
    // },
    // {
    //   width: 120,
    //   title: t("Outlet"),
    //   dataIndex: "outletId",
    //   onFilter: (value: string, record: any) => record.outletId.indexOf(value) === 0,
    //   sorter: (a: any, b: any) => {
    //     const first = outletMap.get(a.outletId)?.name || "";
    //     const second = outletMap.get(b.outletId)?.name || "";

    //     return first?.localeCompare(second);
    //   },
    //   showSorterTooltip: false,
    //   key: "outletId",
    //   render: (_: any, record: SalesOrder) => {
    //     return <p className="tableRowNameDesign">{outletMap.get(record.outletId)?.name}</p>;
    //   },
    // },
    // {
    //   width: 120,
    //   title: t("CPO"),
    //   dataIndex: "cpo",
    //   onFilter: (value: string, record: any) => record.cpo.indexOf(value) === 0,
    //   sorter: (a: any, b: any) => a.cpo.localeCompare(b.cpo),
    //   showSorterTooltip: false,
    //   key: "cpo",
    //   render: (_: any, record: SalesOrder) => {
    //     return <p className="tableRowNameDesign">{record.cpo}</p>;
    //   },
    // },
    {
      width: 120,
      title: t("SalesOrder.estimatedDeliveryDate"),
      dataIndex: "estimatedDeliveredAt",
      onFilter: (value: string, record: any) =>
        record.estimatedDeliveredAt.indexOf(value) === 0,
      sorter: (a: any, b: any) =>
        a.estimatedDeliveredAt.localeCompare(b.estimatedDeliveredAt),
      showSorterTooltip: false,
      key: "estimatedDeliveredAt",
      render: (_: any, record: SalesOrder) => {
        return (
          <p className="tableRowNameDesign">
            {moment(record.estimatedDeliveredAt).format("DD/MM/YYYY")}
          </p>
        );
      },
    },
    {
      width: 120,
      title: t("SalesOrder.creditLimit"),
      dataIndex: "creditLimit",
      onFilter: (value: string, record: any) =>
        record.creditLimit.indexOf(value) === 0,
      sorter: (a: any, b: any) => a.creditLimit - b.creditLimit,
      showSorterTooltip: false,
      key: "creditLimit",
      render: (_: any, record: SalesOrder) => {
        let data = outletMap.get(record.outletId)?.creditLimit;
        let formatted = NumberThousandSeparator(data as number);
        return <p className="tableRowNameDesign">{formatted}</p>;
      },
    },
    {
      width: 120,
      title: t("SalesOrder.outstanding"),
      dataIndex: "outstanding",
      onFilter: (value: string, record: any) =>
        record.outstanding.indexOf(value) === 0,
      sorter: (a: any, b: any) => a.outstanding - b.outstanding,
      showSorterTooltip: false,
      key: "outstanding",
      render: (_: any, record: SalesOrder) => {
        return (
          <p className="tableRowNameDesign">
            {outletMap.get(record.outletId)?.outstandingPayment}
          </p>
        );
      },
    },
    {
      title: t("Common.status"),
      dataIndex: "status",
      key: "status",
      onFilter: (value: string, record: any) =>
        record.status.indexOf(value) === 0,
      sorter: (a: any, b: any) => a.status.localeCompare(b.status),
      showSorterTooltip: false,
      width: 100,
      render: (_: any, record: any) => {
        if (record.status === "UNVERIFIED") {
          record.status = "SYSTEMPROCESSING";
        }
        return statusApproval(record);
      },
    },
    // {
    //   title: t("Common.approvalStatus"),
    //   dataIndex: "approvalStatus",
    //   key: "approvalStatus",
    //   onFilter: (value: string, record: any) =>
    //     record.approvalStatus.indexOf(value) === 0,
    //   sorter: (a: any, b: any) =>
    //     a.approvalStatus?.localeCompare(b.approvalStatus),
    //   showSorterTooltip: false,
    //   width: 100,
    //   render: (_: any, record: any) => {
    //     return statusApproval(record);
    //   },
    // },

    {
      // Action
      title: t("Common.action"),
      dataIndex: "action",
      key: "action",
      fixed: "right",
      width: 100,
      render: (_: any, record: SalesOrder) => {
        const product = record.productOrdered;
        let totals = 0;

        if (product) {
          product.map((item: ProductOrdered) => {
            if (item) {
              const subtotal = (item.quantity ?? 0) * (item?.price ?? 0);
              const taxAmount = subtotal * (item.taxRate ?? 0);
              const total = subtotal - (item.discount ?? 0) - taxAmount;
              totals = totals + total;
            }
          });
        }

        // const containsRorC = /[RU]/.test(userAccess?.policies?.["goodsReturn"] || "");
        // if ((containsRorC || isAdmin || isCompanyAdmin) && record.status !== "UNVERIFIED") {
        return (
          <div className="flex">
            <Button
              type="link"
              onClick={() =>
                router.push(`/salesOrder/orderDetail?id=${record.id}`)
              }
              className="flex items-center  text-xs ml-0 p-2"
            >
              <Tooltip title={t("Common.viewMore")}>
                <EyeOutlined style={{ color: "green" }} />
              </Tooltip>
            </Button>
          </div>
        );
        // }
        // return null;
      },
    },
  ];

  const rowSelection = {
    fixed: true, // This will fix the checkbox column to the left
    onChange: (selectedRowKeys: string[], selectedRows: []) => {
      setSelectedRowData(selectedRows);
      setSelectedRowKeys(selectedRowKeys);
    },

    getCheckboxProps: (record: any) => {
      if (
        record?.netAmount - record?.paidAmount - record?.processingAmount ===
        0
      ) {
        return {
          disabled: true,
        };
      }
    },
  };

  const generatePDF = async () => {
    setPDFLoading(true);

    // Filter out data that is out of date
    // const dateOverdue = new Date("2023-10-01T00:00:00.000Z");

    // const filteredData = selectedRowData.filter((item) => {
    //   const createdAtDate = new Date(item.createdAt);
    //   return createdAtDate >= dateOverdue && (item.eInvoiceSubmissionType === "CONSOLIDATED" || item.eInvoiceSubmissionType === "" || item.eInvoiceSubmissionType === "NOTREQUIRED");
    // });

    // if (filteredData.length === 0) {
    //   MessageInfoUI(t("PDFPrintMessage"));
    //   setLoading(false);
    //   return;
    // }

    try {
      const pdfDoc = await PDFDocument.create();

      for (const invoiceData of selectedRowData) {
        let donorPdfBytes;

        // if (invoiceData.invoiceDocument !== "") {
        // const res: any = await PicSignedUrl(invoiceData.invoiceDocument!);
        //   const pdfBytesResponse = await fetch(res);
        //   donorPdfBytes = await pdfBytesResponse.arrayBuffer();
        // } else {
        const res: any = await apiHelper.GET(
          "salesOrder/pdf?includeOriginals=TRUE&id=" + invoiceData.id
        );
        const documentSigned: any = await PicSignedUrl(res.item); //need to remove after confirmed
        const pdfBytesResponse = await fetch(documentSigned);
        donorPdfBytes = await pdfBytesResponse.arrayBuffer();
        // }

        const donorPdfDoc = await PDFDocument.load(donorPdfBytes);

        // Copy all pages from donorPdfDoc to pdfDoc
        const donorPages = await pdfDoc.copyPages(
          donorPdfDoc,
          donorPdfDoc.getPageIndices()
        );
        donorPages.forEach((page: any) => pdfDoc.addPage(page));
      }

      // Convert the merged PDF to a blob
      const mergedPdfBytes = await pdfDoc.save();

      // Create a blob URL and open it in a new tab
      const blob = new Blob([mergedPdfBytes], { type: "application/pdf" });
      const blobUrl = URL.createObjectURL(blob);
      window.open(blobUrl, "_blank");
    } catch (error) {
      console.error(error);
      setPDFLoading(false);
    }

    setPDFLoading(false);
    if (selectedRowData.length > 0) {
      MessageSuccessUI(
        selectedRowData.length + " " + t("PDFPrintMessageSuccess")
      );
    }
  };

  // const buttons = [
  //   {
  //     label: t("SalesOrder.printSalesOrder"),
  //     onClick: generatePDF,
  //     disabled: !(selectedRowData.length > 0),
  //   },
  // ];

  // *************************************************************************************
  // *** Filter Modal ***
  // *************************************************************************************

  const filterFormOnfinish = (values: any) => {
    setModalFilter(values);
  };

  const searchSalesOrder = (values: any) => {
    //convert to empty string when no value is entered as default value is undefined.
    for (const key in values) {
      if (values[key] === undefined) {
        values[key] = "";
      }
    }

    // return true or false
    let isAnyKeyFilled = Object.keys(values).some(
      (key) => values[key] !== "" && values[key] !== undefined
    );

    const orderDate = values.createdDate
      ? values.createdDate.format("YYYY-MM-DDT00:00:00") + "Z"
      : "";

    let currentOutletId = localStorage.getItem("currentOutletId");

    const params =
      encodeParams({
        companyId: retailerAccess.companyId,
        // companyBranchId: retailerAccess.companyBranchId,
        outletId: currentOutletId,
        fuzzySearch: values.fuzzySearch,
        salesOrderNo: values.salesOrderNo || "",
        createdAt: orderDate || "",
        productId:
          values.productSku && values.productName
            ? [values.productSku, values.productName]
            : values.productSku || values.productName || "",
        status: values.status,
      }) + "&sort=createdAt&sortOrder=-1";

    if (isAnyKeyFilled) {
      setCursor("0");
      setFilterSetting(params);
    }
  };

  const handleStatusMenuClick: MenuProps["onClick"] = ({ key }) => {
    const items = statusFilterOption1;
    setStatusKey(key);
    // Access the label property of the selected item
    const selectedLabel = items.find(
      (menuItem: any) => menuItem.key === key
    )?.label;
    if (selectedLabel) {
      setStatusValue(selectedLabel);
    }
  };

  const filterModal = () => {
    return (
      <div className="w-full">
        <Form
          onFinish={filterFormOnfinish}
          form={filterModalForm}
          className=""
          layout="vertical"
        >
          <h1 className="font-bold text-base pb-4 hidden sm:flex">
            {t("Filter")}
          </h1>
          {/* First Row of Filter Input */}
          <Row className="filterBlockForm flex-col space-y-3">
            <Row className="flex sm:flex-row flex-col gap-x-4 sm:gap-y-0 gap-y-4">
              <Form.Item
                name="salesOrderNo"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("SalesOrder.salesOrder") +
                      " " +
                      t("Common.no") +
                      "?"}
                  </p>
                }
              >
                <FormTextInput
                  placeholder={
                    t("Common.eg") +
                    " " +
                    t("SalesOrder.salesOrder") +
                    " " +
                    t("Common.no")
                  }
                  maxLength={30}
                />
              </Form.Item>
              <Form.Item
                name="createdDate"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("SalesOrder.orderDate") +
                      "?"}
                  </p>
                }
              >
                <SingleDateInput
                  placeholder={t("Common.eg") + " " + t("SalesOrder.orderDate")}
                  onChange={() => {
                    filterModalForm.submit();
                  }}
                />
              </Form.Item>
            </Row>
            <Row className="flex sm:flex-row flex-col gap-x-4 sm:gap-y-0 gap-y-4">
              <Form.Item
                name="productSku"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("SalesOrder.productSKU") +
                      "?"}
                  </p>
                }
              >
                <ComponentFilterSelect
                  placeholder={
                    t("Common.eg") + " " + t("SalesOrder.productSKU")
                  }
                  dbName={"productCatalogues"}
                  customParams={{ status: "ACTIVE" }}
                  displayExpr={"sku"}
                  valueExpr={"id"}
                  userAccess={retailerAccess}
                />
              </Form.Item>
              <Form.Item
                name="productName"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("SalesOrder.productName") +
                      "?"}
                  </p>
                }
              >
                <ComponentFilterSelect
                  placeholder={
                    t("Common.eg") + " " + t("SalesOrder.productName")
                  }
                  dbName={"productCatalogues"}
                  customParams={{ status: "ACTIVE" }}
                  displayExpr={"name"}
                  valueExpr={"id"}
                  userAccess={retailerAccess}
                />
              </Form.Item>
            </Row>
          </Row>
          <Row className="flex pt-8 justify-between">
            <PrimaryButtonUI
              label={t("Common.resetAll")}
              onClick={() => {
                filterModalForm.resetFields();
                // setParamsFromLocalStorage(router.pathname, `status=${statusKey}`, "productFilter");
              }}
            />
            {/* <Row> */}
            <SecondaryButtonUI
              label={t("Common.cancel")}
              htmlType="reset"
              onClick={() => {
                setModalFilter({});
                setIsFilterModalOpen(false);
              }}
            />
            <PrimaryButtonUI
              label={t("Common.applyFilter")}
              htmlType="submit"
              onClick={() => {
                setIsFilterModalOpen(false);
              }}
            />
            {/* </Row> */}
          </Row>
        </Form>
      </div>
    );
  };

  const buttons = [
    ...(outletInfo?.isRetailerCPOEnabled === "TRUE"
      ? [
        {
          label:
            outletInfo?.isRetailerCPOEnabled === "TRUE"
              ? t("SalesOrder.uploadCPO")
              : "",
          // onClick: () => {
          //   t && (bulkUpdateModal(x, pathName, t))
          // },
          // onClick: bulkUpdateModal,
          onClick: async () => {
            const configureSettingIsOn: any = await getConfigureSetting(retailerAccess?.companyId ?? "")

            configureSettingIsOn && Object.keys(configureSettingIsOn).length ?
              // modal display where holiday
              ModalInfoUI({
                title: t("Notice"),
                content:
                  <div
                    dangerouslySetInnerHTML={{ __html: configureSettingIsOn.value }}
                  />
                ,
                okText: t("Common.ok"),
                cancelText: t("Common.cancel"),
                onOk: () => { },
                onCancel: () => { },
              }) : bulkUpdateModal();
          },
          buttonColor: "excelButtonBg",
          loading: pdfLoading,
        },
      ]
      : []),
    {
      label: t("SalesOrder.printSalesOrder"),
      onClick: generatePDF,
      disabled: !(selectedRowData.length > 0),
      loading: pdfLoading,
    },
  ].filter(Boolean);

  const showContent = () => {
    return (
      <div>
        <BackButtonUI
          buttons={buttons}
          title={t("SalesOrder.salesOrder")}
        ></BackButtonUI>
        <Row className="mb-4 w-full">
          <FilterFormComponent
            filterForm={filterForm}
            onDebouncedChange={(value) => {
              if (value === "") {
                filterForm.resetFields();
                setStatusKey("ALL");
                setStatusValue("All");
                setFuzzySearchFilter("");
                setModalFilter({});
                filterModalForm.resetFields();
                // setData([...fullData]);
                setShowClearFilter(false);
                setCursor(tempCursor);
                setFilterSetting("");
                localStorage.removeItem("salesOrderFilter");
              } else {
                filterModalForm.resetFields();
                setModalFilter({});
                setFuzzySearchFilter(value);
              }
            }}
            fieldName={fieldName}
            clearButtonOnChange={() => {
              filterForm.resetFields();
              setStatusKey("ALL");
              setStatusValue("All");
              setFuzzySearchFilter("");
              setModalFilter({});
              filterModalForm.resetFields();
              // setData([...fullData]);
              setShowClearFilter(false);
              setCursor(tempCursor);
              setFilterSetting("");
              localStorage.removeItem("salesOrderFilter");
            }}
            filterModalButtonOnClick={() => {
              setIsFilterModalOpen(true);
              filterForm.resetFields();
              setFuzzySearchFilter("");
            }}
            modalFilterValue={modalFilter}
            option={SalesOrderFilterOption}
            handleStatusMenuClick={handleStatusMenuClick}
            clearFilterDisable={showClearFilter === true ? false : true}
            statusValue={statusValue}
            debounceValue={fuzzySearchFilter}
          ></FilterFormComponent>
        </Row>
        <ListingTableUI
          // EditableCell={EditableCell}
          bordered
          dataSource={data}
          columns={column}
          loader={showButtonLoader}
          loading={tableLoading}
          // rowClassName="editable-row"
          rowKey={(record: any) => record.id}
          cursor={cursor}
          // loader={showButtonLoader}
          pagination={false}
          rowSelection={rowSelection}
        />
      </div>
    );
  };

  const showUploading = () => (
    <Modal className="mt-24" closable={false} open={isUpload} footer={null}>
      <div className="h-160px flex justify-center items-center flex-col">
        <p className="font-bold text-xl mb-4">Uploading...</p>
        <Progress type="circle" percent={precent} />
      </div>
    </Modal>
  );

  return (
    <div className="flex flex-col w-full min-h-screen bg-bgOrange">
      <Header items={headerItems} hasSearch={false} values={() => { }} />
      <Content className="flex flex-col mt-3 w-full sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2">
        {showContent()}
        {showUploading()}
      </Content>
      <Row className="justify-center w-full pt-4">
        {showScrollButton && (
          <div className="fixed bottom-20 right-8 z-50">
            <button
              className={`flex items-center justify-center rounded-full w-10 h-10 text-white text-lg font-semibold focus:outline-none bg-blue-500 hover:bg-blue-600`}
              onClick={handleScrollToTop}
            >
              <ArrowUpOutlined style={{ fontSize: "24px" }} />
            </button>
          </div>
        )}
      </Row>
      {isSmallScreen ? (
        <Drawer
          title="Filter"
          placement="bottom"
          closable={false}
          onClose={() => setIsFilterModalOpen(false)}
          open={isFilterModalOpen}
          height="80vh"
          className="rounded-t-lg"
        >
          {filterModal()}
        </Drawer>
      ) : (
        <ModalUI
          // title={"More Filter"}
          width="70%"
          className={"modalFilterBody"}
          visible={isFilterModalOpen}
          onOk={() => setIsFilterModalOpen(false)}
          onCancel={() => setIsFilterModalOpen(false)}
          content={filterModal()}
          title={""}
        ></ModalUI>
      )}

      <AppFooter retailerAccessValues={retailerAccess} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default SalesOrderListing;
