import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import { Divider, List } from "antd";
import Header, { supportedLocales } from "../../components/header";
import { TopSideBar } from "@/components/sidebar";
import { ProductBlock } from "@/components/ui";
import noImage from "../../assets/sampleImage/NoImagePlaceholder.jpg";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import {
  CompanyGeneralInfo,
  Outlet,
  OutletProductList,
  OutletPromotion,
  PreOrderSetting,
  ProductCategories,
  ProductPriceGroup,
  ProductTradeInfo,
  Promotion,
  Retailer,
  TradeInfoAggregate,
  TradeInfoAggregateUI,
} from "@/components/type";
import { DataSource, PUBLIC_BUCKET_URL, encodeParams } from "@/stores/utilize";
import useRetailerStore from "@/stores/store";
import { getOutletData, getRetailerData } from "@/stores/authContext";
import moment from "moment";
import InfiniteScroll from "react-infinite-scroll-component";
import AppFooter from "@/components/footer";
import { cloneDeep } from "lodash";

function productCategory() {
  const { t } = useTranslation("common");
  const router = useRouter();
  const [outletInfo, setOutletInfo] = useState<Outlet>();
  // const [outletPromotionProductList, setOutletPromotionProductList] = useState<
  //   OutletPromotion[]
  // >([]);
  // const [allPromotionList, setAllPromotionList] = useState<Promotion[]>([]);
  // const [allProductList, setAllProductList] = useState<Product[]>([]);
  // const [allProductPriceGroup, setAllProductPriceGroup] = useState<
  //   ProductPriceGroup[]
  // >([]);
  // const [companyData, setCompanyData] = useState<CompanyGeneralInfo[]>([]);
  // const [productCategorySummary, setProductCategorySummary] = useState<ProductCategorySummary[]>([]);
  const [productCategoriesSecondLevel, setProductCategoriesSecondLevel] =
    useState<ProductCategories[]>([]);
  // const [selectedFilteredCategory, setSelectedCategory] = useState(null);
  const [inputValue, setInputValue] = useState("");
  const [filterValue, setFilterValue] = useState({
    tradeType: "",
    sellingPriceGT: 0,
    sellingPriceLT: 0,
  });
  // const [isDataReady, setIsDataReady] = useState(false);
  // const [currentPage, setCurrentPage] = useState(1);
  // const [paginatedData, setPaginatedData] = useState<Product[]>([]);
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({}); //access control
  const [activeAt, setActiveAt] = useState<string>(moment().startOf("day").add(1, "millisecond").toISOString());

  const [productMap, setProductMap] = useState(new Map());
  const [companyMap, setCompanyMap] = useState(new Map());
  // const [productPriceMap, setProductPriceMap] = useState(new Map());
  // const [productCatalogueMap, setProductCatalogueMap] = useState(new Map());
  // const [pageSize, setPageSize] = useState(50); // Default page size is 50
  // const [outletProductListId, setOutletProductListId] = useState<string[]>([]);
  const [data, setData] = useState<TradeInfoAggregateUI[]>([]);
  const [cursor, setCursor] = useState("0");
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  // const [imageIndexingMap, setImageIndexingMap] = useState(new Map());

  const passedRouter = useRouter();
  const queryInfo = passedRouter.query;
  const productCategoryId = queryInfo.categoryId || "";

  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    if (router.isReady && Object.keys(useRetailerStore.getState()).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }

      // get outlet info to check autoCheckout eligibility
      const outletNameData: any = useRetailerStore.getState().outletNameData || {}
      if (!Object.keys(outletNameData).length) {
        getOutletData().then((value: any) => {
          setActiveAt(value?.nextQualifiedDate ?? moment().startOf("day").add(1, "millisecond").toISOString())
        })
      } else {
        setActiveAt(outletNameData?.nextQualifiedDate ?? moment().startOf("day").add(1, "millisecond").toISOString())
      }
    }
  }, [router.isReady, Object.keys(useRetailerStore.getState()).length]);

  useEffect(() => {
  }, [activeAt])

  useEffect(() => {
    if (retailerAccess && Object.keys(retailerAccess).length > 0) {
      if (localStorage.getItem("products")) {
        let localStorageProduct: any = localStorage.getItem("products");
        setInputValue(decodeURI(localStorageProduct));
        // const { pathname, query } = router;
        // const updatedQuery = { ...query, product: "" };
        // router.replace({ pathname, query: updatedQuery }, undefined, {
        //   // shallow: true,
        // });
      }

      if (localStorage.getItem("stockType")) {
        let localStorageStockType: any = localStorage.getItem("stockType");
        setFilterValue((prevValue) => ({
          ...prevValue,
          tradeType: localStorageStockType,
        }));
        localStorage.removeItem("stockType");
      }
      // const fetchData = async () => {
      //   await getProductCategoriesSummary();
      // };
      // fetchData();
      getProductCategoriesSecondLevel();
      getOutletProduct();
    }
  }, [retailerAccess]);

  // useEffect(() => {
  //   if (productCategorySummary.length > 0) {
  //     getProductCategoriesSecondLevel(); // Call here after productCategorySummary is updated
  //   }
  // }, [productCategorySummary]);

  // useEffect(() => {
  //   if (productMap.size === 0 || paginatedData.length > 0) {
  //     setIsDataReady(true);
  //   }
  // }, [paginatedData, productMap]);

  // useEffect(() => {
  //   // when that two product has value.
  //   if (currentPage && productMap.size) {
  //     setPaginatedData(getPaginatedData(currentPage) as Product[]);
  //   }
  // }, [currentPage, productMap]);

  useEffect(() => {
    if (Object.keys(outletInfo || {}).length) {
      setIsLoading(true);
      setCursor("0");
      getAggreateProducts(outletInfo, promotions, true);
    }
  }, [outletInfo, productCategoryId]);

  // Use for the fitler.
  // need to test when alot of the data.
  useEffect(() => {
    if (Object.keys(outletInfo || {}).length) {
      setCursor("0");
      // to make sure the cursor was save to the state
      setTimeout(() => {
        getAggreateProducts(outletInfo, promotions, true);
      }, 300);
    }
  }, [inputValue, filterValue]);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const headerItems = [
    {
      label: t("Header.home"),
      route: "/landing",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: "Product Category",
      route: "/product/productCategory",
      className: "labelTextStyle",
    },
  ];

  const handleToProduct = (route: any, product: TradeInfoAggregateUI) => {
    router.push({
      pathname: route,
      query: {
        productId: product?.productCatalogueId,
        productInfo: JSON.stringify(product),
      },
    });
  };

  // const getProductCategoriesSummary = async () => {
  //   try {
  //     const dataSource = new DataSource(
  //       "productCategory/summary",
  //       "",
  //       true
  //     );
  //     const res: any = await dataSource.load();
  //     if (res?.length) {
  //           setProductCategorySummary(res);
  //           return res;
  //       }
  //       return [];
  //   } catch (err) {
  //     return [];
  //   }
  // };

  const getProductCategoriesSecondLevel = async () => {
    try {
      // const findTrading = productCategorySummary.find((a: any) => a._id === "65a78877325f3fd96a808b33")
      // const subCategoryIds = findTrading?.subCategoryIds;
      // const dataSource = new DataSource(
      //   "productCategories",
      //   `?parentCategoryIds=${subCategoryIds?.join('&parentCategoryIds=')}&sort=createdAt&sortOrder=-1`,
      //   true
      // );
      const dataSource = new DataSource(
        "productCategories",
        "parentCategoryIds=65a78877325f3fd96a808b33",
        true
      );
      const res: any = await dataSource.load();
      if (res && res !== null) {
        setProductCategoriesSecondLevel(res);
      } else {
        setProductCategoriesSecondLevel([]);
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  // const getProductByCategories = async () => {
  //   const categoryId = router.query.categoryId;
  //   if (!categoryId) return [];
  //   try {
  //     const params = {
  //       categoryIds: categoryId,
  //       status: "ACTIVE",
  //     };
  //     const dataSource = new DataSource(
  //       "productCategories",
  //       encodeParams(params),
  //       true
  //     );
  //     const res: any = await dataSource.load();
  //     return res?.items || [];
  //   } catch (err) {
  //     return [];
  //   }
  // };

  // Warning - need to change after implement outlet selected.
  const getOutletProduct = () => {
    let currentOutletId = localStorage.getItem("currentOutletId");
    let params: any = {
      sort: "createdAt",
      sortOrder: "-1",
      id: currentOutletId,
    };
    const dataSource = new DataSource("outlets", encodeParams(params), false);
    dataSource.load().then(async (res: any) => {
      if (!res?.items?.length) {
        // stop loading here.
        return;
      }

      const objectMap = res.items.reduce(
        (accumulator: any, current: Outlet) => {
          accumulator["companyId"] = accumulator["companyId"] || [];

          if (
            current.companyId &&
            !companyMap.has(current.companyId) &&
            !accumulator["companyId"].includes(current.companyId)
          ) {
            accumulator["companyId"].push(current.companyId ?? "");
          }

          current.outletProductList?.reduce(
            (acc: any, product: OutletProductList) => {
              accumulator["productId"] = accumulator["productId"] || [];
              if (
                product.productId &&
                !productMap.has(product.productId) &&
                !accumulator["productId"].includes(product.productId)
              ) {
                accumulator["productId"].push(product.productId ?? "");
              }
              return acc;
            },
            {}
          );
          return accumulator;
        },
        {}
      );

      const outlet: Outlet = res.items[0];

      delete outlet.outletProductList;

      setOutletInfo(outlet);
      const company = await getCompany(objectMap["companyId"]);
      if (company?.length) {
        // setOutletProductListId(objectMap['productId']);
        // getProduct(objectMap["productId"], 0, company[0].id, outlet);
      }
    });
  };

  const getCompany = async (id: string[] = []) => {
    try {
      const params: any = {
        status: "ACTIVE",
        id: [],
      };
      params.id = id?.splice(0, 50);
      const dataSource = new DataSource(
        "companies",
        encodeParams(params),
        false
      );
      const res: any = await dataSource.load();
      if (res !== null && res.items.length > 0) {
        const newDataMap = new Map(companyMap);

        res.items.forEach((item: CompanyGeneralInfo) => {
          if (!newDataMap.has(item.id)) {
            newDataMap.set(item.id, item);
          }
        });

        setCompanyMap(newDataMap);
      }
      return res?.items || [];
    } catch (err) {
      return [];
    }
  };

  const getAggreateProducts = async (
    outlet?: Outlet,
    promotions?: Promotion[],
    isRequestNewData?: boolean
  ) => {
    setIsLoading(true);
    try {
      const params: any = {
        maxResultsPerPage: 100,
        sellingType: "SELLING",
        companyId: retailerAccess.companyId,
        outletId: outlet?.id || outletInfo?.id,

        // autoCheckout eligibility date
        activeAt: activeAt,

        pageNumber: "1",
      };
      if (filterValue.tradeType !== "PROMOTION") {
        if (productCategoryId) {
          params.categoryId = productCategoryId;
        }

        if (inputValue) {
          params.fuzzySearch = encodeURIComponent(inputValue);
        }
        if (filterValue.tradeType === "HOTSELLING") {
          params.tradeLabel = filterValue.tradeType;
        }
        if (
          filterValue.tradeType !== "HOTSELLING" &&
          filterValue.tradeType !== "NEWPRODUCT"
        ) {
          params.tradeType = filterValue.tradeType;
        }
        if (filterValue.tradeType === "NEWPRODUCT") {
          params.isNewProduct = "TRUE";
        }
        params.sellingPriceGT = filterValue.sellingPriceGT;
        params.sellingPriceLT = filterValue.sellingPriceLT;
      } else if (filterValue.tradeType === "PROMOTION") {
        const dataSource = new DataSource(
          "promotion/products",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load();

        if (res[0].productIds.length > 0) {
          params.productCatalogueId = res[0].productIds;
        } else {
          setData([]);
          setCursor("0");
          setIsLoading(false);
          return;
        }
      }

      if (cursor !== "0" && !isRequestNewData) {
        params.pageNumber = cursor;
      }

      let isCallFullApi =
        filterValue.tradeType === "PREORDER" ||
        filterValue.tradeType === "PROMOTION";

      let res: any;

      if (filterValue.tradeType !== "PROMOTION") {
        const dataSource = new DataSource(
          "productTradeInfo/aggregate",
          encodeParams(params),
          isCallFullApi
        );
        res = await dataSource.load();
      } else {
        res = await getProductTradeInfoByProductId(params);
      }

      if (!isCallFullApi ? res?.items?.length === 0 : res?.length === 0) {
        setData([]);
        setCursor("0");
        setIsLoading(false);
        return;
      }

      const productTradeInfoAggregate: TradeInfoAggregate[] = !isCallFullApi
        ? res?.items
        : res;

      const productCatalogueId = productTradeInfoAggregate.map(
        (item) => item.productCatalogueId
      );

      const preOrderProductCatalogueId: any = [];

      productTradeInfoAggregate.map((item) => {
        if (item.tradeType === "PREORDER") {
          preOrderProductCatalogueId.push(item.productCatalogueId);
        }
      });

      const preOrderSettingById = await getPreOrderSettingProduct(
        preOrderProductCatalogueId
      );

      const preOrderSettingProductId = preOrderSettingById
        .map((obj) => obj.productIds)
        .flat();

      // const imageMap = await getImageIndexing(productCatalogueId);
      const promotionRelateds = await getPromotionProduct(
        cloneDeep(productCatalogueId)
      );

      const promotionRelatedOutlets = await getPromotionRelatedOutlet(
        promotionRelateds,
        [],
        ""
      );

      let tradeInfoProductUI = productTradeInfoAggregate.map(
        (item: TradeInfoAggregateUI) => {
          // const imageFromIndexing = imageMap.get(item?.productCatalogueId);
          // console.log("imageFromIndexing: ", imageFromIndexing);
          // if (imageFromIndexing) {
          //   item.imageIndexing = imageFromIndexing[0];
          // }

          const promotionProduct = promotionRelateds?.find(
            (promotion) => promotion.productId === item.productCatalogueId
          );

          let isSinglePromo = false,
            isBundlePromo = false;
          let promotionInitId: string[] = [];

          // for upload outlet list
          if (promotionProduct) {
            promotionRelatedOutlets.map((promotion) => {
              if (promotionProduct.promoIds.includes(promotion?.id || "")) {
                if ((promotion?.productGroups?.length || 0) > 1)
                  isBundlePromo = true;
                if ((promotion?.productGroups?.length || 0) === 1)
                  isSinglePromo = true;
                if (promotion.isAutoApply !== "FALSE") {
                  promotionInitId = promotionInitId.concat(promotion?.id || "");
                }
              }
            });
          }

          // comment it.
          // const promotionProductByCategoires = getPromotionByProductId(
          //   item.productCatalogueId,
          //   promotions
          // );
          // if (promotionProductByCategoires.length) {
          //   promotionProductByCategoires.map((promotion) => {
          //     if ((promotion?.productGroups?.length || 0) > 1)
          //       isBundlePromo = true;
          //     if ((promotion?.productGroups?.length || 0) === 1)
          //       isSinglePromo = true;
          //     if (promotion.isAutoApply !== "FALSE") {
          //       promotionInitId = promotionInitId.concat(promotion?.id || "");
          //     }
          //   });
          // }

          item.isSinglePromo = isSinglePromo;
          item.isBundlePromo = isBundlePromo;
          item.outletCompanyId = outlet?.companyId || outletInfo?.companyId;
          item.promotionInitId = Array.from(new Set(promotionInitId));
          return item;
        }
      );

      if (filterValue.tradeType === "PROMOTION") {
        tradeInfoProductUI = tradeInfoProductUI.filter(
          (item) => item.isSinglePromo || item.isBundlePromo
        );
      }

      tradeInfoProductUI = tradeInfoProductUI.filter((item) => {
        if (
          item.tradeType === "PREORDER" &&
          preOrderSettingProductId.includes(item.productCatalogueId)
        ) {
          return true; // item.productCatalogueId === preOrderSettingById;
        } else if (item.tradeType !== "PREORDER") {
          return true;
        }
        return false;
      });

      if (cursor === "0" || isRequestNewData) {
        setData((data) => [...tradeInfoProductUI]);
        setIsLoading(false);
      } else {
        setData((data) => data.concat(tradeInfoProductUI));
        setIsLoading(false);
      }

      if (res.cursor) {
        setCursor(res.cursor);
      }
      setIsLoading(false);
    } catch (err) {
      setIsLoading(false);
    }
  };

  const getPromotionByProductId = (
    productId: string,
    tempPromotions?: Promotion[]
  ) => {
    if (!productId) return [];

    const promotionsCheck = tempPromotions?.length
      ? tempPromotions
      : promotions;

    const productPromotions = promotionsCheck.filter((item) => {
      const productIds = item.productGroups?.flatMap((product) =>
        product.selectedProducts?.flatMap((selected) => selected.productId)
      );
      return productIds?.includes(productId);
    });
    return productPromotions;
  };

  // const getProduct = async (id: string[] = [], spliceNumber = 0, companyId?: string, outlet?: Outlet) => {
  //   try {
  //     const { priceGroupId, companyId } = outlet || outletInfo || {};
  //     const productId = id.splice(spliceNumber * 50, 50)
  //     const params = {
  //       id: productId
  //     }
  //     const productPriceGroups = await getProductTradeInfo(productId, companyId, priceGroupId);
  //     const promotionRelated = await getPromotionProduct(productId);
  //     const promotions = await getPromotionRelatedOutlet(promotionRelated);

  //     const dataSoruce = new DataSource("productCatalogues", encodeParams(params), false);
  //     const res: any = await dataSoruce.load();
  //     // skip it when does not has product.
  //     if (!res?.items.length) return;

  //     const newDataMap = new Map(productMap);

  //     res.items.forEach((item: Product) => {
  //       const product = productPriceGroups?.find(price => price.productCatalogueId === item.id)
  //       const promotionProduct = promotionRelated?.find(promotion => promotion.productId === item.id)
  //       let isSinglePromo = true, isBundlePromo = true;
  //       if (promotionProduct) {
  //         promotions.map((promotion) => {
  //           if (promotionProduct.promoIds.includes(promotion?.id || '')) {
  //             if ((promotion?.productGroups?.length || 0) > 1) isBundlePromo = true
  //             if ((promotion?.productGroups?.length || 0) === 1) isSinglePromo = true
  //           }
  //         })
  //       }
  //       const { sellingPrice, tradeType } = product || {};
  //       newDataMap.set(item.id, { ...item, sellingPrice, isSinglePromo, isBundlePromo, priceGroupId, outletCompanyId: companyId, tradeType });
  //     });

  //     setProductMap(newDataMap);

  //     return res.items
  //   }
  //   catch (err) {
  //     return []
  //   }
  // }

  const getProductTradeInfo = async (
    id: string[] = [],
    companyId?: string,
    priceGroupId?: string
  ): Promise<any[]> => {
    // productTradeInfos/flatten
    try {
      if (!id?.length) return [];

      const companyMapId = Array.from(companyMap.keys());
      // need to change only can access one outlet only.
      const params = {
        productCatalogueId: id,
        companyId: companyMapId?.length ? companyMapId[0] : companyId,
        status: "ACTIVE",
      };
      const dataSource = new DataSource(
        "productTradeInfos",
        encodeParams(params),
        true
      );
      const res: any = await dataSource.load();
      if (!res?.length) return [];

      // group params by uomId

      const groupDefaultUOMId = res.reduce(
        (accum: any, current: ProductTradeInfo) => {
          accum[current.defaultUOMId] = accum[current.defaultUOMId] || [];
          accum[current.defaultUOMId].push(current.productCatalogueId);
          return accum;
        },
        {}
      );

      const arrayGroup = Object.keys(groupDefaultUOMId).map((key) => {
        return {
          uomId: key,
          productId: groupDefaultUOMId[key],
        };
      });

      const results = await getProductPriceGroup(
        arrayGroup,
        companyId,
        priceGroupId
      );

      const groupTradeInfoAndProduct = res.map(
        (tradeInfo: ProductTradeInfo) => {
          const price = results.find(
            (item) =>
              item.productCatalogueId === tradeInfo.productCatalogueId &&
              item.productUOMId === tradeInfo.defaultUOMId
          );
          return Object.assign(tradeInfo, price);
        }
      );

      return groupTradeInfoAndProduct;
    } catch (err) {
      return [];
    }
  };

  const getProductPriceGroup = async (
    tradeInfo: GroupTradeInfo[],
    companyId?: string,
    priceGroupId?: string
  ): Promise<ProductPriceGroup[]> => {
    let results: any = [];
    if (!tradeInfo.length) return results;

    const companyMapId = Array.from(companyMap.keys());
    for (let i = 0; i < tradeInfo.length; i++) {
      const item: any = tradeInfo[i];
      let { uomId, productId } = item;
      while (productId?.length) {
        const params = {
          activeAt: activeAt,
          productCatalogueId: productId.splice(0, 50),
          productUOMId: uomId,
          companyId: companyMapId?.length ? companyMapId[0] : companyId,
          priceGroupId: priceGroupId || outletInfo?.priceGroupId,
        };

        const dataSource = new DataSource(
          "productPriceGroups",
          encodeParams(params),
          true
        );
        const res: any = await dataSource.load().catch(() => {
          productId = [];
        });

        if (res?.length) {
          results = results.concat(res);
        }
      }
    }

    return results;
  };

  const getPreOrderSettingProduct = async (
    id: string[] = []
  ): Promise<PreOrderSetting[]> => {
    let result: any = [];
    try {
      let currentOutletId = localStorage.getItem("currentOutletId");
      if (!id.length) return [];

      while (id.length) {
        const params = {
          companyBranchIds: retailerAccess?.companyBranchIds,
          outletIds: currentOutletId,
          activeAt: activeAt,
          status: "ACTIVE",
          isOrCondition: "TRUE",
          outletCategoryIds: outletInfo?.outletCategoryIds,
          productIds: id.splice(0, 50),
        };
        const dataSource = new DataSource(
          "preorderSettings",
          encodeParams(params),
          false,
          "v2"
        );
        const res: any = await dataSource.load();
        if (res?.items?.length) {
          result = result.concat(res.items);
        }
      }

      return result || [];
    } catch (err) {
      return result || [];
    }
  };

  const getPromotionProduct = async (
    id: string[] = []
  ): Promise<OutletPromotion[]> => {
    let result: any = [];
    try {
      let currentOutletId = localStorage.getItem("currentOutletId");
      if (!id.length) return [];

      while (id.length) {
        const params = {
          outletId: currentOutletId,
          productId: id.splice(0, 50),

          // autoCheckout eligibility date
          activeAt: activeAt
        };
        const dataSource = new DataSource(
          "promotion/outlets",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load();
        if (res?.length) {
          result = result.concat(res);
        }
      }

      return result || [];
    } catch (err) {
      return result || [];
    }
  };

  const getProductTradeInfoByProductId = async (
    params: any
  ): Promise<any[]> => {
    let result: any = [];
    const customParmas = cloneDeep(params);
    const id = params.productCatalogueId;
    try {
      if (!id.length) return [];

      while (id.length) {
        customParmas.productCatalogueId = id.splice(0, 50);

        const dataSource = new DataSource(
          "productTradeInfo/aggregate",
          encodeParams(customParmas),
          false
        );
        const res: any = await dataSource.load();
        if (res?.items?.length) {
          result = result.concat(res?.items);
        }
      }

      return result || [];
    } catch (err) {
      return result || [];
    }
  };

  const getPromotionRelatedOutlet = async (
    promotionRelatedOutlet: OutletPromotion[],
    outletCategoryIds: string[],
    outletCompanBranchId: string
  ): Promise<Promotion[]> => {
    try {
      if (!promotionRelatedOutlet.length) return [];

      let groupPromotionId: Array<string> = [];
      promotionRelatedOutlet.map((promotion) => {
        promotion.promoIds.map((item) => {
          if (!groupPromotionId.includes(item)) {
            groupPromotionId.push(item);
          }
        });
      });

      if (!groupPromotionId.length) return [];

      let results: Array<Promotion> = [];

      while (groupPromotionId.length) {
        const params = {
          id: groupPromotionId.splice(0, 50),
          outletCategoryIds,
          companyBranchIds: outletCompanBranchId,
          activeAt: activeAt,
          status: "ACTIVE",
        };
        const dataSource = new DataSource(
          "promotions",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch((err) => {
          groupPromotionId = [];
        });
        results = results.concat(res?.items || []);
      }

      return results;
    } catch (err) {
      return [];
    }
  };

  const getPromotions = async (outletCategoryIds: string[] = []) => {
    try {
      const params = {
        activeAt: activeAt,
        status: "ACTIVE",
        outletCategoryIds,
      };
      const dataSource = new DataSource(
        "promotions",
        encodeParams(params),
        true
      );
      const res: any = await dataSource.load();
      if (res?.length) {
        setPromotions(res);
        return res;
      }
      return [];
    } catch (err) {
      return [];
    }
  };

  // const getImageIndexing = (productId: string[]) => {
  //   const newDataMap = new Map(imageIndexingMap);
  //   const params = {
  //     productId: productId,
  //     companyId: retailerAccess.companyId,
  //     companyBranchId: retailerAccess.companyBranchId,
  //     status: "ACTIVE",
  //   };
  //   const dataSource = new DataSource(
  //     "imageIndexings",
  //     encodeParams(params),
  //     false,
  //     "v1"
  //   );
  //   dataSource
  //     .load()
  //     .then((res: any) => {
  //       if (res !== null) {
  //         console.log("res: ", res.items);
  //         // setImageIndexingMap((prevDataMap) => {

  //         res.items?.forEach((data: any) => {
  //           if (!newDataMap.has(data.productId)) {
  //             const pic = data.productImage?.map(
  //               (val: string) => PUBLIC_BUCKET_URL + val
  //             );
  //             newDataMap.set(data.productId, pic);
  //           }
  //         });

  //         setImageIndexingMap(newDataMap);
  //         // });
  //       }
  //     })
  //     .catch(() => {
  //       //* This Part need re-edit*//
  //     });
  //   return newDataMap;
  // };

  const handleCategoryClick = (category: any, values: any = "") => {
    let categoryData: ProductCategories = {};
    if (values !== "") {
      categoryData.id = values.id;
      categoryData.name = values.name[0][1].props.children;
    } else {
      categoryData.id = category.id;
      categoryData.name = category.name;
    }
    // setSelectedCategory(category.id); // Update state with the selected category
    setInputValue("");
    router.push({
      pathname: "/product/productCategory",
      query: {
        category: categoryData.name,
        categoryId: categoryData.id,
      },
    });
  };

  const handleStockChange = (value: any) => {
    setCursor("0");
    setFilterValue((prevValue) => ({
      ...prevValue,
      tradeType: value.length ? value[0] : "",
    }));
  };

  const handlePriceChange = (value: any) => {
    setCursor("0");
    setFilterValue((prevValue) => ({
      ...prevValue,
      sellingPriceGT: value[0],
      sellingPriceLT: value[1],
    }));
  };
  // const getPaginatedData = (page: number) => {
  //   const startIndex = (page - 1) * pageSize;
  //   const endIndex = startIndex + pageSize;

  //   // due to the productMap is get from the getProduct, it does not get full product from outlet show that need to check and split it.
  //   if (endIndex > productMap.size) {
  //     const subset = Array.from(productMap.values()).slice(productMap.size - pageSize, productMap.size);
  //     return subset;
  //   }
  //   else {
  //     const subset = Array.from(productMap.values()).slice(startIndex, endIndex);
  //     return subset;
  //   }
  // };

  // const handlePageChange = async (newPage: number, newPageSize: number) => {
  //   await getProduct(outletProductListId, newPage);
  //   setCurrentPage(newPage);
  //   setPageSize(newPageSize);
  // };

  const showListContent = () => {
    return (
      <InfiniteScroll
        dataLength={data.length}
        next={getAggreateProducts}
        hasMore={cursor !== "0"}
        loader={<div onLoad={() => setIsLoading(true)}></div>} // Update isLoading state
        endMessage={
          !isLoading && <Divider plain>{t("ListingTable.endMessage")}</Divider>
        } // Render endMessage only if not loading
        scrollableTarget="scrollableDiv"
        scrollThreshold="100px"
        className="mt-3 w-full sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2"
      >
        <List
          grid={{
            xs: 2,
            sm: 4,
            md: 5,
            lg: 6,
            xl: 7,
            xxl: 9,
          }}
          className="overflow-x-hidden bg-lightPurple"
          loading={isLoading}
          dataSource={data}
          renderItem={(product: TradeInfoAggregate) => (
            <List.Item key={product.productCatalogueId}>
              <div className="product-col">
                <a
                  href={`/product/productDetail?productId=${product?.productCatalogueId
                    }&productInfo=${JSON.stringify(product)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  // className="product-col"
                  onClick={(e) => {
                    e.preventDefault(); // Prevent default link behavior
                    handleToProduct("/product/productDetail", product); // Call your function
                  }}
                >
                  <ProductBlock
                    className="flex flex-col items-center"
                    productName={product?.name || "EMPTY"}
                    product={product}
                    price={product.sellingPrice || 0.0}
                    image={
                      (product?.picture &&
                        PUBLIC_BUCKET_URL + product.picture) ||
                      noImage.src
                    }
                  />
                </a>
              </div>
            </List.Item>
          )}
        />
      </InfiniteScroll>
    );
  };

  return (
    <div className="flex flex-col w-full min-h-screen bg-bgOrange">
      <Header
        items={headerItems}
        hasSearch={true}
        inputDisabled={filterValue.tradeType === "PROMOTION" ? true : false}
        values={(item: any) => setInputValue(item)}
        fieldValue={inputValue}
      />
      <div
        className={`navbar sticky top-0 py-4 ${isScrolled ? "bg-white shadow-lg z-10" : ""
          }`}
      >
        <TopSideBar
          categories={productCategoriesSecondLevel}
          onHandleClick={(value: any) => handleCategoryClick(queryInfo, value)}
          onHandleStockChange={handleStockChange}
          onHandlePriceChange={handlePriceChange}
        />
      </div>
      {showListContent()}
      <AppFooter retailerAccessValues={retailerAccess} />
    </div>
  );
}

type GroupTradeInfo = {
  uomId: string;
  productId: any;
};

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default productCategory;
