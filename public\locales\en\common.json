{"Common": {"next": "Next", "eg": "Eg", "email": "Email", "edit": "Edit", "remove": "Remove", "select": "Select", "back": "Back", "save": "Save", "cancel": "Cancel", "yes": "Yes", "staff": "Staff", "status": "Status", "approvalStatus": "Approval Status", "action": "Action", "viewMore": "View More", "no": "No", "ok": "OK", "selectAll": "Select All", "submit": "Submit", "seeMore": "See More", "whatIsThe": "What Is The", "applyFilter": "Apply Filter", "resetAll": "Reset All", "updateSuccess": "Update Successful", "updateUnsuccess": "Update Failed", "createSuccess": "Create Successful", "createUnsuccess": "Create Failed", "deleteSuccess": "Delete Successful", "deleteUnsuccess": "Delete Failed", "successful": "Successful", "failed": "Failed", "update": "Update", "delete": "Delete", "confirmDeleteMessage": "Confirm Delete?", "confirmDeleteDescription": "Deleting this item will remove it from the record. Are you sure you want to delete this item?", "Approval.Submit.Success": "Request Submitted for Approval", "Approval.Submit.Failed": "Failed to Submit Request for Approval", "PDFPrintMessage": "Unable to print as data(s) selected is overdue", "startAfter": "Start After", "endBefore": "End Before"}, "Header": {"search": "Search...", "promotion": "Promotion", "myHotSelling": "My Hot Selling", "lowInventoryItems": "Low Inventory Items", "areaTopSelling": "Top Selling In My Area", "home": "Home", "cart": "<PERSON><PERSON>", "profile": "Profile", "landing": "Landing", "setting": "Setting", "ticketing": "Ticketing", "goodsReturn": "Goods Return", "partialGoodsReturn": "Partial Goods Return", "dashboard": "Dashboard", "invoice": "Invoice", "goodsReturnSummary": "Goods Return Summary", "orderHistory": "Order History", "paymentHistory": "Payment History", "statement": "Statement", "debitNote": "Debit Note", "creditNote": "Credit Note", "salesOrder": "Sales Order", "toPay": "To Pay", "preOrder": "PreOrder", "menu": "<PERSON><PERSON>", "logout": "Logout"}, "Login": {"default": "<PERSON><PERSON>", "title": "Welcome back,", "inputValidPassword": "Please input valid password! (At least 6 characters)", "wrongContactAndPassword": "Wrong contact number or password", "enterPassword": "Enter Password", "verifiedPhoneNumber": "Verify Phone Number", "returnLoginPage": "Return To Login Page", "loginSuccessful": "Login Successful", "updatePasswordSuccess": "Update Password Successful", "updatePasswordFail": "Update Password Failed", "signUp": {"title": "Sign Up", "firstTimeLogin": "First Time Login", "createNew": "Create A New Account", "firstName": "First Name", "Company": "Company", "CompanyRequirement": "Company Is a Required Field", "lastName": "Last Name", "nameNeeded": "What's your name?", "password": "Password", "confirmPassword": "Confirm Password", "enterPassword": "Enter Password", "inputValidPassword": "Please input valid password! (At least 6 characters)", "passwordNotMatch": "Password and confirm password are not match!", "passwordRequirement": "Password required at least 1 digit, 1 special character, 1 uppercase and 1 lowercase", "email": "Email", "enterFirstName": "Enter First Name", "enterLastName": "Enter Last Name", "sampleEmail": "<EMAIL>", "contactNumber": "Contact Number", "notEmptyPhoneNumber": "Phone number cannot be empty", "icNumber": "IC Number", "otpSent": "An OTP has sent to your phone number", "failSendOtp": "Failed to send OTP", "tryAgainLater": "You have tried more than 3 time. Please try again in 15 minutes.", "otpIncorrect": "The OTP you entered is incorrect.", "icFront": "IC Front Picture", "icBack": "IC Back Picture", "uploadPic": "Upload Pictures", "outletFrontPicture": "Outlet Front Picture", "outletName": "Outlet Name", "outletLocation": "Outlet Location", "latitude": "Latitude", "longitude": "Longitude", "outletAddress": "Outlet Address", "unitNo": "Unit No", "address1": "Address1", "address2": "Address2", "country": "Country", "state": "State", "city": "City", "postalCode": "Postal Code", "billContactPerson": "Contact Person", "billMobilePhone": "Mobile Phone", "billOfficePhone": "Office Phone", "billEmail": "<PERSON>", "bankName": "Bank Name", "accountNo": "Account No", "swiftCode": "Swift Code", "businessType": "Business Type", "ssm": "SSM No", "ssmExpireDate": "SSM Expire Date", "enterSsmNo": "Enter SSM No", "ssmNoNeeded": "SSM No Needed", "ssmDoc": "SSM Document", "uploadFiles": "Upload Files", "SignUpSuccess": "Registration Successful", "SignUpUnSuccess": "Registration UnSuccessful, Please try again later.", "resetFields": "Reset Fields", "dontHaveAccount?": "Dont have an account? ", "notFillRequiredFields": "You have not fill up all required fields.", "contactNumberExist": "Contact Number already exist.", "failVerifyOtp": "Failed to verify <PERSON><PERSON>. Please try again later."}, "forgotPassword": {"title": "Forgot Password?", "firstTimeLogin": "First Time Login", "default": "Forgot Password,", "enterNumber": "Please enter your registered contact number.", "enterOTP": "Please enter OTP code to reset your password.", "enterOTPVerified": "Please enter OTP code to verified your phone number.", "enterPassword": "Enter Password", "enterYourPassword": "Please enter your password", "enterCode": "Enter Code,", "successfulVerification": "Verified successful! Please enter your new password.", "successfulVerificationPhoneNumber": "Phone Number verified successful!", "successfulVerificationPhoneNumberContact": "Please wait 1-7days, admin will approval this record.", "resendCode": "Resend code", "didntReceive": "Didn't receive code?", "done": "Done", "otpIncorrect": "The OTP you entered is incorrect.", "otpSent": "An OTP has sent to your phone number", "failSendOtp": "Failed to send OTP", "uploadDoc": "Upload Document", "newPassword": "New Password", "confirmPassword": "Confirm Password", "tryAgainLater": "You have tried more than 3 time. Please try again in 15 minutes.", "success": "Password change successful", "fail": "Password fail to change.", "inputValidPassword": "Please input valid password! (At least 6 characters)", "passwordNotMatch": "Password and confirm password are not match!", "contactNumber": "Please enter your contact number", "notEmptyPhoneNumber": "Phone number cannot be empty", "passwordRequirement": "Password required at least 1 digit, 1 special character, 1 uppercase and 1 lowercase"}}, "ErrorPage": {"wrong": "Something Went Wrong", "sorry": "We're sorry, the page you are looking for doesn't exist or has been moved."}, "Validation": {"phoneFormat": "Phone number format is invalid", "requiredField": "is a required field", "invalidEmail": "Email format is invalid", "specialCharacter": "Special characters are not allowed", "icLength": "IC length incorrect", "alphabetCharacter": "Alphabet characters are not allowed", "numberCharacter": "Number characters are not allowed", "gstFormat": "GST format is invalid", "spaceNotPermitted": "Space is not permitted"}, "Checkout": {"checkout": "Checkout", "deliveryAddress": "Delivery Address", "selectAddress": "Select the address to deliver", "addAddress": "Add new address", "addAddressTitle": "Add Delivery Address", "addressName": "Address Name", "personInCharge": "Person-In-Charge", "phoneNumber": "Phone Number", "unitNo": "Unit No", "location": "Location", "address1": "Address 1", "address2": "Address 2", "country": "Country", "state": "State", "city": "City", "postalCode": "Postal Code", "longitude": "Longitude", "latitude": "Latitude", "editAddressTitle": "Edit Delivery Address", "gpsLocation": "GPS Location", "order": "Order", "validation": {"addressName": "Please input the address name.", "personInCharge": "Please input the person in charge.", "contactNumber": "Please input the contact number.", "unitNo": "Please input the unit no.", "location": "PLease input the location.", "address": "Please input the address.", "city": "Please input the city.", "postalCode": "Please input the postal code.", "state": "Please input the state.", "country": "Please input the country.", "longitude": "Please input the longitude.", "latitude": "Please input the latitude.", "titleConfirmAddAddress": "Confirm Add Delivery Address", "descriptionAddAddress": "Are You Sure You Want to Add Delivery Address?", "titleConfirmEditAddress": "Confirm Edit Delivery Address", "descriptionEditAddress": "Are You Sure You Want to Edit Delivery Address?"}, "orderItems": "Ordered Items", "orderDetail": "Orderd Detail", "subTotal": "Sub Total", "totalDiscount": "Total Discount", "shippingFee": "Shipping Fee", "totalTax": "Total Tax", "totalAmount": "Total Amount", "pre-OrderItems": "Pre-Order Items", "quantityExceeds": "Quantity Exceeds"}, "Cart": {"myCart": "My Cart", "productCode": "Product Code", "unitPrice": "Unit Price", "taxRate": "Tax Rate", "moq": "MOQ", "addToCart": "Add To Cart", "selectedUOM": "Please Select a UOM", "removeSuccess": "Remove Success", "removeUnsuccess": "Remove Unsuccess", "applyPromotion": "Apply Promotion", "AHSConfirmation": "Abnormal Products Quantity", "ConfirmCheckout": "Confirm Checkout", "TableColumns": {"ProductName": "Product Name", "PercentageExceeded": "Percentage Exceeded"}, "meetMOQ": "Please fulfill minimum order requirement"}, "Dashboard": {"dashboard": "Dashboard", "salesOrder": "Sales Order", "autoProposedOrder": "Auto Proposed Order", "recurringPreOrder": "Recurring Pre-Order", "stockTake": "Stock Take", "goodsReturn": "Goods Return", "invoice": "Invoice", "statement": "Statement", "creditNote": "Credit Note", "debitNote": "Debit Note", "paymentHistory": "Payment History", "jobListing": "Job Listing", "autoProposed": "Auto Proposed", "preOrder": "PreOrder", "order": "Order", "inventory": "Inventory", "financial": "Financial", "others": "Others", "creditLimit": "Credit Limit", "outstandingAmount": "Outstanding Amount", "overdueAmount": "Overdue Amount"}, "Setting": {"setting": "Setting", "helpFile": "Help File", "logout": "Logout", "firstName": "First Name", "lastName": "Last Name", "contact": "Contact", "icNumber": "IC Number", "ssmNo": "SSM No", "ssmDocs": "SSM Docs", "unitNo": "Unit No", "location": "Location", "outlet": "Outlet", "address1": "Address 1", "address2": "Address 2", "city": "City", "postalCode": "Postal Code", "state": "State", "country": "Country", "longitude": "Longitude", "latitude": "Latitude", "outltName": "Outlet Name", "storeFrontPhoto": "Store Front Photo", "outletName": "Outlet Name", "outletImage": "Outlet Image", "taxable": "Taxable", "taxExamptionNo": "Tax Examption No", "gstNo": "GST No", "sstNo": "SST No", "bankName": "Bank Name", "accountNo": "Account No", "swiftCode": "Swift Code", "outletLocation": "Outlet Location", "billContactPerson": "Contact Person", "billMobilePhone": "Mobile Phone", "billOfficePhone": "Office Phone", "billEmail": "<PERSON>", "tradeName": "Trade Name", "ssmRegistrationName": "SSM Registration Name", "outletDetails": "Outlet Details", "virtualAccountDetails": "Virtual Account Details", "businessRegistrationNumber": "Business Registration Number", "taxIdentificationNumber": "Tax Identification Number", "requiredEInvoice": "Required E-Invoice", "eInvoiceGeneralTin": "TIN Type", "printeInvoice": "Print E-Invoice"}, "SalesOrder": {"salesOrder": "Sales Order", "orderHistory": "Order History", "salesOrderNo": "Sales Order No", "orderDate": "Order Date", "estimatedDeliveryDate": "Estimated Delivery Date", "creditLimit": "Credit Limit", "outstanding": "Outstanding", "printSalesOrder": "Print Sales Order", "orderDetail": "Order Detail", "product": "Product", "productCode": "Product Code", "uom": "UOM", "unitPrice": "Unit Price", "quantity": "Quantity", "discount": "Discount", "tax": "Tax", "itemSubtotal": "Item <PERSON>", "remark": "Remark", "rejectRemark": "Reject Remark", "productName": "Product Name", "productSKU": "Product SKU", "promotion": "Promotion", "ViewAllPromotion": "View All Promotion", "promotionList": "Promotion List", "singlePromotion": "Single Promotion", "bundlePromotion": "Bundle Promotion", "chooseBundleProduct": "Choose Bundle Product", "warningDescription": "Attention! Your selected item has not been saved!", "warningTitle": "Confirm to cancel", "singlePromotionDetail": "Single Promotion Detail", "bundlePromotionDetail": "Bundle Promotion Detail", "focPromotionDetail": "FOC Promotion Detail", "orderedItems": "Ordered Items", "subTotal": "Sub Total", "totalDiscount": "Total Discount", "shippingFee": "Shipping Fee", "totalTax": "Total Tax", "totalAmount": "Total Amount", "applicantName": "Applicant Type | Account Name", "uploadCPO": "Upload CPO", "createSuccess": "Create Successful", "createUnsuccess": "Create Unsuccessful"}, "GoodsReturn": {"goodsReturn": "Goods Return", "returnDate": "Return Date", "makePartialReturn": "Make Partial Return", "makeFullReturn": "Make Full Return", "printGoodsReturn": "Print Goods Return", "staff": "Staff", "returnMode": "Return Mode", "returnType": "Return Type", "totalAmount": "Total Amount", "product": "Product", "invoice": "Invoice", "quantity": "Quantity", "returnQuantity": "Return Quantity", "goodsCondition": "Goods Condition", "reason": "Reason", "expiryDate": "Expiry Date", "uom": "UOM", "company": "Company", "productCode": "Product Code", "selectReason": "Select Reason", "selectUOM": "Select UOM", "selectGoodsCondition": "Select Goods Condition", "invoiceDate": "Invoice Date", "outletCode": "Outlet Code", "outlet": "Outlet", "choose": "<PERSON><PERSON>", "partialGoodsReturn": "Partial Goods Return", "chooseInvoice": "Choose Invoice", "productSKU": "Product SKU", "productName": "Product Name", "createdDate": "Created Date", "selectExpiryDate": "Select Expiry Date", "remarks": "Remarks", "companyName": "Company Name", "goodsReturnSummary": "Goods Return Summary", "noInvoiceFound": "No Invoice Found", "availableQuantity": "Available Quantity", "processingQuantity": "Processing Quantity", "overReturnableQuantity": "The quantity exceeds the Returnable Quantity", "chooseInvoiceError": "Please choose invoice to return", "returnableQuantity": "Returnable Quantity", "bad": "Bad", "good": "Good", "enterQuantity": "Please enter quantity to return", "invoiceNo": "Invoice No", "invoiceQuantity": "Invoice Quantity", "unitPrice": "Unit Price", "reasonToReturn": "Reason To Return", "applicantName": "Applicant Type | Account Name"}, "CreditNote": {"creditNote": "Credit Note", "creditNoteDate": "Credit Note Date", "creditType": "Credit Note Type", "nettAmount": "<PERSON><PERSON>", "usedAmount": "Used Amount", "printCreditNote": "Print Credit Note", "invoice": "Invoice", "company": "Company", "type": "Type", "grossAmount": "Gross Amount", "taxAmount": "Tax Amount", "supportedDocument": "Supported Document", "product": "Product", "invoiceQuantity": "Invoice Quantity", "returnQuantity": "Return Quantity", "totalPrice": "Total Price", "details": "Details", "creditNoteNo": "Credit Note No", "productSKU": "Product SKU", "productName": "Product Name", "sku": "SKU"}, "DebitNote": {"debitNote": "Debit Note", "debitNoteDate": "Debit Note Date", "debitType": "Debit Note Type", "nettAmount": "<PERSON><PERSON>", "paidAmount": "<PERSON><PERSON>", "printDebitNote": "Print Debit Note", "details": "Details", "returnQuantity": "Return Quantity", "invoiceQuantity": "Invoice Quantity", "product": "Product", "supportedDocument": "Supported Document", "taxAmount": "Tax Amount", "grossAmount": "Gross Amount", "unitNo": "Unit No", "invoice": "Invoice", "company": "Company", "productName": "Product Name", "outletName": "Outlet Name", "productSKU": "Product SKU"}, "Invoice": {"invoice": "Invoice", "invoiceDate": "Invoice Date", "outletCode": "Outlet Code", "outlet": "Outlet", "overdueDate": "Overdue Date", "po": "PO", "createdDate": "Created Date", "makePayment": "Make Payment", "makeGoodsReturn": "Make Goods Return", "printInvoice": "Print Invoice", "printDo": "Print Do", "company": "Company", "do": "DO", "shippingAddress": "Shipping Address", "invoiceAmount": "Invoice Amount", "doDate": "DO Date", "supportedDocument": "Supported Document", "product": "Product", "quantity": "Quantity", "discount": "Discount", "totalPrice": "Total Price", "details": "Details", "productCode": "Product Code", "unitPrice": "Unit Price", "taxRate": "Tax Rate", "reason": "Reason", "selectReason": "Select Reason", "returnMode": "Return Mode", "selectReturnMode": "Select Return Mode", "returnDate": "Return Date", "remark": "Remark", "invoiceNo": "Invoice No", "invoiceQuantity": "Invoice Quantity", "returnQuantity": "Return Quantity", "salesOrder": "Sales Order", "ProductOrdered": "Product Ordered", "Totebox": "Totebox", "SealId": "SealId", "eInvoiceSignature": "E-Invoice Signature", "eInvoiceSubmissionType": "E-Invoice Submission Type", "eInvoiceStatus": "E-Invoice Status", "eInvoiceUuid": "E-Invoice Uuid", "eInvoiceSubmissionUid": "E-Invoice Submission Uuid", "eInvoiceSubmittedAt": "E-Invoice Submitted At", "eInvoiceValidatedAt": "E-Invoice Validated At", "eInvoiceRespondedAt": "E-Invoice Responded At", "eInvoiceRequestCancelAt": "E-Invoice Request Cancel At", "eInvoiceCancelledAt": "E-Invoice Cancelled At", "eInvoiceLastPrintTime": "E-Invoice Last Print Time", "deliveryStatus": "Delivery Status", "deliveredDate": "Delivered Date", "proofOfDelivery": "Proof Of Delivery", "totalAmount": "Total Amount", "paidAmount": "<PERSON><PERSON>", "outStandingAmount": "OutStanding Amount", "sortAscOrderBy": "Sort Ascending Order by"}, "Payment": {"payment": "Payment", "paymentDate": "Payment Date", "paymentAmount": "Payment Amount", "type": "Type", "documentAmount": "Document Amount", "number": "Number", "paidAmount": "Paid Amount/(Used) Amount", "date": "Date", "receiptNo": "Receipt No", "paymentHistory": "Payment History", "method": "Payment method", "printOR": "Print Official Receipt", "toPay": "To Pay", "statement": "Statement", "invoice": "Invoice", "invoiceDate": "Invoice Date", "totalProduct": "Total Product", "invoiceAmount": "Invoice Amount", "remainingAmount": "Remaining Amount", "amountToPaid": "Amount To Pay", "debitNote": "Debit Note", "debitNoteDate": "Debit Note Date", "netAmount": "Net Amount", "creditNote": "Credit Note", "creditNoteDate": "Credit Note Date", "FPXPayment": "FPX Payment", "errorAmount0": "Amount can not be 0", "referenceNo": "Reference No", "referenceDate": "Reference Date", "receipt": "Receipt", "upload": "Upload", "addDebitNote": "Add Debit Note", "addCreditNote": "Add Credit Note", "paymentDetail": "Payment Detail", "bankName": "Bank Name", "cash": "Cash", "cheque": "Cheque", "onlineTransfer": "Online Transfer", "collection": "Collection", "viewPayment": "viewPayment", "detail": "Detail", "processingAmount": "Processing Amount", "applicantName": "Applicant Type | Account Name"}, "Statement": {"statement": "Statement", "statementDate": "Statement Date", "invoiceDate": "Invoice Date", "paidAmont": "<PERSON><PERSON>", "creaatedDate": "Created Date", "makePayment": "Make Payment", "printStatement": "Print Statement", "printInvoice": "Print Invoice", "printCreditNote": "Print Credit Note", "printDebitNote": "Print Debit Note", "number": "Number", "type": "Type", "documentaAmount": "Document Amount", "paidAmountUsed": "Paid Amount/(Used) Amount", "date": "Date", "daysDifferent": "Days Different", "statementNo": "Statement No", "statementAmount": "Statement Amount", "paidAmount": "<PERSON><PERSON>", "createdDate": "Created Date", "status": "Status", "invoice": "Invoice", "creditNote": "Credit Note", "debitNote": "Debit Note", "dueDate": "Due Date", "outstandingAmount": "Outstanding Amount", "processingAmount": "Processing Amount"}, "Landing": {"newProduct": "New Product", "promotion": "Promotion", "hotSelling": "Hot Selling", "lowInventoryItems": "Low Inventory Items", "topSellingInMyArea": "Top Selling In My Area", "shopByCategory": "Shop By Category", "myHotSelling": "My Hot Selling"}, "Product": {"product": "Product", "name": "Name", "barcode": "Barcode", "sku": "S<PERSON>", "ctn": "CTN", "unit": "UNIT", "addToCart": "Add To Cart", "quantity": "Quantity", "promotion": "Promotion", "stockLastFor": "Stock Last For", "averageSales": "Average Sales", "moq": "MOQ", "lastStockCount": "Last Stock", "unitofMeasurement": "Unit of Measurement", "brand": "Brand Name", "description": "Description", "productInvolved": "Product Involved", "focProduct": "FOC Product(s)", "productCode": "Product Code", "unitPrice": "Unit Price", "uom": "UOM", "details": "Product Details", "orderInfo": "Order Information"}, "AutoProposed": {"autoProposedList": "Auto Proposed List", "addToCart": "Add To Cart", "uom": "UOM"}, "PlaceHolder": {"remark": "Remark", "newPassword": "New Password", "confirmPassword": "Confirm Password", "password": "Password", "enterFirstName": "Enter First Name", "enterLastName": "Enter Last Name", "sampleEmail": "<EMAIL>", "enterSSMNo": "Enter SSM No", "enterICNumber": "Enter IC Number", "outletName": "Outlet Name", "soleProprietorship": "Sole Proprietorship", "gstNo": "GST No", "sstNo": "SST No", "taxExamptionNo": "Tax Examption No", "bankName": "Bank Name", "accountNo": "Account No", "swiftCode": "Swift Code", "billContactPerson": "Contact Person", "billMobilePhone": "Mobile Phone", "billOfficePhone": "Office Phone", "billEmail": "<PERSON>", "tradeName": "Trade Name", "unitNo": "Unit No", "address1": "Address 1", "address2": "Address 2", "city": "City", "postalCode": "Postal Code", "state": "State", "country": "Country", "longitude": "Longitude", "latitude": "Latitude", "outletLocation": "Outlet Location", "invoiceNo": "Invoice No"}, "PreOrder": {"preOrder": "PreOrder", "orderHistory": "Order History", "salesOrderNo": "Sales Order No", "orderDate": "Order Date", "estimatedDeliveryDate": "Estimated Delivery Date", "creditLimit": "Credit Limit", "outstanding": "Outstanding", "printSalesOrder": "Print Sales Order", "orderDetail": "Order Detail", "product": "Product", "productCode": "Product Code", "uom": "UOM", "unitPrice": "Unit Price", "quantity": "Quantity", "discount": "Discount", "tax": "Tax", "itemSubtotal": "Item <PERSON>", "remark": "Remark", "productName": "Product Name", "productSKU": "Product SKU", "ViewAllPromotion": "View All Promotion", "promotionList": "Promotion List", "singlePromotion": "Single Promotion", "bundlePromotion": "Bundle Promotion", "chooseBundleProduct": "Choose Bundle Product", "warningDescription": "Attention! Your selected item has not been saved!", "warningTitle": "Confirm to cancel", "singlePromotionDetail": "Single Promotion Detail", "bundlePromotionDetail": "Bundle Promotion Detail", "focPromotionDetail": "FOC Promotion Detail", "cancelOrder": "Cancel Order", "status": "Status"}, "Modal": {"confirmDelete": "Confirm Delete?", "confirmCancel": "Confirm Cancel?", "ensureContent": "Are you sure to perform the action?", "close": "Close"}, "Fpx": {"SuccessfulTitle": "Payment Successful", "SuccessfulSubTitle": "have been successfully paid.", "successfulButton": "OK. Got It!", "UnsuccessfulTitle": "Payment UnSuccessful", "UnSuccessfulSubTitle": "Something went wrong. Please try again later.", "UnsuccessfulButton": "OK. Got It!", "ProcessingTitle": "Payment Processing", "ProcessingSubTitle": "under processing.", "ProcessingButton": "OK. Got It!"}, "ListingTable": {"endMessage": "It is all, nothing more"}, "Excel": {"excelSample": "Excel Sample", "errorDataInserted": "Error Data Inserted", "printErrorExcel": "Do you want to print out the error data in excel?", "yes": "Yes", "no": "No", "excelDoneCreate": "data has been created"}}