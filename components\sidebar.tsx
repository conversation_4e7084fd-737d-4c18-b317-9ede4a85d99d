import { Button, Checkbox, Input, InputRef, <PERSON>u, Popover, Row } from "antd";
import { useEffect, useRef, useState } from "react";
import { useRouter } from "next/router";
import { CaretDownOutlined } from "@ant-design/icons";
import { CheckboxValueType } from "antd/es/checkbox/Group";
import _, { set } from "lodash";

export const FilterSidebar = (props: any) => {
  const router = useRouter();
  const inputMinRef = useRef<InputRef>(null);
  const inputMaxRef = useRef<InputRef>(null);
  const selectedCategory = router.query.categoryId;
  const categoryNameArray = props.categories.map((item: any) => item.name);
  const categoryIdArray = props.categories.map((item: any) => item.id);
  // Create a new array to reorder categories with the selected category moved to the front
  const reorderedCategories = (() => {
    const selectedIndex = categoryIdArray.indexOf(selectedCategory);

    if (selectedIndex !== -1) {
      const selectedCategoryObj = {
        id: categoryIdArray[selectedIndex],
        name: categoryNameArray[selectedIndex],
      };

      // const beforeSelected = categoryIdArray.slice(0, selectedIndex).map((id: any, index: number) => ({ id, name: categoryNameArray[index] }));
      // const afterSelected = categoryIdArray.slice(selectedIndex + 1).map((id: any, index: number) => ({ id, name: categoryNameArray[index + selectedIndex + 1] }));

      return categoryNameArray.map((name: string, index: number) => ({
        id: categoryIdArray[index],
        name,
      }));
    } else {
      return categoryNameArray.map((name: string, index: number) => ({
        id: categoryIdArray[index],
        name,
      }));
    }
  })();

  const [showMore, setShowMore] = useState(false);

  const showItems = showMore
    ? reorderedCategories
    : reorderedCategories.slice(0, 5);

  const handleShowMore = () => {
    setShowMore(!showMore);
  };

  const handleClick = (category: any) => {
    // Pass the clicked category back to the parent component
    props.onHandleClick(category);
  };

  const handleShippingChange = (checkedValues: any) => {
    // Handle the shipping time filter changes
    console.log("Shipping Time Filters:", checkedValues);
    // Implement further logic based on selected checkboxes
  };

  const handleStockChange = (checkedValues: any) => {
    // Handle the shipping time filter changes
    console.log("Shipping Time Filters:", checkedValues);
    // Implement further logic based on selected checkboxes
    props.onHandleStockChange(checkedValues);
  };

  const { Group } = Input;

  const handlePriceChange = (value: any) => {
    // Handle price range changes
    console.log("Price Range:", value);
    // Implement further logic based on the selected range
    props.onHandlePriceChange(value);
  };

  return (
    <div>
      {/* All Categories */}
      <div className="panel">
        <h2 className="underline font-bold pb-3">All Categories</h2>
        <div className="panel-container">
          {showItems.map((category: any) => {
            const isSelected = category.id === selectedCategory;
            const highlightClass = isSelected ? "highlight-category" : "";

            return (
              <div
                key={category.id}
                className={`panel-item ${highlightClass}`}
                onClick={() => handleClick(category)}
              >
                {category.name}
              </div>
            );
          })}
        </div>
        {categoryNameArray.length > 5 && (
          <button onClick={handleShowMore}>
            {showMore ? "Show Less" : "Show More"}
          </button>
        )}
      </div>

      {/* Search Filters */}
      <div className="panel">
        <h2 className="underline font-bold pb-3">Search Filters</h2>
        {/* Sub-modules: Shipping Time */}
        {/* <div className="sub-panel">
          <h4 className="underline pb-2">Shipping Time</h4>
          <Checkbox.Group onChange={handleShippingChange}>
            <Checkbox value="within1day">Within 1 day</Checkbox>
            <Checkbox value="1to2days">1-2 days</Checkbox>
            <Checkbox value="3to5days">3-5 days</Checkbox>
            <Checkbox value="5to7days">5-7 days</Checkbox>
            <Checkbox value="moreThanAWeek">More than a week</Checkbox>
          </Checkbox.Group>
        </div> */}
        {/* Sub-modules: Price Range */}
        <div className="sub-panel">
          <h4 className="underline pb-2">Price Range</h4>
          <Row>
            <Input
              style={{ width: "100px", textAlign: "center" }}
              placeholder="Min"
              ref={inputMinRef}
              onChange={(e) =>
                handlePriceChange([
                  e.target.value,
                  inputMaxRef?.current?.input?.value || 0,
                ])
              }
            />
            <Input
              style={{ width: "100px", textAlign: "center" }}
              placeholder="Max"
              ref={inputMaxRef}
              onChange={(e) =>
                handlePriceChange([
                  inputMinRef?.current?.input?.value || 0,
                  e.target.value,
                ])
              }
            />
          </Row>
        </div>
        {/* Sub-modules: Stock Type */}
        <div className="sub-panel">
          <h4 className="underline pb-2">Stock Type</h4>
          <Checkbox.Group onChange={handleStockChange}>
            <Checkbox value="PREORDER">Pre-Order Stock</Checkbox>
            <Checkbox value="READYSTOCK">Ready Stock</Checkbox>
          </Checkbox.Group>
        </div>
        {/* Sub-modules: Brand */}
        {/* <div className="sub-panel">
          <h4 className="underline pb-2">Brand</h4> */}
        {/* Implement brand filter */}
        {/* Example: */}
        {/* <Input.Search placeholder="Search Brand" /> */}
        {/* Brand list with checkboxes */}
        {/* <Checkbox.Group onChange={onChange}>
            <Checkbox value="Brand1">Brand 1</Checkbox>
            <Checkbox value="Brand2">Brand 2</Checkbox> */}
        {/* Add more checkboxes as needed */}
        {/* </Checkbox.Group> */}
        {/* </div> */}
      </div>
    </div>
  );
};

export const TopSideBar = (props: any) => {
  const router = useRouter();
  const paramCategory: any = router.query;
  const inputMinRef = useRef<InputRef>(null);
  const inputMaxRef = useRef<InputRef>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [IsPromotionProducts, setIsPromotionProducts] =
    useState<boolean>(false);
  const [filterStockTypeValue, setFilterStockTypeValue] = useState<string>("");
  const categoryNameArray = props.categories.map((item: any) => item.name);
  const categoryIdArray = props.categories.map((item: any) => item.id);

  const [selectedStockTypes, setSelectedStockTypes] = useState<
    CheckboxValueType[]
  >([]);

  const [categoryVisible, setCategoryVisible] = useState(false);
  const [priceVisible, setPriceVisible] = useState(false);
  const [stockTypeVisible, setStockTypeVisible] = useState(false);

  useEffect(() => {
    setSelectedCategory(
      "category" in paramCategory ? _.capitalize(paramCategory?.category) : ""
    );
    if (localStorage.getItem("stockType")) {
      let localStorageStockType: any = [localStorage.getItem("stockType")];
      if (localStorage.getItem("stockType") === "PROMOTION") {
        setIsPromotionProducts(true);
        setSelectedStockTypes(["PROMOTION"]);
      }
      if (localStorage.getItem("stockType") !== "PROMOTION") {
        setSelectedStockTypes(localStorageStockType);
        setFilterStockTypeValue(localStorage.getItem("stockType") ?? "");
      }
    }
  }, [router.isReady]);

  useEffect(() => {
    const handleScroll = () => {
      setPriceVisible(false); // Close popover on scroll
      setCategoryVisible(false); // Close popover on scroll
      setStockTypeVisible(false); // Close popover on scroll
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const handleCategoryChange = (e: any) => {
    const categoryName = e.item.props.children;
    setSelectedCategory(categoryName);
    props.onHandleClick({ id: e.key, name: categoryName });
  };

  const handlePriceChange = () => {
    const minPrice = inputMinRef.current?.input?.value || 0;
    const maxPrice = inputMaxRef.current?.input?.value || 0;
    props.onHandlePriceChange([minPrice, maxPrice]);
  };

  const handleStockChange = (value: CheckboxValueType[]) => {
    setSelectedStockTypes(value);
    props.onHandleStockChange(value);
  };

  const priceContent = (
    <div className="flex items-center">
      <Input
        className="w-24 text-center"
        placeholder="Min"
        ref={inputMinRef}
        onChange={handlePriceChange}
      />
      <span className="px-2">-</span>
      <Input
        className="w-24 text-center"
        placeholder="Max"
        ref={inputMaxRef}
        onChange={handlePriceChange}
      />
    </div>
  );

  return (
    <div className="flex flex-wrap justify-start w-full sm:w-4/5 sm:mx-auto gap-x-3 px-2">
      <div>
        <Popover
          className="capitalize"
          content={
            <Menu style={{ overflow: "hidden" }} onClick={handleCategoryChange}>
              {props.categories.map((category: any) => (
                <Menu.Item className="capitalize" key={category.id}>
                  {category.name ? category.name.toLowerCase() : ""}
                </Menu.Item>
              ))}
            </Menu>
          }
          trigger="click"
          placement="bottom"
          visible={categoryVisible}
          onVisibleChange={setCategoryVisible}
        >
          <Button
            disabled={IsPromotionProducts}
            className="rounded-lg flex items-center font-bold bg-buttonOrange text-white border-none .button-themeColor"
          >
            {selectedCategory ? selectedCategory : "Product Category"}
            <CaretDownOutlined />
          </Button>
        </Popover>
      </div>

      <div>
        <Popover
          content={priceContent}
          trigger="click"
          visible={priceVisible}
          onVisibleChange={setPriceVisible}
          placement="bottom"
        >
          <Button
            disabled={IsPromotionProducts}
            className="rounded-lg flex items-center font-bold bg-buttonOrange text-white border-none .button-themeColor"
            onClick={() => setPriceVisible(!priceVisible)}
          >
            Price Range <CaretDownOutlined />
          </Button>
        </Popover>
      </div>

      <div>
        <Popover
          content={
            <div className="p-2 flex flex-col">
              <Checkbox.Group
                onChange={handleStockChange}
                value={selectedStockTypes}
                className="Flex flex-col gap-2"
              >
                <Checkbox
                  disabled={
                    (filterStockTypeValue === "" && !IsPromotionProducts) ||
                    filterStockTypeValue === "PREORDER"
                      ? false
                      : true
                  }
                  value="PREORDER"
                  onChange={(val) => {
                    if (val.target.checked) {
                      setFilterStockTypeValue(val.target.value);
                    } else if (!val.target.checked) {
                      setFilterStockTypeValue("");
                    }
                  }}
                >
                  Pre-Order Stock
                </Checkbox>
                <Checkbox
                  disabled={
                    (filterStockTypeValue === "" && !IsPromotionProducts) ||
                    filterStockTypeValue === "READYSTOCK"
                      ? false
                      : true
                  }
                  value="READYSTOCK"
                  onChange={(val) => {
                    if (val.target.checked) {
                      setFilterStockTypeValue(val.target.value);
                    } else if (!val.target.checked) {
                      setFilterStockTypeValue("");
                    }
                  }}
                >
                  Ready Stock
                </Checkbox>
                <Checkbox
                  disabled={
                    (filterStockTypeValue === "" && !IsPromotionProducts) ||
                    filterStockTypeValue === "HOTSELLING"
                      ? false
                      : true
                  }
                  value="HOTSELLING"
                  onChange={(val) => {
                    if (val.target.checked) {
                      setFilterStockTypeValue(val.target.value);
                    } else if (!val.target.checked) {
                      setFilterStockTypeValue("");
                    }
                  }}
                >
                  Hot Selling
                </Checkbox>
                <Checkbox
                  disabled={
                    (filterStockTypeValue === "" && !IsPromotionProducts) ||
                    filterStockTypeValue === "NEWPRODUCT"
                      ? false
                      : true
                  }
                  value="NEWPRODUCT"
                  onChange={(val) => {
                    if (val.target.checked) {
                      console.log("val.target.checked: ", val.target.checked);
                      setFilterStockTypeValue(val.target.value);
                    } else if (!val.target.checked) {
                      setFilterStockTypeValue("");
                    }
                  }}
                >
                  New Product
                </Checkbox>
                <Checkbox
                  disabled={filterStockTypeValue === "" ? false : true}
                  value="PROMOTION"
                  onChange={(val) => setIsPromotionProducts(val.target.checked)}
                >
                  Promotion
                </Checkbox>
              </Checkbox.Group>
            </div>
          }
          trigger="click"
          placement="bottom"
          autoAdjustOverflow={{ adjustX: 0, adjustY: 0 }}
          visible={stockTypeVisible}
          onVisibleChange={setStockTypeVisible}
        >
          <Button className="rounded-lg flex items-center font-bold bg-buttonOrange text-white border-none .button-themeColor">
            {selectedStockTypes.length
              ? selectedStockTypes.length > 1
                ? "Pre-Order Stock, Ready Stock"
                : selectedStockTypes.includes("PREORDER")
                ? "Pre-Order Stock"
                : selectedStockTypes.includes("READYSTOCK")
                ? "Ready Stock"
                : selectedStockTypes.includes("HOTSELLING")
                ? "Hot Selling"
                : selectedStockTypes.map((type: any) => _.capitalize(type))
              : "Stock Type"}
            <CaretDownOutlined />
          </Button>
        </Popover>
      </div>
    </div>
  );
};
