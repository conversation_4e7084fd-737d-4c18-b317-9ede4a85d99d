import React, { useEffect, useState } from "react";
import { Content } from "antd/lib/layout/layout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import { Button, Drawer, Form, MenuProps, Row, Tooltip } from "antd";
import Header, { supportedLocales } from "../../components/header";
import {
  BackButtonUI,
  PrimaryButtonUI,
  SecondaryButtonUI,
} from "@/components/buttonUI";
import { useRouter } from "next/router";
import { ArrowUpOutlined, EyeOutlined } from "@ant-design/icons";
import { ListingTableUI, MessageInfoUI, statusApproval } from "@/components/ui";
import {
  DataSource,
  PicSignedUrl,
  encodeParams,
  NumberThousandSeparator,
  formateDateAndTime,
  getParamsFromLocalStorage,
  setFilterForm,
  setParamsFromLocalStorage,
} from "@/stores/utilize";
import {
  CreditNote,
  CreditNoteDocument,
  DebitNote,
  DebitNoteDocument,
  Invoice,
  InvoiceDocument,
  Outlet,
  Payment,
  PaymentDetail,
  Retailer,
  SelectOption,
  Staff,
  User,
} from "@/components/type";
import moment from "moment";
import {
  FormTextInput,
  SelectInput,
  SingleDateInput,
} from "@/components/input";
import { ModalUI } from "@/components/modalUI";
import useRetailerStore from "@/stores/store";
import { getRetailerData } from "@/stores/authContext";
import {
  paymentMethodOption,
  paymentStatusFilterOption,
} from "@/components/config";
import FilterFormComponent from "@/components/filter";
import AppFooter from "@/components/footer";
import { PDFDocument } from "pdf-lib";
import apiHelper from "../api/apiHelper";

function PaymentListing() {
  const { t } = useTranslation("common");
  const router = useRouter();
  const [filterForm] = Form.useForm();
  const [filterModalForm] = Form.useForm();
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [showButtonLoader, setShowButtonLoader] = useState(false);
  const [cursor, setCursor] = useState("");
  const [tempCursor, setTempCursor] = useState("");
  const [tableLoading, setTableLoading] = useState(false);
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({});
  const [data, setData] = useState<any[]>([]);
  const [outletMap, setOutletMap] = useState(new Map());
  const [invoiceMap, setInvoiceMap] = useState(new Map());
  const [creditNoteMap, setCreditNoteMap] = useState(new Map());
  const [debitNoteMap, setDebitNoteMap] = useState(new Map());
  const [statementMap, setStatementMap] = useState(new Map());
  const [staffMap, setStaffMap] = useState(new Map());
  const [retailerMap, setRetailerMap] = useState(new Map());
  const [userMap, setUserMap] = useState(new Map());

  const [showReceipt, setShowReceipt] = useState(false);
  const [receipt, setReceipt] = useState<Payment>({});
  const [receiptImage, setReceiptImage] = useState<{ [key: string]: string }>();

  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRowData, setSelectedRowData] = useState<any[]>([]);
  const [pdfLoading, setPDFLoading] = useState(false);
  // ======================================================================
  // filter function - state()
  // ======================================================================
  const [showClearFilter, setShowClearFilter] = useState(false);
  const [fuzzySearchFilter, setFuzzySearchFilter] = useState("");
  const [modalFilter, setModalFilter] = useState<any>({});
  const [filterSetting, setFilterSetting] = useState("");
  const [statusValue, setStatusValue] = useState("All");
  const [statusKey, setStatusKey] = useState("ALL");
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [fieldName, setFieldName] = useState("");

  const headerItems = [
    {
      label: t("Header.dashboard"),
      route: "/profile/dashboard",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("Header.paymentHistory"),
      route: "/payment/paymentListing",
      className: "labelTextStyle",
    },
  ];

  useEffect(() => {
    // Function to check screen size and update state
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth <= 950); // Define your breakpoint for small screens
    };

    // Add event listener for window resize
    window.addEventListener("resize", handleResize);

    // Call handleResize initially to set initial screen size
    handleResize();

    // Clean up the event listener on component unmount
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollButton(window.pageYOffset > 0);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    getPayment();
  }, []);

  useEffect(() => {
    if (Object.keys(useRetailerStore.getState()).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }
    }
  }, [Object.keys(useRetailerStore.getState()).length]);

  useEffect(() => {
    if (retailerAccess && Object.keys(retailerAccess).length > 0) {
      // getPayment();
      // getProductData();

      // Get from localStorage
      const filterParams: any = getParamsFromLocalStorage(
        router.pathname,
        "paymentFilter"
      );

      const filterKey: any = {
        fuzzySearch: "",
        receiptNo: null,
        date: "",
        debitNoteNo: null,
        creditNoteNo: null,
        invoiceNo: null,
        // outletName: null,
        // outletCode: null,
      };

      const clonedFilterKey = { ...filterKey };
      delete clonedFilterKey.fuzzySearch;

      Object.keys(clonedFilterKey).forEach((key) => {
        const capitalizedKey = key
          .split(/(?=[A-Z])|\s+/)
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
        clonedFilterKey[capitalizedKey] = clonedFilterKey[key];
        delete clonedFilterKey[key];
      });

      const keysAsString: string = Object.keys(clonedFilterKey).join(", ");
      setFieldName(keysAsString);

      if (filterParams) {
        // Initialize variables to store the values
        // Follow search params' key

        setFilterSetting(filterParams);
        setFilterForm(filterParams, filterKey);
        filterForm.setFieldValue(["fuzzySearch"], filterKey.fuzzySearch);
        filterModalForm.setFieldsValue(filterKey);
        setFuzzySearchFilter(filterKey.fuzzySearch);

        let data = {
          receiptNo: filterKey.receiptNo || "",
          debitNoteNo: filterKey.debitNoteNo || "",
          creditNoteNo: filterKey.creditNoteNo || "",
          invoiceNo: filterKey.invoiceNo || "",
          date: filterKey.date ? moment(filterKey.date) : filterKey.date,
          // outletCode: filterKey.outletCode || "",
          // outletName: filterKey.outletName || "",
        };
        setModalFilter(data);
        setStatusKey(filterKey.status ?? "ALL");
        const filterStatusLabel: any = paymentStatusFilterOption.find(
          (item: any) => item.key === filterKey.status
        )?.label;
        setStatusValue(filterStatusLabel ?? "All");
      } else {
        getPayment(true);
      }
    }
  }, [retailerAccess]);

  useEffect(() => {
    const data = {
      fuzzySearch: fuzzySearchFilter || "",
      receiptNo: modalFilter.receiptNo || "",
      debitNoteNo: modalFilter.debitNoteNo || "",
      creditNoteNo: modalFilter.creditNoteNo || "",
      invoiceNo: modalFilter.invoiceNo || "",
      date: modalFilter.date ? moment(modalFilter.date) : modalFilter.date,
      status: statusKey === "ALL" ? "" : statusKey,
    };

    const allPropertiesEmpty = Object.values(data).every(
      (value) => value === ""
    );
    if (!allPropertiesEmpty) {
      searchPayment(data);
    } else {
      setFilterSetting("");
    }
  }, [fuzzySearchFilter, statusKey, modalFilter]);

  useEffect(() => {
    if (Object.keys(retailerAccess).length) {
      // Check params whether is same
      const filterParams = getParamsFromLocalStorage(
        router.pathname,
        "paymentFilter"
      );
      if (filterSetting) {
        getPayment(true, false);

        if (filterSetting !== filterParams) {
          setParamsFromLocalStorage(
            router.pathname,
            filterSetting,
            "paymentFilter"
          );
        }
        setShowClearFilter(true);
      } else {
        setShowClearFilter(false);
        if (data.length > 0) {
          localStorage.removeItem("paymentFilter");
        }
        getPayment(true, false);
      }
    }
  }, [filterSetting, retailerAccess]);

  // *************************************************************************************
  // *** Scolling Function - useEffect ***
  // *************************************************************************************
  // Check scrolling position
  useEffect(() => {
    if (cursor === "") return;
    const handleScroll = () => {
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight - 50;
      const scrollPosition = window.scrollY;
      if (windowHeight + scrollPosition >= documentHeight) {
        // Stop API calling when cursor is equal to '0'
        if (cursor !== "0") {
          getPayment();
        }
      }
    };
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [cursor]);

  const handleScrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  let isLoading = false;

  const getPayment = (isRefresh = false, isClearFilter = false) => {
    // const accessBranches = otherViewBranchAccess === false ? retailerAccess.companyBranchIds : otherCompanyBranchesIds;
    let currentOutletId = localStorage.getItem("currentOutletId");
    if (isLoading) return;
    setTableLoading(true);
    setShowButtonLoader(true);
    isLoading = true;
    setTimeout(() => {
      let params: any = {
        sort: "createdAt",
        sortOrder: "-1",
        companyId: retailerAccess.companyId,
        // companyBranchId: retailerAccess.companyBranchId,
        outletId: currentOutletId,
      };

      if (isRefresh === false) {
        params.cursor = cursor;
      }

      // if (!isAdmin) {
      //   params.companyBranchIds = accessBranches;
      // }
      // const checkAdminRights = isAdmin ? params : params + "&companyId=" + retailerAccess.companyId;
      // const checkFilterRights = filterSetting && !isClearFilter ? filterSetting : encodeParams(params);
      const checkFilterRights =
        filterSetting && !isClearFilter
          ? filterSetting +
          (cursor && cursor !== "0" ? `&cursor=${cursor}` : "")
          : encodeParams(params);
      const dataSource = new DataSource("payments", checkFilterRights, false);

      // !isAdmin && !otherViewBranchAccess ? filterForm.setFieldValue("companyId", retailerAccess.companyId) : null;

      // if ((accessBranches && accessBranches.length > 0) || isAdmin) {
      dataSource
        .load()
        .then(async (res: any) => {
          if (res && res.items !== null) {
            // res.items = res.items.filter(
            //   (item: Payment) => item.paymentProcessStatus !== "UNVERIFIED"
            // );
            if (res.items !== null) {
              res.items.map((item: any) => {
                let amount = 0;
                let method = "";
                if (item.paymentDetails !== undefined) {
                  item.paymentDetails.map((payment: PaymentDetail) => {
                    if (payment.amount !== undefined) {
                      amount += payment.amount;
                    }
                    if (payment.type !== undefined) {
                      let paymentType = paymentMethodOption.find(
                        (item: any) => item.value === payment.type
                      )?.label;
                      if (paymentType !== undefined) {
                        if (method === "") {
                          method = paymentType;
                        } else {
                          method += ", " + paymentType;
                        }
                      }
                    }
                    // item.status = item.paymentProcessStatus;
                  });
                  item.amount = amount;
                  item.method = method;
                }
              });
            }
            let data = res.items;
            const objectMap = data.reduce(
              (accumulator: any, current: Payment) => {
                accumulator["outletId"] = accumulator["outletId"] || [];
                if (
                  current.outletId &&
                  !outletMap.has(current.outletId) &&
                  !accumulator["outletId"].includes(current.outletId)
                ) {
                  accumulator["outletId"].push(current.outletId ?? "");
                }

                accumulator["staffId"] = accumulator["staffId"] || [];
                if (
                  current.staffId &&
                  !staffMap.has(current.staffId) &&
                  !accumulator["staffId"].includes(current.staffId)
                ) {
                  accumulator["staffId"].push(current.staffId ?? "");
                }

                accumulator["retailerId"] = accumulator["retailerId"] || [];
                if (
                  current.retailerId &&
                  !retailerMap.has(current.retailerId) &&
                  !accumulator["retailerId"].includes(current.retailerId)
                ) {
                  accumulator["retailerId"].push(current.retailerId ?? "");
                }

                accumulator["userId"] = accumulator["userId"] || [];
                if (
                  current.userId &&
                  !userMap.has(current.userId) &&
                  !accumulator["userId"].includes(current.userId)
                ) {
                  accumulator["userId"].push(current.userId ?? "");
                }

                current.invoiceDocuments?.reduce(
                  (acc: any, product: InvoiceDocument) => {
                    accumulator["invoiceId"] = accumulator["invoiceId"] || [];
                    if (
                      product.invoiceId &&
                      !invoiceMap.has(product.invoiceId) &&
                      !accumulator["invoiceId"].includes(product.invoiceId)
                    ) {
                      accumulator["invoiceId"].push(product.invoiceId ?? "");
                    }

                    accumulator["statementId"] =
                      accumulator["statementId"] || [];
                    if (
                      product.statementId &&
                      !statementMap.has(product.statementId) &&
                      !accumulator["statementId"].includes(product.statementId)
                    ) {
                      accumulator["statementId"].push(
                        product.statementId ?? ""
                      );
                    }

                    return acc;
                  },
                  {}
                );

                current.creditNoteDocuments?.reduce(
                  (acc: any, product: CreditNoteDocument) => {
                    accumulator["creditNoteId"] =
                      accumulator["creditNoteId"] || [];
                    if (
                      product.creditNoteId &&
                      !creditNoteMap.has(product.creditNoteId) &&
                      !accumulator["creditNoteId"].includes(
                        product.creditNoteId
                      )
                    ) {
                      accumulator["creditNoteId"].push(
                        product.creditNoteId ?? ""
                      );
                    }

                    return acc;
                  },
                  {}
                );

                current.debitNoteDocuments?.reduce(
                  (acc: any, product: DebitNoteDocument) => {
                    accumulator["debitNoteId"] =
                      accumulator["debitNoteId"] || [];
                    if (
                      product.debitNoteId &&
                      !debitNoteMap.has(product.debitNoteId) &&
                      !accumulator["debitNoteId"].includes(product.debitNoteId)
                    ) {
                      accumulator["debitNoteId"].push(
                        product.debitNoteId ?? ""
                      );
                    }

                    return acc;
                  },
                  {}
                );

                return accumulator;
              },
              {}
            );

            getOutlets(objectMap["outletId"]);
            getInvoice(objectMap["invoiceId"]);
            getCreditNote(objectMap["creditNoteId"]);
            getDebitNote(objectMap["debitNoteId"]);
            getStatement(objectMap["statementId"]);
            // getCompanyBranch(objectMap["companyBranchId"]);
            const staff = await getStaff(objectMap["staffId"]);
            const retailer = await getRetailer(objectMap["retailerId"]);
            const user = await getUser(objectMap["userId"]);

            // create new key for display name and applicant type
            data.map((item: Payment) => {
              if (item.staffId && item.staffId !== "") {
                item.applicantNameNType = `Staff | ${staff.get(item.staffId)?.lastName ?? ""
                  } ${staff.get(item.staffId)?.firstName ?? ""}`;
              }

              if (item.retailerId && item.retailerId !== "") {
                item.applicantNameNType = `Retailer | ${retailer.get(item.retailerId)?.lastName ?? ""
                  } ${retailer.get(item.retailerId)?.firstName ?? ""}`;
              }

              if (item.userId && item.userId !== "") {
                item.applicantNameNType = `Admin | ${user.get(item.userId)?.lastName ?? ""
                  } ${user.get(item.userId)?.firstName ?? ""}`;
              }

              return item;
            });

            const nextCursor = res.cursor; // Get the cursor from the last item in the response
            if (nextCursor !== cursor || isRefresh) {
              // Avoid duplicates
              if (!isRefresh) {
                // setFullData((prevData) => [...prevData, ...data]);
                setData((prevData) => [...prevData, ...data]);
                setTableLoading(false);
                setShowButtonLoader(false); //path to check
              } else {
                // setFullData([...data]);
                setData([...data]);
                setTableLoading(false);
                setShowButtonLoader(false); //path to check
              }
              // cursor = nextCursor;
              setCursor(nextCursor);
              setTempCursor(nextCursor);
            }
          }
          isLoading = false;
        })
        .catch(() => {
          isLoading = false;
          //* This Part need re-edit*//
        });
      // } else {
      //   setGoodsReturnListingInfoData([]);
      // }
    }, 500);
  };

  const getOutlets = async (id: string[] = []) => {
    let tempProductMap = new Map(outletMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      //   status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "outlets",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });
        if (res !== null && res.items.length > 0) {
          setOutletMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Outlet) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Outlet) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getInvoice = async (id: string[] = []) => {
    let tempProductMap = new Map(invoiceMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      // status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "invoices",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setInvoiceMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Invoice) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Invoice) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getCreditNote = async (id: string[] = []) => {
    let tempProductMap = new Map(creditNoteMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      // status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "creditNotes",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setCreditNoteMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: CreditNote) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: CreditNote) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getDebitNote = async (id: string[] = []) => {
    let tempProductMap = new Map(debitNoteMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      // status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "debitNotes",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setDebitNoteMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: DebitNote) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: DebitNote) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getStatement = async (id: string[] = []) => {
    let tempProductMap = new Map(statementMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      // status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "statements",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setStatementMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: DebitNote) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: DebitNote) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getStaff = async (id: string[] = []) => {
    let tempStaffMap = new Map(staffMap);
    if (!id?.length) return tempStaffMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "staffs",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setStaffMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Staff) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Staff) => {
            tempStaffMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempStaffMap;
    }
    return tempStaffMap;
  };

  const getRetailer = async (id: string[] = []) => {
    let tempRetailerMap = new Map(retailerMap);
    if (!id?.length) return tempRetailerMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "retailers",
          encodeParams(params),
          false,
          "v2"
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setRetailerMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Retailer) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Retailer) => {
            tempRetailerMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempRetailerMap;
    }
    return tempRetailerMap;
  };

  const getUser = async (id: string[] = []) => {
    let tempUserMap = new Map(userMap);
    if (!id?.length) return tempUserMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource("users", encodeParams(params), false);
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setUserMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: User) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: User) => {
            tempUserMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempUserMap;
    }
    return tempUserMap;
  };

  const column = [
    {
      width: 150,
      title: t("Payment.applicantName"),
      dataIndex: "applicantNameNType",
      // onFilter: (value: string, record: any) =>
      //   record.applicantNameNType.indexOf(value) === 0,
      sorter: (a: any, b: any) =>
        a?.applicantNameNType?.localeCompare(b?.applicantNameNType),
      showSorterTooltip: false,
      key: "applicantNameNType",
      render: (_: any, record: Payment) => {
        return (
          <p className="tableRowNameDesign">
            {record.applicantNameNType ? (
              record.applicantNameNType
            ) : (
              <p className="tableRowNameDesign text-center">—</p>
            )}{" "}
          </p>
        );
      },
    },
    {
      title: t("Payment.receiptNo"),
      dataIndex: "receiptNo",

      key: "receiptNo",
      render: (_: any, record: Payment) => {
        return <p className="tableRowNameDesign">{record.receiptNo}</p>;
      },
    },
    {
      // Statement Date
      title: t("Payment.paymentDate"),
      dataIndex: "date",

      key: "date",
      render: (_: any, record: Payment) => {
        return (
          <p className="tableRowNameDesign">
            {formateDateAndTime(record.date)}
          </p>
        );
      },
    },
    {
      // Statement Amount
      title: t("Payment.paymentAmount"),
      dataIndex: "amount",
      key: "amount",
      render: (_: any, record: Payment) => {
        return (
          <p className="tableRowNameDesign">
            {NumberThousandSeparator(record.amount ?? 0)}
          </p>
        );
      },
    },
    {
      // Payment Method
      title: t("Payment.method"),
      dataIndex: "method",
      key: "method",

      render: (_: any, record: Payment) => {
        let paymentMethod = record.method;
        if (
          record.creditNoteDocuments &&
          record.creditNoteDocuments.length > 0
        ) {
          paymentMethod = paymentMethod
            ? paymentMethod + ", Credit Note"
            : "Credit Note";
        }

        return (
          <a
            className="tableRowNameDesign underline"
            onClick={() => {
              processImage(record);
              setReceipt(record);
              setShowReceipt(true);
            }}
          >
            {paymentMethod}
          </a>
        );
      },
    },
    {
      // Payment Status
      title: t("Status"),
      dataIndex: "paymentApprovalStatus",
      width: 250,
      key: "paymentApprovalStatus",
      render: (_: any, value: any) => {
        return statusApproval(value);
      },
    },
    {
      // Action
      title: t("Action"),
      dataIndex: "action",
      key: "action",
      fixed: "right",
      width: 100,
      render: (_: any, value: any) => {
        return (
          <div className="flex">
            <Button
              type="link"
              onClick={() => {
                router.push("/payment/viewPayment?id=" + value.id);
              }}
              className="flex items-center  text-xs ml-0 p-2"
            >
              <Tooltip title={t("ViewMore")}>
                <EyeOutlined style={{ color: "green" }} />
              </Tooltip>
            </Button>
          </div>
        );
      },
    },
  ];

  const rowSelection = {
    onChange: (selectedRowKeys: string[], selectedRows: []) => {
      setSelectedRowData(selectedRows);
      setSelectedRowKeys(selectedRowKeys);
    },

    getCheckboxProps: (record: { disabled: any; status: any }) => {
      // if (selectedRowData && selectedRowData.length > 0) {
      //   if (regexPattern.test(selectedRowData[0]?.status)) {
      //     return {
      //       disabled: !regexPattern.test(record.status),
      //     };
      //   }
      // }
      // return {
      //   disabled: !regexPattern.test(record.status),
      // };
    },
  };

  const processReceiptItem = async (url: string) => {
    try {
      const res: any = await PicSignedUrl(url);
      return res;
    } catch (error) {
      return "";
    }
  };

  const processImage = async (receipt: Payment) => {
    const paymentDetails = receipt?.paymentDetails || [];
    let imageMap: any = {};
    for (const detailsItem of paymentDetails) {
      if (
        detailsItem.receipt !== undefined &&
        detailsItem.receipt !== null &&
        detailsItem.receipt.length > 0
      ) {
        const processReceiptItems = await detailsItem?.receipt?.reduce(
          async (allocatedPromise: Promise<any>, item: any) => {
            const allocated = await allocatedPromise;
            const processedUrl: string = await processReceiptItem(item);
            if (processedUrl) {
              allocated[item] = processedUrl;
            }
            return allocated;
          },
          Promise.resolve({})
        );

        imageMap = Object.assign(imageMap, processReceiptItems);
      }
    }
    setReceiptImage(imageMap);
  };

  const generatePDF = async () => {
    setPDFLoading(true);

    try {
      const pdfDoc = await PDFDocument.create();
      let numberPagePrinted = 0;

      for (const paymentData of selectedRowData) {
        let donorPdfBytes;

        let res: any = await apiHelper.GET("payment/pdf?id=" + paymentData.id);
        numberPagePrinted += 1;

        if (res) {
          const documentSigned: any = await PicSignedUrl(res.item); //need to remove after confirmed
          const pdfBytesResponse = await fetch(documentSigned);
          donorPdfBytes = await pdfBytesResponse.arrayBuffer();

          const donorPdfDoc = await PDFDocument.load(donorPdfBytes);

          // Copy all pages from donorPdfDoc to pdfDoc
          const donorPages = await pdfDoc.copyPages(
            donorPdfDoc,
            donorPdfDoc.getPageIndices()
          );
          donorPages.forEach((page: any) => pdfDoc.addPage(page));
        }
      }
      if (numberPagePrinted === 0) {
        MessageInfoUI("No Valid Document(s) found");
        return;
      }
      if (numberPagePrinted > 0) {
        MessageInfoUI(numberPagePrinted + " Valid Document(s) Printed");
      }

      // Convert the merged PDF to a blob
      const mergedPdfBytes = await pdfDoc.save();

      // Create a blob URL and open it in a new tab
      const blob = new Blob([mergedPdfBytes], { type: "application/pdf" });
      const blobUrl = URL.createObjectURL(blob);
      window.open(blobUrl, "_blank");
    } catch (error) {
      console.error(error);
      setPDFLoading(false);
    }

    setPDFLoading(false);
  };

  const buttons = [
    {
      label: t("Payment.printOR"),
      onClick: generatePDF,
      disabled: !(selectedRowData.length > 0),
    },
  ];

  // *************************************************************************************
  // *** Filter Modal ***
  // *************************************************************************************

  const filterFormOnfinish = (values: any) => {
    setModalFilter(values);
  };

  const searchPayment = (values: any) => {
    //convert to empty string when no value is entered as default value is undefined.
    for (const key in values) {
      if (values[key] === undefined) {
        values[key] = "";
      }
    }

    // return true or false
    let isAnyKeyFilled = Object.keys(values).some(
      (key) => values[key] !== "" && values[key] !== undefined
    );

    let currentOutletId = localStorage.getItem("currentOutletId");

    const params =
      encodeParams({
        companyId: retailerAccess.companyId,
        // companyBranchId: retailerAccess.companyBranchId,
        outletId: currentOutletId,
        fuzzySearch: values.fuzzySearch,
        debitNoteNo: values.debitNoteNo || "",
        invoiceNo: values.invoiceNo || "",
        // outletId:
        //   values.outletCode && values.outletName
        //     ? [values.outletCode, values.outletName]
        //     : values.outletCode || values.outletName || "",
        productId:
          values.productSku && values.productName
            ? [values.productSku, values.productName]
            : values.productSku || values.productName || "",
        status: values.status,
      }) + "&sort=createdAt&sortOrder=-1";

    if (isAnyKeyFilled) {
      setCursor("0");
      setFilterSetting(params);
    }
  };

  const handleStatusMenuClick: MenuProps["onClick"] = ({ key }) => {
    const items = paymentStatusFilterOption;
    setStatusKey(key);
    // Access the label property of the selected item
    const selectedLabel = items.find(
      (menuItem: any) => menuItem.key === key
    )?.label;
    if (selectedLabel) {
      setStatusValue(selectedLabel);
    }
  };

  const filterModal = () => {
    return (
      <div className="w-full">
        <Form
          onFinish={filterFormOnfinish}
          form={filterModalForm}
          className=""
          layout="vertical"
        >
          <h1 className="font-bold text-base pb-4 hidden sm:flex">
            {t("Filter")}
          </h1>
          {/* First Row of Filter Input */}
          <Row className="filterBlockForm flex-col space-y-3">
            <Row className="flex sm:flex-row flex-col gap-x-4 sm:gap-y-0 gap-y-4">
              <Form.Item
                name="receiptNo"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") + " " + t("Payment.receiptNo") + "?"}
                  </p>
                }
              >
                <FormTextInput
                  placeholder={t("Common.eg") + " " + t("Payment.receiptNo")}
                  maxLength={30}
                />
              </Form.Item>
              <Form.Item
                name="date"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") + " " + t("Payment.date") + "?"}
                  </p>
                }
              >
                <SingleDateInput
                  placeholder={t("Common.eg") + " " + t("Payment.date")}
                  onChange={() => {
                    filterModalForm.submit();
                  }}
                />
              </Form.Item>
            </Row>
            <Row className="flex sm:flex-row flex-col gap-x-4 sm:gap-y-0 gap-y-4">
              <Form.Item
                name="method"
                className="flex-1 mb-0"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") + " " + t("Payment.method") + "?"}
                  </p>
                }
              >
                <SelectInput
                  placeholder={t("Common.eg") + " " + t("Payment.method")}
                  options={paymentMethodOption}
                />
              </Form.Item>
              <Form.Item name="" className="flex-1 mb-0"></Form.Item>
            </Row>
          </Row>
          <Row className="flex pt-8 justify-between">
            <PrimaryButtonUI
              label={t("Common.resetAll")}
              onClick={() => {
                filterModalForm.resetFields();
                // setParamsFromLocalStorage(router.pathname, `status=${statusKey}`, "productFilter");
              }}
            />
            {/* <Row> */}
            <SecondaryButtonUI
              label={t("Common.cancel")}
              htmlType="reset"
              onClick={() => {
                setModalFilter({});
                setIsFilterModalOpen(false);
              }}
            />
            <PrimaryButtonUI
              label={t("Common.applyFilter")}
              htmlType="submit"
              onClick={() => {
                setIsFilterModalOpen(false);
              }}
            />
            {/* </Row> */}
          </Row>
        </Form>
      </div>
    );
  };

  const showContent = () => {
    return (
      <div>
        <BackButtonUI
          buttons={buttons}
          title={t("Payment.paymentHistory")}
        ></BackButtonUI>
        <Row className="mb-4 w-full">
          <FilterFormComponent
            filterForm={filterForm}
            onDebouncedChange={(value) => {
              if (value === "") {
                filterForm.resetFields();
                setStatusKey("ALL");
                setStatusValue("All");
                setFuzzySearchFilter("");
                setModalFilter({});
                filterModalForm.resetFields();
                // setData([...fullData]);
                setShowClearFilter(false);
                setCursor(tempCursor);
                setFilterSetting("");
                localStorage.removeItem("paymentFilter");
              } else {
                filterModalForm.resetFields();
                setModalFilter({});
                setFuzzySearchFilter(value);
              }
            }}
            fieldName={fieldName}
            clearButtonOnChange={() => {
              filterForm.resetFields();
              setStatusKey("ALL");
              setStatusValue("All");
              setFuzzySearchFilter("");
              setModalFilter({});
              filterModalForm.resetFields();
              // setData([...fullData]);
              setShowClearFilter(false);
              setCursor(tempCursor);
              setFilterSetting("");
              localStorage.removeItem("paymentFilter");
            }}
            filterModalButtonOnClick={() => {
              setIsFilterModalOpen(true);
              filterForm.resetFields();
              setFuzzySearchFilter("");
            }}
            modalFilterValue={modalFilter}
            option={paymentStatusFilterOption}
            handleStatusMenuClick={handleStatusMenuClick}
            clearFilterDisable={showClearFilter === true ? false : true}
            statusValue={statusValue}
            debounceValue={fuzzySearchFilter}
          ></FilterFormComponent>
        </Row>
        <ListingTableUI
          // EditableCell={EditableCell}
          bordered
          dataSource={data}
          columns={column}
          // rowClassName="editable-row"
          rowKey={(record: any) => record.id}
          cursor={cursor}
          loader={showButtonLoader}
          loading={tableLoading}
          pagination={false}
          rowSelection={rowSelection}
        />
      </div>
    );
  };

  return (
    <div className="flex flex-col w-full min-h-screen bg-bgOrange">
      <Header items={headerItems} hasSearch={false} values={() => { }} />
      <Content className="flex flex-col mt-3 w-full sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2">
        {showContent()}
      </Content>
      <Row className="justify-center w-full pt-4">
        {showScrollButton && (
          <div className="fixed bottom-20 right-8 z-50">
            <button
              className={`flex items-center justify-center rounded-full w-10 h-10 text-white text-lg font-semibold focus:outline-none bg-blue-500 hover:bg-blue-600`}
              onClick={handleScrollToTop}
            >
              <ArrowUpOutlined style={{ fontSize: "24px" }} />
            </button>
          </div>
        )}
      </Row>
      {isSmallScreen ? (
        <Drawer
          title="Filter"
          placement="bottom"
          closable={false}
          onClose={() => setIsFilterModalOpen(false)}
          open={isFilterModalOpen}
          height="80vh"
          className="rounded-t-lg"
        >
          {filterModal()}
        </Drawer>
      ) : (
        <ModalUI
          // title={"More Filter"}
          width="70%"
          className={"modalFilterBody"}
          visible={isFilterModalOpen}
          onOk={() => setIsFilterModalOpen(false)}
          onCancel={() => setIsFilterModalOpen(false)}
          content={filterModal()}
          title={""}
        ></ModalUI>
      )}
      <ModalUI
        visible={showReceipt}
        onOk={() => setShowReceipt(false)}
        onCancel={() => setShowReceipt(false)}
        title={""}
        content={
          receipt.paymentDetails !== undefined
            ? receipt.paymentDetails.map(
              (item: PaymentDetail, index: number) => (
                <div className="pb-8" key={index}>
                  <h1>
                    {
                      paymentMethodOption.find(
                        (option: SelectOption) => option.value === item.type
                      )?.label
                    }
                  </h1>
                  <div className="flex">
                    {item.receipt?.map((url: string, urlIndex: number) => (
                      <img
                        src={receiptImage && receiptImage[url]}
                        width={100}
                        className="px-2"
                        key={urlIndex}
                        alt={`Receipt ${index}-${urlIndex}`}
                      />
                    ))}
                  </div>
                </div>
              )
            )
            : null
        }
      />
      <AppFooter retailerAccessValues={retailerAccess} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default PaymentListing;
