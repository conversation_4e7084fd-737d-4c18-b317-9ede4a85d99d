import React, { useEffect, useState } from "react";
import { Content } from "antd/lib/layout/layout";
import defaultImage from "../../assets/default/emptyImage.png";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import { Col, FloatButton, Form, MenuProps, Row } from "antd";
import Header, { supportedLocales } from "../../components/header";
import {
  BackButtonUI,
  CounterComponent,
  PrimaryButtonUI,
  SecondaryButtonUI,
} from "@/components/buttonUI";
import { useRouter } from "next/router";
import { ArrowUpOutlined } from "@ant-design/icons";
import {
  ListingTableUI,
  MessageErrorUI,
  MessageSuccessUI,
} from "@/components/ui";
import {
  DataSource,
  encodeParams,
  PUBLIC_BUCKET_URL,
  NumberThousandSeparator,
  setParamsFromLocalStorage,
  getParamsFromLocalStorage,
  setFilterForm,
} from "@/stores/utilize";
import {
  Product,
  UOM,
  Retailer,
  Cart,
  TradeInfoAggregate,
  OutletAverageSale,
  Taxes,
  TradeInfoAggregateUI,
  Promotion,
  Outlet,
} from "@/components/type";
import useRetailerStore from "@/stores/store";
import { getRetailerData } from "@/stores/authContext";
import { invoiceStatusFilterOption } from "@/components/config";
import FilterFormComponent from "@/components/filter";
import apiHelper from "../api/apiHelper";
import { ModalUI } from "@/components/modalUI";
import { ComponentFilterSelect } from "@/components/filterSelectInput";
import {
  getOutlet,
  getProduct,
  getPromotionByProductId,
  getPromotionProduct,
  getPromotionRelatedOutlet,
} from "../api/salesOrderHelper";
import { cloneDeep } from "lodash";
import Decimal from "decimal.js";
import AppFooter from "@/components/footer";

function AutoProposed() {
  const outletId = useRetailerStore((state) => state.currentOutletData?.id); //
  const [form] = Form.useForm();
  const { t } = useTranslation("common");
  const router = useRouter();
  const [filterForm] = Form.useForm();
  const [filterModalForm] = Form.useForm();
  const [cursor, setCursor] = useState("");
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({});
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const [uomMap, setUomMap] = useState(new Map());
  const [cartData, setCartData] = useState<any>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRowData, setSelectedRowData] = useState<any[]>([]);
  const [productMap, setProductMap] = useState(new Map());
  const [taxMap, setTaxMap] = useState(new Map());
  // ======================================================================
  // filter function - state()
  // ======================================================================
  const [showClearFilter, setShowClearFilter] = useState(false);
  const [fuzzySearchFilter, setFuzzySearchFilter] = useState("");
  const [modalFilter, setModalFilter] = useState<any>({});
  const [filterSetting, setFilterSetting] = useState("");
  const [statusValue, setStatusValue] = useState("All");
  const [statusKey, setStatusKey] = useState("ALL");
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  // const [fieldName, setFieldName] = useState("");
  const [loading, setLoading] = useState<boolean>(false);
  const [outletData, setOutletData] = useState<Outlet>({});

  const headerItems = [
    {
      label: t("Header.dashboard"),
      route: "/profile/dashboard",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("Dashboard.autoProposed"),
      route: "/autoProposed/autoProposedListing",
      className: "labelTextStyle",
    },
  ];

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollButton(window.pageYOffset > 0);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    if (Object.keys(useRetailerStore.getState()).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }
    }
  }, [Object.keys(useRetailerStore.getState()).length]);

  useEffect(() => {
    // getProductData();
  }, []);

  useEffect(() => {
    if (retailerAccess && Object.keys(retailerAccess).length > 0 && outletId) {
      getOutletData(outletId);
      getUOM();

      // Get from localStorage
      const filterParams: any = getParamsFromLocalStorage(
        router.pathname,
        "autoProposedFilter"
      );

      const filterKey: any = {
        fuzzySearch: "",
        SKU: null,
        name: null,
      };

      const clonedFilterKey = { ...filterKey };
      delete clonedFilterKey.fuzzySearch;

      Object.keys(clonedFilterKey).forEach((key) => {
        const capitalizedKey = key
          .split(/(?=[A-Z])|\s+/)
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
        clonedFilterKey[capitalizedKey] = clonedFilterKey[key];
        delete clonedFilterKey[key];
      });

      // const keysAsString: string = Object.keys(clonedFilterKey).join(", ");
      // setFieldName(keysAsString);

      if (filterParams) {
        // Initialize variables to store the values
        // Follow search params' key

        setFilterSetting(filterParams);
        setFilterForm(filterParams, filterKey);
        filterForm.setFieldValue(["fuzzySearch"], filterKey.fuzzySearch);
        filterModalForm.setFieldsValue(filterKey);
        setFuzzySearchFilter(filterKey.fuzzySearch);
        let data = {
          sku: filterKey.sku || "",
          productName: filterKey.productName || "",
        };
        setModalFilter(data);
        setStatusKey(filterKey.status ?? "ALL");
        const filterStatusLabel: any = invoiceStatusFilterOption.find(
          (item: any) => item.key === filterKey.status
        )?.label;
        setStatusValue(filterStatusLabel ?? "All");
      } else {
        getDistinctInvoiceProduct(true);
      }
    }
  }, [retailerAccess, outletId]);

  useEffect(() => {
    const data = {
      fuzzySearch: fuzzySearchFilter || "",
      sku: modalFilter.sku || "",
      status: statusKey === "ALL" ? "" : statusKey,
    };

    const allPropertiesEmpty = Object.values(data).every(
      (value) => value === ""
    );
    setCursor("0");

    if (!allPropertiesEmpty) {
      searchInvoice(data);
    } else {
      setFilterSetting("");
    }
  }, [fuzzySearchFilter, statusKey, modalFilter]);

  useEffect(() => {
    // Check params whether is same
    const filterParams = getParamsFromLocalStorage(
      router.pathname,
      "autoProposedFilter"
    );
    if (filterSetting) {
      getDistinctInvoiceProduct(true, false);

      if (filterSetting !== filterParams) {
        setParamsFromLocalStorage(
          router.pathname,
          filterSetting,
          "autoProposedFilter"
        );
      }
      setShowClearFilter(true);
    } else {
      setShowClearFilter(false);
      if (data.length > 0) {
        localStorage.removeItem("autoProposedFilter");
      }
      getDistinctInvoiceProduct(true, false);
    }
  }, [filterSetting]);

  // *************************************************************************************
  // *** Scolling Function - useEffect ***
  // *************************************************************************************
  // Check scrolling position
  useEffect(() => {
    if (cursor === "") return;
    const handleScroll = () => {
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight - 50;
      const scrollPosition = window.scrollY;
      if (windowHeight + scrollPosition >= documentHeight) {
        // Stop API calling when cursor is equal to '0'
        if (cursor !== "0") {
          getDistinctInvoiceProduct();
          // getInvoice(true);
        }
      }
    };
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [cursor]);

  const getOutletData = async (id: string[] = []) => {
    const dataSource = new DataSource(
      "outlets",
      encodeParams({ id: id }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setOutletData(res.items?.[0]);
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const handleScrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const getUOM = async () => {
    let tempProductMap = new Map(uomMap);

    const params: any = {
      status: "ACTIVE",
    };

    try {
      const dataSource = new DataSource("uoms", encodeParams(params), false);
      const res: any = await dataSource.load();
      if (res !== null && res.items.length > 0) {
        setUomMap((prevDataMap) => {
          const newDataMap = new Map(prevDataMap);
          res.items.forEach((item: UOM) => {
            if (!newDataMap.has(item.id)) {
              newDataMap.set(item.id, item);
            }
          });
          return newDataMap;
        });
        res.items?.map((item: UOM) => {
          tempProductMap.set(item.id, item);
        });
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getProductFromCart = () => {
    const dataSource = new DataSource(
      "retailerCarts",
      "retailerId=" + retailerAccess.id,
      false,
      "v2"
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null) {
          setCartData(res.items);
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getDistinctInvoiceProduct = async (
    isRefresh = false,
    isClearFilter = false
  ) => {
    try {
      const params: any = {
        outletId,
      };
      if (isRefresh === false) {
        params.cursor = cursor;
      }
      if (!outletId) {
        return;
      }
      setLoading(true);

      const checkFilterRights =
        filterSetting && !isClearFilter
          ? filterSetting +
            "&outletId=" +
            outletId +
            (cursor && cursor !== "0" ? `&cursor=${cursor}` : "")
          : encodeParams(params);
      const dataSource = new DataSource(
        "invoice/distinct/invoiceProduct",
        checkFilterRights,
        false
      );
      const res: any = await dataSource.load();
      if (!res?.items?.length) {
        setData([]);
        setLoading(false);
        setCursor("0");
        return [];
      }

      const productCatalogueId = res.items?.map(
        (item: TradeInfoAggregate) => item.productCatalogueId
      );
      const productMaps = await getProduct(cloneDeep(productCatalogueId));
      const outletAverageSaleDatas = await getLoopOutletAverageSales(
        cloneDeep(productCatalogueId)
      );

      const promotionRelateds = await getPromotionProduct(
        cloneDeep(productCatalogueId)
      );
      const outlet = await getOutlet();
      const promotionRelatedOutlets: Promotion[] =
        await getPromotionRelatedOutlet(
          cloneDeep(promotionRelateds),
          "TRUE",
          [],
          // outlet.companyBranchId || ""
          ""
        );

      outletAverageSaleDatas.map((item) => {
        item.productSales.map((averageSale) => {
          let value = 0;
          const key = `${averageSale.productId}-quantity`;
          const product: Product = productMaps.get(averageSale.productId);
          const tradeInfoAggreateData: TradeInfoAggregate = res?.items?.find(
            (item: TradeInfoAggregate) =>
              item.productCatalogueId === averageSale.productId
          );

          const productUOM = product.productUOM || [];
          const currentUOMData = productUOM.find(
            (uom) => uom.productUOMId === tradeInfoAggreateData.defaultUOMId
          );

          value =
            currentUOMData?.isSmallest === "FALSE"
              ? (averageSale.averagePromoSales +
                  averageSale.averageNonPromoSales) /
                (currentUOMData?.conversionToSmallestUOM || 1)
              : averageSale.averagePromoSales +
                averageSale.averageNonPromoSales;

          form.setFieldValue(key, Math.floor(value));
        });
      });

      const tradeInfoProductUI = res.items?.map(
        (item: TradeInfoAggregateUI) => {
          const promotionProduct = promotionRelateds?.find(
            (promotion) => promotion.productId === item.productCatalogueId
          );

          let promotionInitId: string[] = [];

          // for upload outlet list
          if (promotionProduct) {
            promotionRelatedOutlets.map((promotion) => {
              if (promotionProduct.promoIds.includes(promotion?.id || "")) {
                if (promotion.isAutoApply !== "FALSE") {
                  promotionInitId = promotionInitId.concat(promotion?.id || "");
                }
              }
            });
          }

          const promotionProductByCategoires = getPromotionByProductId(
            item.productCatalogueId,
            promotionRelatedOutlets
          );

          if (promotionProductByCategoires?.length) {
            promotionProductByCategoires?.map((promotion: Promotion) => {
              if (promotion.isAutoApply !== "FALSE") {
                promotionInitId = promotionInitId.concat(promotion?.id || "");
              }
            });
          }
          item.promotionInitId = Array.from(new Set(promotionInitId));
          return item;
        }
      );

      getTax(cloneDeep(productCatalogueId));
      setCursor(res?.cursor || "0");
      setProductMap(new Map(productMaps));
      setData(tradeInfoProductUI || []);
      setLoading(false);
      return tradeInfoProductUI || [];
    } catch (err) {
      return [];
    }
  };

  const getLoopOutletAverageSales = async (
    productIds: string[]
  ): Promise<OutletAverageSale[]> => {
    let results: any = [];

    if (!productIds.length) return results;

    while (productIds.length) {
      results = results.concat(
        await getOutletAverageSales(productIds.splice(0, 50))
      );
    }
    return results;
  };

  const getOutletAverageSales = async (productId: string[]) => {
    try {
      const params = {
        outletId,
        productId,
      };
      const dataSource = new DataSource(
        "invoice/outletAverageSales",
        encodeParams(params),
        false
      );
      const res: any = await dataSource.load();
      if (res.length) {
        return res;
      }
      return res?.items || [];
    } catch (err) {
      return [];
    }
  };

  const getTax = async (id: string[] = []) => {
    let tempProductMap = new Map(taxMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      //   status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource("taxes", encodeParams(params), false);
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setTaxMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Taxes) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Taxes) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const submitAddToCart = () => {
    selectedRowData.forEach((item: TradeInfoAggregateUI) => {
      let rate = 0;
      let totalTax = 0;

      const formKey = `${item.productCatalogueId}-quantity`;
      const found = cartData.find(
        (val: Cart) =>
          val.productId === item.productCatalogueId &&
          val.productUOMId === item.defaultUOMId
      );
      const quantity = form.getFieldValue(formKey);
      const productCatalogue = productMap.get(item.productCatalogueId);
      const unitPriceRound = parseFloat(
        new Decimal(item?.sellingPrice ?? 0).toDecimalPlaces(2).toString()
      );
      const productQuantity = quantity ? quantity : 1;
      const price = parseFloat(
        new Decimal(unitPriceRound * productQuantity)
          .toDecimalPlaces(2)
          .toString()
      );
      const productCatalogueTax = productCatalogue?.taxCategoryIds || [];

      if (productCatalogueTax?.length) {
        productCatalogueTax.forEach((item: string) => {
          const selectedTax = taxMap.get(item);
          if (selectedTax) {
            rate = rate + selectedTax.rate;
            totalTax = totalTax + selectedTax.rate * (unitPriceRound ?? 0);
          }
        });
      }

      let dataSubmit: Cart = {
        companyId: retailerAccess.companyId || "",
        // companyBranchId: retailerAccess.companyBranchId || "",
        companyBranchId: outletData?.companyBranchId || "",
        outletId: outletId ?? "",
        retailerId: retailerAccess.id || "",
        productId: item.productCatalogueId,
        productUOMId: item.defaultUOMId,
        quantity: quantity,
        unitPrice: item.sellingPrice ?? 0,
        totalPrice: price,
        promotionIds: item?.promotionInitId?.length ? item.promotionInitId : [],
        type: "SALES", //foc or sales
        totalDiscount: 0,
        totalNetPrice: price + totalTax,
        taxId: productCatalogueTax?.length ? productCatalogueTax[0] : "",
        taxRate: rate,
        totalTax: totalTax,
        supplierCompanyId: "66598c0468d35b66e82306ff",
        sellingType: "SELLING", //preorder or selling
        status: "PENDING",
      };

      if (found) {
        dataSubmit.id = found.id;
        apiHelper
          .PUT("retailerCart?id=" + found.id, dataSubmit, "", "v2")
          ?.then(() => {
            MessageSuccessUI(
              t("Cart.addToCart") + " " + t("Common.updateSuccess")
            );
            getProductFromCart();
          })
          ?.catch(() => {
            //* This Part need re-edit*//
            MessageErrorUI(
              t("Cart.addToCart") + " " + t("Common.updateUnsuccess")
            );
          });
      } else {
        apiHelper
          .POST("retailerCart", dataSubmit, "", "v2")
          ?.then(() => {
            MessageSuccessUI(
              t("Cart.addToCart") + " " + t("Common.successful")
            );
            getProductFromCart();
          })
          ?.catch(() => {
            //* This Part need re-edit*//
            MessageErrorUI(t("Cart.addToCart") + " " + t("Common.failed"));
          });
      }
    });
  };

  const productColumn = [
    {
      title: t("Invoice.product"),
      dataIndex: "productCatalogueId",
      key: "productCatalogueId",
      render: (_: any, record: TradeInfoAggregate) => {
        return (
          <Col className="flex items-center w-full">
            <img
              className="object-contain h-[80px] min-w-[80px] p-2"
              src={
                record.picture
                  ? PUBLIC_BUCKET_URL + record.picture
                  : defaultImage.src
              }
              loading="lazy"
            ></img>
            <div className="flex flex-col w-full">
              <p className="font-bold text-[14px]">{record.name}&nbsp;</p>
              <p className="text-gray-500 text-[10px] w-full flex ">
                <span>
                  {t("Invoice.productCode")}: {record.sku}
                </span>
              </p>
            </div>
          </Col>
        );
      },
    },
    {
      title: t("AutoProposed.uom"),
      dataIndex: "uom",
      key: "uom",
      render: (_: any, record: TradeInfoAggregate) => {
        return (
          <p className="tableRowNameDesign">
            {uomMap.get(record.defaultUOMId)?.name}
          </p>
        );
      },
    },
    {
      title: t("Invoice.unitPrice"),
      dataIndex: "unitPrice",
      key: "unitPrice",
      render: (_: any, record: TradeInfoAggregate) => {
        return (
          <p className="tableRowNameDesign flex justify-end">
            RM
            {NumberThousandSeparator(record.sellingPrice ?? 0)}
          </p>
        );
      },
    },
    {
      title: t("Invoice.totalPrice"),
      dataIndex: "totalPrice",
      key: "totalPrice",
      render: (_: any, record: TradeInfoAggregate) => {
        const key = `${record.productCatalogueId}-quantity`;
        const value = form.getFieldValue(key);
        const total = (record?.sellingPrice || 0) * (value || 0);
        return (
          <p className="tableRowNameDesign flex justify-end">
            RM {NumberThousandSeparator(total)}
          </p>
        );
      },
    },
    {
      title: t("Invoice.quantity"),
      dataIndex: "quantity",
      sorter: (a: any, b: any) => a.quantity.localeCompare(b.quantity),
      showSorterTooltip: false,
      key: "quantity",
      render: (_: any, record: TradeInfoAggregate) => {
        const key = `${record.productCatalogueId}-quantity`;
        return (
          <Form.Item name={key}>
            <CounterComponent
              className="bg-buttonOrange text-white font-bold"
              defaultValue={form.getFieldValue(key)}
              onCountChange={async (val) => {
                form.setFieldValue(key, val);
              }}
            />
          </Form.Item>
        );
      },
    },
  ];

  const rowSelection = {
    onChange: (selectedRowKeys: string[], selectedRows: []) => {
      setSelectedRowData(selectedRows);
      setSelectedRowKeys(selectedRowKeys);
    },

    getCheckboxProps: (record: { disabled: any; status: any }) => {},

    selectedRowKeys: selectedRowKeys,
  };

  const buttons = [
    {
      label: t("AutoProposed.addToCart"),
      onClick: submitAddToCart,
      disabled: !(selectedRowData.length > 0),
    },
  ];

  // *************************************************************************************
  // *** Filter Modal ***
  // *************************************************************************************

  const filterFormOnfinish = (values: any) => {
    setModalFilter(values);
  };

  const searchInvoice = (values: any) => {
    //convert to empty string when no value is entered as default value is undefined.
    for (const key in values) {
      if (values[key] === undefined) {
        values[key] = "";
      }
    }

    // return true or false
    let isAnyKeyFilled = Object.keys(values).some(
      (key) => values[key] !== "" && values[key] !== undefined
    );

    const params =
      encodeParams({
        fuzzySearch: values.fuzzySearch,
        sku: values.sku,
        status: values.status,
      }) + "&sort=createdAt&sortOrder=-1";

    if (isAnyKeyFilled) {
      // setCursor("0");
      setFilterSetting(params);
    }
  };

  const handleStatusMenuClick: MenuProps["onClick"] = ({ key }) => {
    const items = invoiceStatusFilterOption;
    setStatusKey(key);
    // Access the label property of the selected item
    const selectedLabel = items.find(
      (menuItem: any) => menuItem.key === key
    )?.label;
    if (selectedLabel) {
      setStatusValue(selectedLabel);
    }
  };

  const filterModal = () => {
    return (
      <div className="w-full">
        <Form
          onFinish={filterFormOnfinish}
          form={filterModalForm}
          className=""
          layout="vertical"
        >
          <h1 className="font-bold text-base pb-4">{t("Filter")}</h1>
          {/* First Row of Filter Input */}{" "}
          <Row className="flex md:flex-row flex-col gap-x-4">
            <Form.Item
              name="sku"
              className="mb-0 flex-1"
              label={
                <p className="text-neutral700 text-[12px]">
                  {t("Common.whatIsThe") + " " + t("CreditNote.sku") + "?"}
                </p>
              }
            >
              <ComponentFilterSelect
                placeholder={t("Common.eg") + " " + t("CreditNote.sku")}
                dbName={"productCatalogues"}
                customParams={{ status: "ACTIVE" }}
                displayExpr={"sku"}
                valueExpr={"id"}
                userAccess={retailerAccess}
                searchExpr={"fuzzySearch"}
                template={(item: any) => {
                  return `${item.name} (${item.sku})`;
                }}
              />
            </Form.Item>
            {/* <Form.Item
              name="productName"
              className="mb-0 flex-1"
              label={
                <p className="text-neutral700 text-[12px]">
                  {t("Common.whatIsThe") +
                    " " +
                    t("CreditNote.productName") +
                    "?"}
                </p>
              }
            >
              <SelectInput
                placeholder={t("Eg") + " " + t("CreditNote.productName")}
                options={productNameOption}
              />
            </Form.Item> */}
          </Row>
          <Row className="flex gap-x-3 pt-8 justify-between">
            <PrimaryButtonUI
              label={t("Common.resetAll")}
              onClick={() => {
                filterModalForm.resetFields();
                // setParamsFromLocalStorage(router.pathname, `status=${statusKey}`, "productFilter");
              }}
            />
            <Row>
              <SecondaryButtonUI
                label={t("Common.cancel")}
                htmlType="reset"
                onClick={() => {
                  setModalFilter({});
                  setIsFilterModalOpen(false);
                }}
              />
              <PrimaryButtonUI
                label={t("Common.applyFilter")}
                htmlType="submit"
                onClick={() => {
                  setIsFilterModalOpen(false);
                }}
              />
            </Row>
          </Row>
        </Form>
      </div>
    );
  };

  const showContent = () => {
    return (
      <div>
        <BackButtonUI
          title={t("AutoProposed.autoProposedList")}
          buttons={buttons}
        ></BackButtonUI>
        <Row className="mb-4 w-full">
          <FilterFormComponent
            filterForm={filterForm}
            onDebouncedChange={(value) => {
              filterModalForm.resetFields();
              setModalFilter({});
              setFuzzySearchFilter(value);
            }}
            fieldName={"Product Name and Product Sku"}
            clearButtonOnChange={() => {
              filterForm.resetFields();
              setStatusKey("ALL");
              setStatusValue("All");
              setFuzzySearchFilter("");
              setModalFilter({});
              filterModalForm.resetFields();
              // setData([...fullData]);
              setShowClearFilter(false);
              // setCursor(tempCursor);
              setFilterSetting("");
              localStorage.removeItem("autoProposedFilter");
            }}
            filterModalButtonOnClick={() => {
              setIsFilterModalOpen(true);
              filterForm.resetFields();
              setFuzzySearchFilter("");
            }}
            modalFilterValue={modalFilter}
            option={invoiceStatusFilterOption}
            handleStatusMenuClick={handleStatusMenuClick}
            clearFilterDisable={showClearFilter === true ? false : true}
            statusValue={statusValue}
            debounceValue={fuzzySearchFilter}
          ></FilterFormComponent>
        </Row>
        <Form form={form} layout="vertical">
          <ListingTableUI
            bordered
            dataSource={data}
            columns={productColumn}
            cursor={cursor}
            rowKey={(record: any) => record.productCatalogueId}
            pagination={false}
            loading={loading}
            rowSelection={rowSelection}
          />
        </Form>
      </div>
    );
  };

  return (
    <div className="flex flex-col w-full min-h-screen bg-bgOrange">
      <Header items={headerItems} hasSearch={false} values={() => {}} />
      <Content className="flex flex-col mt-3 w-full sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2">
        {showContent()}
      </Content>
      <Row className="justify-center w-full pt-4">
        {showScrollButton && (
          <FloatButton
            shape="circle"
            type="primary"
            onClick={() => {
              handleScrollToTop();
            }}
            style={{ right: 25, bottom: 120 }}
            icon={
              <ArrowUpOutlined
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              />
            }
          />
        )}
      </Row>
      <ModalUI
        // title={"More Filter"}
        width="70%"
        className={"modalFilterBody"}
        visible={isFilterModalOpen}
        onOk={() => setIsFilterModalOpen(false)}
        onCancel={() => setIsFilterModalOpen(false)}
        content={filterModal()}
        title={""}
      ></ModalUI>
      <AppFooter retailerAccessValues={retailerAccess} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default AutoProposed;
