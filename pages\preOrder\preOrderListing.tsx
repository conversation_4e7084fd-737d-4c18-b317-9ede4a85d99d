import React, { useEffect, useState } from "react";
import { Content } from "antd/lib/layout/layout";
import { Button, Form, MenuProps, Row, Tooltip } from "antd";
import Header, { supportedLocales } from "../../components/header";
import {
  FormTextInput,
  SelectInput,
  SingleDateInput,
} from "@/components/input";
import {
  BackButtonUI,
  PrimaryButtonUI,
  SecondaryButtonUI,
} from "@/components/buttonUI";
import { useRouter } from "next/router";
import { ArrowUpOutlined, DeleteFilled, EyeOutlined } from "@ant-design/icons";
import {
  ListingTableUI,
  MessageErrorUI,
  MessageSuccessUI,
  ModalConfirmUI,
  statusApproval,
} from "@/components/ui";
import {
  DataSource,
  encodeParams,
  NumberThousandSeparator,
  setParamsFromLocalStorage,
  getParamsFromLocalStorage,
  setFilterForm,
} from "@/stores/utilize";
import {
  Outlet,
  Product,
  UOM,
  ProductOrdered,
  PreOrder,
  OutletShippingAddress,
  SelectOption,
  Retailer,
  ProductsPreordered,
} from "@/components/type";
import { ModalUI } from "@/components/modalUI";
import moment from "moment";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import FilterFormComponent from "@/components/filter";
import { statusFilterOption1 } from "@/components/config";
import useRetailerStore from "@/stores/store";
import { getRetailerData } from "@/stores/authContext";
import apiHelper from "../api/apiHelper";
import { ComponentFilterSelect } from "@/components/filterSelectInput";
import AppFooter from "@/components/footer";

function PreOrderListing() {
  const { t } = useTranslation("common");
  const router = useRouter();
  const [filterForm] = Form.useForm();
  const [filterModalForm] = Form.useForm();
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({});
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [showButtonLoader, setShowButtonLoader] = useState(false);
  const [cursor, setCursor] = useState("");
  const [tempCursor, setTempCursor] = useState("");
  const [tableLoading, setTableLoading] = useState(false);

  const [data, setData] = useState<any[]>([]);
  // const [companyMap, setCompanyMap] = useState(new Map());
  const [outletMap, setOutletMap] = useState(new Map());
  const [productMap, setProductMap] = useState(new Map());
  const [shippingAddressMap, setShippingAddressMap] = useState(new Map());
  const [uomMap, setUomMap] = useState(new Map());
  // const [productSkuOption, setProductSkuOption] = useState<SelectOption[]>([]);
  // const [productNameOption, setProductNameOption] = useState<SelectOption[]>(
  //   []
  // );

  // const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRowData, setSelectedRowData] = useState<any[]>([]);
  // const [pdfLoading, setPDFLoading] = useState(false);
  // ======================================================================
  // filter function - state()
  // ======================================================================
  const [showClearFilter, setShowClearFilter] = useState(false);
  const [fuzzySearchFilter, setFuzzySearchFilter] = useState("");
  const [modalFilter, setModalFilter] = useState<any>({});
  const [filterSetting, setFilterSetting] = useState("");
  const [statusValue, setStatusValue] = useState("All");
  const [statusKey, setStatusKey] = useState("ALL");
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [fieldName, setFieldName] = useState("");

  const headerItems = [
    {
      label: t("Header.dashboard"),
      route: "/profile/dashboard",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("Header.preOrder"),
      route: "/preOrder/preOrderListing",
      className: "labelTextStyle",
    },
  ];

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollButton(window.pageYOffset > 0);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    if (Object.keys(retailerAccess).length) {
      getPreOrder();
    }
  }, [retailerAccess]);

  useEffect(() => {
    if (Object.keys(useRetailerStore.getState()).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }
    }
  }, [Object.keys(useRetailerStore.getState()).length]);

  useEffect(() => {
    if (retailerAccess && Object.keys(retailerAccess).length > 0) {
      // getPreOrder();
      // getProductData();

      // Get from localStorage
      const filterParams: any = getParamsFromLocalStorage(
        router.pathname,
        "preOrderFilter"
      );

      const filterKey: any = {
        fuzzySearch: "",
        preorderNo: null,
        createdDate: null,
        productName: null,
        productSku: null,
      };

      const clonedFilterKey = { ...filterKey };
      delete clonedFilterKey.fuzzySearch;

      Object.keys(clonedFilterKey).forEach((key) => {
        const capitalizedKey = key
          .split(/(?=[A-Z])|\s+/)
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
        clonedFilterKey[capitalizedKey] = clonedFilterKey[key];
        delete clonedFilterKey[key];
      });

      const keysAsString: string = Object.keys(clonedFilterKey).join(", ");
      setFieldName(keysAsString);

      if (filterParams) {
        // Initialize variables to store the values
        // Follow search params' key

        setFilterSetting(filterParams);
        setFilterForm(filterParams, filterKey);
        filterForm.setFieldValue(["fuzzySearch"], filterKey.fuzzySearch);
        filterModalForm.setFieldsValue(filterKey);
        setFuzzySearchFilter(filterKey.fuzzySearch);

        let data = {
          preorderNo: filterKey.preorderNo || "",
          createdDate: filterKey.createdDate || "",
          productSku: filterKey.productSku || "",
          productName: filterKey.productName || "",
        };
        setModalFilter(data);
        setStatusKey(filterKey.status ?? "ALL");
        const filterStatusLabel: any = statusFilterOption1.find(
          (item: any) => item.key === filterKey.status
        )?.label;
        setStatusValue(filterStatusLabel ?? "All");
      } else {
        getPreOrder(true);
      }
    }
  }, [retailerAccess]);

  useEffect(() => {
    if (Object.keys(retailerAccess).length) {
      const data = {
        fuzzySearch: fuzzySearchFilter || "",
        preorderNo: modalFilter.preorderNo || "",
        productSku: modalFilter.productSku || "",
        productName: modalFilter.productName || "",
        createdDate: modalFilter.createdDate || "",
        status: statusKey === "ALL" ? "" : statusKey,
      };

      const allPropertiesEmpty = Object.values(data).every(
        (value) => value === ""
      );

      if (!allPropertiesEmpty) {
        searchPreOrder(data);
      } else {
        setFilterSetting("");
      }
    }
  }, [fuzzySearchFilter, statusKey, modalFilter, retailerAccess]);

  useEffect(() => {
    if (Object.keys(retailerAccess).length) {
      // Check params whether is same
      const filterParams = getParamsFromLocalStorage(
        router.pathname,
        "preOrderFilter"
      );
      if (filterSetting) {
        getPreOrder(true, false);

        if (filterSetting !== filterParams) {
          setParamsFromLocalStorage(
            router.pathname,
            filterSetting,
            "preOrderFilter"
          );
        }
        setShowClearFilter(true);
      } else {
        setShowClearFilter(false);
        if (data.length > 0) {
          localStorage.removeItem("preOrderFilter");
        }
        getPreOrder(true, false);
      }
    }
  }, [filterSetting, retailerAccess]);

  // *************************************************************************************
  // *** Scolling Function - useEffect ***
  // *************************************************************************************
  // Check scrolling position
  useEffect(() => {
    if (cursor === "") return;
    const handleScroll = () => {
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight - 50;
      const scrollPosition = window.scrollY;
      if (windowHeight + scrollPosition >= documentHeight) {
        // Stop API calling when cursor is equal to '0'
        if (cursor !== "0") {
          getPreOrder();
        }
      }
    };
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [cursor]);

  const handleScrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  let isLoading = false;

  const getPreOrder = (isRefresh = false, isClearFilter = false) => {
    // const accessBranches = otherViewBranchAccess === false ? retailerAccess.companyBranchIds : otherCompanyBranchesIds;
    let currentOutletId = localStorage.getItem("currentOutletId");
    if (isLoading) return;
    setTableLoading(true);
    setShowButtonLoader(true);
    isLoading = true;
    setTimeout(() => {
      let params: any = {
        sort: "createdAt",
        sortOrder: "-1",
        companyId: retailerAccess.companyId,
        // companyBranchId: retailerAccess.companyBranchId,
        outletId: currentOutletId,
      };

      if (isRefresh === false) {
        params.cursor = cursor;
      }

      // if (!isAdmin) {
      //   params.companyBranchIds = accessBranches;
      // }
      // const checkAdminRights = isAdmin ? params : params + "&companyId=" + retailerAccess.companyId;
      const checkFilterRights =
        filterSetting && !isClearFilter
          ? filterSetting +
          (cursor && cursor !== "0" ? `&cursor=${cursor}` : "")
          : encodeParams(params);
      const dataSource = new DataSource("preorders", checkFilterRights, false);

      // !isAdmin && !otherViewBranchAccess ? filterForm.setFieldValue("companyId", retailerAccess.companyId) : null;

      // if ((accessBranches && accessBranches.length > 0) || isAdmin) {
      dataSource
        .load()
        .then((res: any) => {
          if (res && res.items !== null) {
            let data = res.items;
            const objectMap = data.reduce(
              (accumulator: any, current: PreOrder) => {
                accumulator["outletId"] = accumulator["outletId"] || [];
                if (
                  current.outletId &&
                  !outletMap.has(current.outletId) &&
                  !accumulator["outletId"].includes(current.outletId)
                ) {
                  accumulator["outletId"].push(current.outletId ?? "");
                }

                accumulator["shippingAddressId"] =
                  accumulator["shippingAddressId"] || [];
                if (
                  current.shippingAddressId &&
                  !shippingAddressMap.has(current.shippingAddressId) &&
                  !accumulator["shippingAddressId"].includes(
                    current.shippingAddressId
                  )
                ) {
                  accumulator["shippingAddressId"].push(
                    current.shippingAddressId ?? ""
                  );
                }

                current.productsPreordered?.reduce(
                  (acc: any, product: ProductsPreordered) => {
                    accumulator["productId"] = accumulator["productId"] || [];
                    if (
                      product.productId &&
                      !productMap.has(product.productId) &&
                      !accumulator["productId"].includes(product.productId)
                    ) {
                      accumulator["productId"].push(product.productId ?? "");
                    }

                    accumulator["productUOMId"] =
                      accumulator["productUOMId"] || [];
                    if (
                      product.productUOMId &&
                      !uomMap.has(product.productUOMId) &&
                      !accumulator["productUOMId"].includes(
                        product.productUOMId
                      )
                    ) {
                      accumulator["productUOMId"].push(
                        product.productUOMId ?? ""
                      );
                    }

                    return acc;
                  },
                  {}
                );

                return accumulator;
              },
              {}
            );

            // getCompany(objectMap["companyId"]);
            getOutlets(objectMap["outletId"]);
            getProduct(objectMap["productId"]);
            getUOM(objectMap["productUOMId"]);
            getShippingAddress(objectMap["shippingAddressId"]);
            // getCompanyBranch(objectMap["companyBranchId"]);
            const nextCursor = res.cursor; // Get the cursor from the last item in the response
            if (nextCursor !== cursor || isRefresh) {
              // Avoid duplicates
              if (!isRefresh) {
                // setFullData((prevData) => [...prevData, ...data]);
                setData((prevData) => [...prevData, ...data]);
                setTableLoading(false);
                setShowButtonLoader(false); //path to check
              } else {
                // setFullData([...data]);
                setData([...data]);
                setTableLoading(false);
                setShowButtonLoader(false); //path to check
              }
              // cursor = nextCursor;
              setCursor(nextCursor);
              setTempCursor(nextCursor);
            }
          }
          isLoading = false;
        })
        .catch(() => {
          isLoading = false;
          //* This Part need re-edit*//
        });
      // } else {
      //   setGoodsReturnListingInfoData([]);
      // }
    }, 500);
  };

  // const getCompany = async (id: string[] = []) => {
  //   const dataSource = new DataSource(
  //     "companies",
  //     encodeParams({ id: id }),
  //     false
  //   );
  //   dataSource
  //     .load()
  //     .then((res: any) => {
  //       if (res !== null && res.items.length > 0) {
  //         setCompanyMap((prevDataMap) => {
  //           const newDataMap = new Map(prevDataMap);
  //           res.items.forEach((item: CompanyGeneralInfo) => {
  //             if (!newDataMap.has(item.id)) {
  //               newDataMap.set(item.id, item);
  //             }
  //           });
  //           return newDataMap;
  //         });
  //       }
  //     })
  //     .catch(() => {
  //       //* This Part need re-edit*//
  //     });
  // };

  const getOutlets = async (id: string[] = []) => {
    let tempProductMap = new Map(outletMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "outlets",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });
        if (res !== null && res.items.length > 0) {
          setOutletMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Outlet) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Outlet) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getProduct = async (id: string[] = []) => {
    let tempProductMap = new Map(productMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      //   status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "productCatalogues",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setProductMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Product) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Product) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getUOM = async (id: string[] = []) => {
    let tempProductMap = new Map(uomMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource("uoms", encodeParams(params), false);
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setUomMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: UOM) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: UOM) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getShippingAddress = async (id: string[] = []) => {
    let tempProductMap = new Map(uomMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "shippingAddresses",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setShippingAddressMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: OutletShippingAddress) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: OutletShippingAddress) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  // const getProductData = async () => {
  //   const dataSource = new DataSource(
  //     "productCatalogues",
  //     "status=ACTIVE",
  //     true
  //   );
  //   const res: any = await dataSource.load();

  //   if (res !== null) {
  //     let nameList: SelectOption[] = [];
  //     let skuList: SelectOption[] = [];
  //     res.map((value: any) => {
  //       nameList.push({
  //         value: value.id,
  //         label: value.name,
  //       });
  //       skuList.push({
  //         value: value.id,
  //         label: value.sku,
  //       });
  //     });
  //     setProductNameOption(nameList);
  //     setProductSkuOption(skuList);
  //   }
  // };

  //confirm delete modal
  const confirmDeletePreOrder = (val: string) => {
    ModalConfirmUI({
      title: t("Modal.confirmCancel"),
      content: t("Modal.ensureContent"),
      okText: t("Common.ok"),
      cancelText: t("Common.cancel"),
      onOk: () => {
        cancelOrder(val); //get all the taxes and exclude the selected one and update the data to status.
      },
      onCancel: () => { },
    });
  };

  const cancelOrder = (idSelected: string) => {
    // //validate wheteher use select reason
    // cancelReasonForm
    //   .validateFields()
    //   .then(() => {
    let data = {
      // id: router.query.id,
      status: "CANCELLED",
    };
    apiHelper
      .PUT("preorder?id=" + idSelected, data)
      ?.then(() => {
        MessageSuccessUI(
          t("PreOrder.preOrder") +
          " " +
          t("Common.cancel") +
          " " +
          t("Common.successful")
        );

        setTimeout(() => {
          router.reload();
        }, 500);
      })
      ?.catch(() => {
        //* This Part need re-edit*//
        MessageErrorUI(
          t("PreOrder.preOrder") +
          " " +
          t("Common.cancel") +
          " " +
          t("Common.failed")
        );
        router.reload();
      });
    //   })
    //   .catch(() => {
    //     MessageErrorUI(t("ReasonIsRequired"));
    //   });
  };

  const column = [
    {
      width: 120,
      title: t("PreOrder.preOrder") + " " + t("Common.no"),
      dataIndex: "preorderNo",
      onFilter: (value: string, record: any) =>
        record.preorderNo.indexOf(value) === 0,
      sorter: (a: any, b: any) => a.preorderNo.localeCompare(b.preorderNo),
      showSorterTooltip: false,
      key: "preorderNo",
      render: (_: any, record: PreOrder) => {
        return <p className="tableRowNameDesign">{record.preorderNo}</p>;
      },
    },
    {
      width: 120,
      title: t("PreOrder.orderDate"),
      dataIndex: "createdAt",
      onFilter: (value: string, record: any) =>
        record.createdAt.indexOf(value) === 0,
      sorter: (a: any, b: any) => a.createdAt.localeCompare(b.createdAt),
      showSorterTooltip: false,
      key: "createdAt",
      render: (_: any, record: PreOrder) => {
        return (
          <p className="tableRowNameDesign">
            {moment(record.createdAt).format("DD/MM/YYYY h:mm A")}
          </p>
        );
      },
    },
    // {
    //   width: 120,
    //   title: t("OutletCode"),
    //   dataIndex: "outletCode",
    //   onFilter: (value: string, record: any) => record.outletCode.indexOf(value) === 0,
    //   sorter: (a: any, b: any) => {
    //     const first = outletMap.get(a.outletId)?.outletCode || "";
    //     const second = outletMap.get(b.outletId)?.outletCode || "";

    //     return first?.localeCompare(second);
    //   },
    //   showSorterTooltip: false,
    //   key: "outletCode",
    //   render: (_: any, record: SalesOrder) => {
    //     return <p className="tableRowNameDesign">{outletMap.get(record.outletId)?.outletCode}</p>;
    //   },
    // },
    // {
    //   width: 120,
    //   title: t("Outlet"),
    //   dataIndex: "outletId",
    //   onFilter: (value: string, record: any) => record.outletId.indexOf(value) === 0,
    //   sorter: (a: any, b: any) => {
    //     const first = outletMap.get(a.outletId)?.name || "";
    //     const second = outletMap.get(b.outletId)?.name || "";

    //     return first?.localeCompare(second);
    //   },
    //   showSorterTooltip: false,
    //   key: "outletId",
    //   render: (_: any, record: SalesOrder) => {
    //     return <p className="tableRowNameDesign">{outletMap.get(record.outletId)?.name}</p>;
    //   },
    // },
    // {
    //   width: 120,
    //   title: t("CPO"),
    //   dataIndex: "cpo",
    //   onFilter: (value: string, record: any) => record.cpo.indexOf(value) === 0,
    //   sorter: (a: any, b: any) => a.cpo.localeCompare(b.cpo),
    //   showSorterTooltip: false,
    //   key: "cpo",
    //   render: (_: any, record: SalesOrder) => {
    //     return <p className="tableRowNameDesign">{record.cpo}</p>;
    //   },
    // },
    {
      width: 120,
      title: t("PreOrder.estimatedDeliveryDate"),
      dataIndex: "estimatedDeliveredAt",
      onFilter: (value: string, record: any) =>
        record.estimatedDeliveredAt.indexOf(value) === 0,
      sorter: (a: any, b: any) =>
        a.estimatedDeliveredAt.localeCompare(b.estimatedDeliveredAt),
      showSorterTooltip: false,
      key: "estimatedDeliveredAt",
      render: (_: any, record: PreOrder) => {
        let eta = "";
        if (record.status === "ACKNOWLEDGED" || record.status === "COMPLETED") {
          eta = moment(record.estimatedDeliveredAt).format("DD-MM-YYYY");
        } else {
          eta = "-";
        }
        return <p className="tableRowNameDesign">{eta}</p>;
      },
    },
    {
      width: 120,
      title: t("PreOrder.creditLimit"),
      dataIndex: "creditLimit",
      onFilter: (value: string, record: any) =>
        record.creditLimit.indexOf(value) === 0,
      sorter: (a: any, b: any) => a.creditLimit - b.creditLimit,
      showSorterTooltip: false,
      key: "creditLimit",
      render: (_: any, record: PreOrder) => {
        let data = outletMap.get(record.outletId)?.creditLimit;
        let formatted = NumberThousandSeparator(data as number);
        return <p className="tableRowNameDesign">{formatted}</p>;
      },
    },
    {
      width: 120,
      title: t("PreOrder.outstanding"),
      dataIndex: "outstanding",
      onFilter: (value: string, record: any) =>
        record.outstanding.indexOf(value) === 0,
      sorter: (a: any, b: any) => a.outstanding - b.outstanding,
      showSorterTooltip: false,
      key: "outstanding",
      render: (_: any, record: PreOrder) => {
        return (
          <p className="tableRowNameDesign">
            {outletMap.get(record.outletId)?.outstandingPayment}
          </p>
        );
      },
    },
    {
      title: t("Common.status"),
      dataIndex: "status",
      key: "status",
      onFilter: (value: string, record: any) =>
        record.status.indexOf(value) === 0,
      sorter: (a: any, b: any) => a.status.localeCompare(b.status),
      showSorterTooltip: false,
      width: 100,
      render: (_: any, record: any) => {
        return statusApproval(record);
      },
    },
    // {
    //     title: t("Common.approvalStatus"),
    //     dataIndex: "approvalStatus",
    //     key: "approvalStatus",
    //     onFilter: (value: string, record: any) =>
    //         record.approvalStatus.indexOf(value) === 0,
    //     sorter: (a: any, b: any) =>
    //         a.approvalStatus?.localeCompare(b.approvalStatus),
    //     showSorterTooltip: false,
    //     width: 100,
    //     render: (_: any, record: any) => {
    //         return statusApproval(record);
    //     },
    // },

    {
      // Action
      title: t("Common.action"),
      dataIndex: "action",
      key: "action",
      fixed: "right",
      width: 60,
      render: (_: any, record: PreOrder) => {
        const product = record.productsPreordered;
        let totals = 0;

        if (product) {
          product.map((item: ProductsPreordered) => {
            if (item) {
              const subtotal = (item.quantity ?? 0) * (item?.price ?? 0);
              const taxAmount = subtotal * (item.taxRate ?? 0);
              const total = subtotal - (item.discount ?? 0) - taxAmount;
              totals = totals + total;
            }
          });
        }

        // const containsRorC = /[RU]/.test(userAccess?.policies?.["goodsReturn"] || "");
        // if ((containsRorC || isAdmin || isCompanyAdmin) && record.status !== "UNVERIFIED") {
        return (
          <>
            <div className="flex justify-center items-center">
              <Button
                type="link"
                onClick={() =>
                  router.push(`/preOrder/orderDetail?id=${record.id}`)
                }
                className="flex items-center text-xs ml-0 p-2"
              >
                <Tooltip title={t("Common.viewMore")}>
                  <EyeOutlined style={{ color: "green" }} />
                </Tooltip>
              </Button>

              {record.status === "CONFIRMED" ? (
                <a
                  onClick={() => confirmDeletePreOrder(record?.id ?? "")}
                  className="flex items-center text-red-500 text-xs ml-0 p-2"
                >
                  <Tooltip title={t("Common.cancel")}>
                    {/* Function Note: Tooltip is used to showcase text when hovering over the item */}
                    <DeleteFilled className="text-red-500" />
                  </Tooltip>
                </a>
              ) : null}
            </div>
          </>
        );
        // }
        // return null;
      },
    },
  ];

  // const rowSelection = {
  //   onChange: (selectedRowKeys: string[], selectedRows: []) => {
  //     setSelectedRowData(selectedRows);
  //     setSelectedRowKeys(selectedRowKeys);
  //   },

  //   getCheckboxProps: (record: { disabled: any; status: any }) => {
  //     // if (selectedRowData && selectedRowData.length > 0) {
  //     //   if (regexPattern.test(selectedRowData[0]?.status)) {
  //     //     return {
  //     //       disabled: !regexPattern.test(record.status),
  //     //     };
  //     //   }
  //     // }
  //     // return {
  //     //   disabled: !regexPattern.test(record.status),
  //     // };
  //   },
  // };

  // const generatePDF = () => {
  //   setPDFLoading(true);
  //   selectedRowData.forEach((item: SalesOrder) => {
  //     const dataSource = new DataSource(
  //       "goodsReturn/pdf?id=",
  //       encodeParams({ id: item.id }),
  //       false
  //     );
  //     dataSource
  //       .load()
  //       .then((res: any) => {
  //         PicSignedUrl(res.item)
  //           .then((res: any) => {
  //             const link = document.createElement("a");
  //             link.href = res;
  //             link.target = "_blank"; // Open the link in a new tab (optional)
  //             document.body.appendChild(link);
  //             link.click();
  //             // Cleanup: Remove the link from the DOM
  //             document.body.removeChild(link);
  //           })
  //           .catch(() => {
  //             setPDFLoading(false);
  //           });
  //       })
  //       .catch(() => {
  //         setPDFLoading(false);
  //       });
  //     // }
  //   });

  //   setPDFLoading(false);
  // };

  // const buttons = [
  //   {
  //     label: t("PreOrder.printSalesOrder"),
  //     onClick: generatePDF,
  //     disabled: !(selectedRowData.length > 0),
  //   },
  // ];

  // *************************************************************************************
  // *** Filter Modal ***
  // *************************************************************************************

  const filterFormOnfinish = (values: any) => {
    setModalFilter(values);
  };

  const searchPreOrder = (values: any) => {
    //convert to empty string when no value is entered as default value is undefined.
    for (const key in values) {
      if (values[key] === undefined) {
        values[key] = "";
      }
    }

    // return true or false
    let isAnyKeyFilled = Object.keys(values).some(
      (key) => values[key] !== "" && values[key] !== undefined
    );

    const orderDate = values.createdDate
      ? values.createdDate.format("YYYY-MM-DDT00:00:00") + "Z"
      : "";

    let currentOutletId = localStorage.getItem("currentOutletId");

    const params =
      encodeParams({
        companyId: retailerAccess.companyId,
        // companyBranchId: retailerAccess.companyBranchId,
        outletId: currentOutletId,
        fuzzySearch: values.fuzzySearch,
        preorderNo: values.preorderNo || "",
        createdAt: orderDate || "",
        productId:
          values.productSku && values.productName
            ? [values.productSku, values.productName]
            : values.productSku || values.productName || "",
        status: values.status,
      }) + "&sort=createdAt&sortOrder=-1";

    if (isAnyKeyFilled) {
      setCursor("0");
      setFilterSetting(params);
    }
  };

  const handleStatusMenuClick: MenuProps["onClick"] = ({ key }) => {
    const items = statusFilterOption1;
    setStatusKey(key);
    // Access the label property of the selected item
    const selectedLabel = items.find(
      (menuItem: any) => menuItem.key === key
    )?.label;
    if (selectedLabel) {
      setStatusValue(selectedLabel);
    }
  };

  const filterModal = () => {
    return (
      <div className="w-full">
        <Form
          onFinish={filterFormOnfinish}
          form={filterModalForm}
          className=""
          layout="vertical"
        >
          <h1 className="font-bold text-base pb-4">{t("Filter")}</h1>
          {/* First Row of Filter Input */}
          <Row className="filterBlockForm flex-col space-y-3">
            <Row className="flex flex-row gap-x-4">
              <Form.Item
                name="preorderNo"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("PreOrder.preOrder") +
                      " " +
                      t("Common.no") +
                      "?"}
                  </p>
                }
              >
                <FormTextInput
                  placeholder={
                    t("Common.eg") +
                    " " +
                    t("PreOrder.preOrder") +
                    " " +
                    t("Common.no")
                  }
                  maxLength={30}
                />
              </Form.Item>
              <Form.Item
                name="createdDate"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("PreOrder.orderDate") +
                      "?"}
                  </p>
                }
              >
                <SingleDateInput
                  placeholder={t("Common.eg") + " " + t("PreOrder.orderDate")}
                  onChange={() => {
                    filterModalForm.submit();
                  }}
                />
              </Form.Item>
            </Row>
            <Row className="flex flex-row gap-x-4">
              <Form.Item
                name="productSku"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("PreOrder.productSKU") +
                      "?"}
                  </p>
                }
              >
                <ComponentFilterSelect
                  placeholder={t("Common.eg") + " " + t("PreOrder.productSKU")}
                  dbName={"productCatalogues"}
                  customParams={{ status: "ACTIVE" }}
                  displayExpr={"sku"}
                  valueExpr={"id"}
                  userAccess={retailerAccess}
                />
              </Form.Item>
              <Form.Item
                name="productName"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("PreOrder.productName") +
                      "?"}
                  </p>
                }
              >
                <ComponentFilterSelect
                  placeholder={t("Common.eg") + " " + t("PreOrder.productName")}
                  dbName={"productCatalogues"}
                  customParams={{ status: "ACTIVE" }}
                  displayExpr={"name"}
                  valueExpr={"id"}
                  userAccess={retailerAccess}
                />
              </Form.Item>
            </Row>
          </Row>
          <Row className="flex flex-row gap-x-3 pt-8 justify-between">
            <PrimaryButtonUI
              label={t("Common.resetAll")}
              onClick={() => {
                filterModalForm.resetFields();
                // setParamsFromLocalStorage(router.pathname, `status=${statusKey}`, "productFilter");
              }}
            />
            <Row>
              <SecondaryButtonUI
                label={t("Common.cancel")}
                htmlType="reset"
                onClick={() => {
                  setModalFilter({});
                  setIsFilterModalOpen(false);
                }}
              />
              <PrimaryButtonUI
                label={t("Common.applyFilter")}
                htmlType="submit"
                onClick={() => {
                  setIsFilterModalOpen(false);
                }}
              />
            </Row>
          </Row>
        </Form>
      </div>
    );
  };

  const showContent = () => {
    return (
      <div>
        <BackButtonUI
          buttons={[]}
          title={t("PreOrder.preOrder")}
        ></BackButtonUI>
        <Row className="mb-4 w-full">
          <FilterFormComponent
            filterForm={filterForm}
            onDebouncedChange={(value) => {
              filterModalForm.resetFields();
              setModalFilter({});
              setFuzzySearchFilter(value);
            }}
            fieldName={fieldName}
            clearButtonOnChange={() => {
              filterForm.resetFields();
              setStatusKey("ALL");
              setStatusValue("All");
              setFuzzySearchFilter("");
              setModalFilter({});
              filterModalForm.resetFields();
              // setData([...fullData]);
              setShowClearFilter(false);
              setCursor(tempCursor);
              setFilterSetting("");
              localStorage.removeItem("preOrderFilter");
            }}
            filterModalButtonOnClick={() => {
              setIsFilterModalOpen(true);
              filterForm.resetFields();
              setFuzzySearchFilter("");
            }}
            modalFilterValue={modalFilter}
            option={statusFilterOption1}
            handleStatusMenuClick={handleStatusMenuClick}
            clearFilterDisable={showClearFilter === true ? false : true}
            statusValue={statusValue}
            debounceValue={fuzzySearchFilter}
          ></FilterFormComponent>
        </Row>
        <ListingTableUI
          // EditableCell={EditableCell}
          bordered
          dataSource={data}
          columns={column}
          // rowClassName="editable-row"
          // rowKey={(record: any) => record.id}
          cursor={cursor}
          loader={showButtonLoader}
          loading={tableLoading}
          pagination={false}
        // rowSelection={rowSelection}
        />
      </div>
    );
  };

  return (
    <div className="flex flex-col w-full min-h-screen bg-bgOrange">
      <Header items={headerItems} hasSearch={false} values={() => { }} />
      <Content className="flex flex-col mt-3 w-full sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2">
        {showContent()}
      </Content>
      <Row className="justify-center w-full pt-4">
        {showScrollButton && (
          <div className="fixed bottom-20 right-8 z-50">
            <button
              className={`flex items-center justify-center rounded-full w-10 h-10 text-white text-lg font-semibold focus:outline-none bg-blue-500 hover:bg-blue-600`}
              onClick={handleScrollToTop}
            >
              <ArrowUpOutlined style={{ fontSize: "24px" }} />
            </button>
          </div>
        )}
      </Row>
      <ModalUI
        // title={"More Filter"}
        width="70%"
        className={"modalFilterBody"}
        visible={isFilterModalOpen}
        onOk={() => setIsFilterModalOpen(false)}
        onCancel={() => setIsFilterModalOpen(false)}
        content={filterModal()}
        title={""}
      ></ModalUI>
      <AppFooter retailerAccessValues={retailerAccess} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default PreOrderListing;
