import React, { useEffect, useState } from "react";
import { Content } from "antd/lib/layout/layout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import {
  Button,
  Col,
  Drawer,
  Form,
  MenuProps,
  Modal,
  Progress,
  Row,
  Table,
  Tooltip,
  Upload,
} from "antd";
import Header, { supportedLocales } from "../../components/header";
import {
  BackButtonUI,
  PrimaryButtonUI,
  SecondaryButtonUI,
} from "@/components/buttonUI";
import { useRouter } from "next/router";
import {
  ArrowUpOutlined,
  DownloadOutlined,
  EyeOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import {
  ExpandIcon,
  ListingTableUI,
  MessageErrorUI,
  statusApproval,
} from "@/components/ui";
import defaultImage from "../../assets/default/emptyImage.png";
import {
  DataSource,
  PicSignedUrl,
  encodeParams,
  capitalize,
  formateDate,
  formateDateAndTime,
  NumberThousandSeparator,
  getParamsFromLocalStorage,
  setFilterForm,
  setParamsFromLocalStorage,
  getSelectOptions,
  PUBLIC_BUCKET_URL,
  DataSourceWithPageNumber,
} from "@/stores/utilize";
import {
  CompanyGeneralInfo,
  DocumentList,
  Invoice,
  // Outlet,
  OutletShippingAddress,
  Product,
  ProductOrdered,
  ProductUOM,
  Retailer,
  SelectOption,
  StatementList,
  UOM,
} from "@/components/type";
import { ModalUI } from "@/components/modalUI";
import moment from "moment";
import FilterFormComponent from "@/components/filter";
import useRetailerStore from "@/stores/store";
import { getRetailerData } from "@/stores/authContext";
import { statementStatusFilterOption } from "@/components/config";
import {
  FormTextInput,
  SelectInput,
  SingleDateInput,
} from "@/components/input";
import apiHelper from "../api/apiHelper";
import { UploadProps } from "antd/lib";
import AppFooter from "@/components/footer";

function StatementListing() {
  const { t } = useTranslation("common");
  const router = useRouter();
  const [filterForm] = Form.useForm();
  const [creditNoteDetailForm] = Form.useForm();
  const [debitNoteDetailForm] = Form.useForm();
  const [filterModalForm] = Form.useForm();
  const [invoiceDetailForm] = Form.useForm();
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [showButtonLoader, setShowButtonLoader] = useState(false);
  const [cursor, setCursor] = useState("");
  // const [tempCursor, setTempCursor] = useState("");
  const [tableLoading, setTableLoading] = useState(false);
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({});
  const [data, setData] = useState<any[]>([]);
  // const [fullData, setFullData] = useState<StatementList[]>([]);
  // const [outletMap, setOutletMap] = useState(new Map());
  const [statementNoOption, setStatementNoOption] = useState<SelectOption[]>(
    []
  );
  const [invoiceNoOption, setInvoiceNoOption] = useState<SelectOption[]>([]);
  const [creditNoteNoOption, setCreditNoteNoOption] = useState<SelectOption[]>(
    []
  );
  const [debitNoteNoOption, setDebitNoteNoOption] = useState<SelectOption[]>(
    []
  );

  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRowData, setSelectedRowData] = useState<any[]>([]);
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [pdfLoading, setPDFLoading] = useState(false);
  const [isDownload, setIsDownload] = useState<boolean>(false);
  // ======================================================================
  // filter function - state()
  // ======================================================================
  const [showClearFilter, setShowClearFilter] = useState(false);
  // const [fuzzySearchFilter, setFuzzySearchFilter] = useState("");
  const [modalFilter, setModalFilter] = useState<any>({});
  const [filterSetting, setFilterSetting] = useState("");
  const [statusValue, setStatusValue] = useState("All");
  const [statusKey, setStatusKey] = useState("ALL");
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [isInvoiceModalOpen, setIsInvoiceModalOpen] = useState(false);
  const [isCreditNoteModalOpen, setIsCreditNoteModalOpen] = useState(false);
  const [isDebitNoteModalOpen, setIsDebitNoteModalOpen] = useState(false);
  const [allFiles, setAllFiles] = useState<{ [key: string]: any }>({});
  // const [fieldName, setFieldName] = useState("");
  const [productMap, setProductMap] = useState(new Map());
  const [uomMap, setUomMap] = useState(new Map());
  const [shippingAddressMap, setShippingAddressMap] = useState(new Map());
  const [invoiceMap, setInvoiceMap] = useState(new Map());
  const [companyMap, setCompanyMap] = useState(new Map());
  const [invoiceProductData, setInvoiceProductData] = useState<
    ProductOrdered[]
  >([]);
  const [creditNoteProductData, setCreditNoteProductData] = useState<
    ProductOrdered[]
  >([]);

  const headerItems = [
    {
      label: t("Header.dashboard"),
      route: "/profile/dashboard",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("Header.statement"),
      route: "/statement/statementListing",
      className: "labelTextStyle",
    },
  ];

  useEffect(() => {
    // Function to check screen size and update state
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth <= 950); // Define your breakpoint for small screens
    };

    // Add event listener for window resize
    window.addEventListener("resize", handleResize);

    // Call handleResize initially to set initial screen size
    handleResize();

    // Clean up the event listener on component unmount
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollButton(window.pageYOffset > 0);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    const retailer: Retailer = useRetailerStore.getState().retailer || {};
    setRetailerAccess(retailer);
    if (!Object.keys(retailer).length) {
      getRetailerData().then((value) => setRetailerAccess(value));
    }
  }, []);

  useEffect(() => {
    if (retailerAccess && Object.keys(retailerAccess).length > 0) {
      let currentOutletId = localStorage.getItem("currentOutletId");
      getSelectOptions(
        "invoices",
        encodeParams({
          companyId: retailerAccess.companyId,
          companyBranchId: retailerAccess.companyBranchId,
          outletId: currentOutletId,
          includedField: ["_id", "invoiceNo"],
        }),
        "invoiceNo",
        "id",
        true
      ).then((value) => {
        setInvoiceNoOption(value);
      });
      getSelectOptions(
        "creditNotes",
        encodeParams({
          companyId: retailerAccess.companyId,
          companyBranchId: retailerAccess.companyBranchId,
          outletId: currentOutletId,
        }),
        "creditNoteNo",
        "id",
        true
      ).then((value) => {
        setCreditNoteNoOption(value);
      });
      getSelectOptions(
        "debitNotes",
        encodeParams({
          companyId: retailerAccess.companyId,
          companyBranchId: retailerAccess.companyBranchId,
          outletId: currentOutletId,
        }),
        "debitNoteNo",
        "id",
        true
      ).then((value) => {
        setDebitNoteNoOption(value);
      });
      getSelectOptions(
        "statements",
        encodeParams({
          companyId: retailerAccess.companyId,
          companyBranchId: retailerAccess.companyBranchId,
          outletId: currentOutletId,
        }),
        "statementNo",
        "statementNo",
        true
      ).then((value) => {
        setStatementNoOption(value);
      });

      // Get from localStorage
      const filterParams: any = getParamsFromLocalStorage(
        router.pathname,
        "statementFilter"
      );

      const filterKey: any = {
        // fuzzySearch: "",
        statementNo: null,
        statementDate: null,
        invoiceNo: null,
        creditNoteNo: null,
        debitNoteNo: null,
        status: null,
      };

      // const clonedFilterKey = { ...filterKey };
      // // delete clonedFilterKey.fuzzySearch;

      // Object.keys(clonedFilterKey).forEach((key) => {
      //   const capitalizedKey = key
      //     .split(/(?=[A-Z])|\s+/)
      //     .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      //     .join(" ");
      //   clonedFilterKey[capitalizedKey] = clonedFilterKey[key];
      //   delete clonedFilterKey[key];
      // });

      // const keysAsString: string = Object.keys(clonedFilterKey).join(", ");
      // setFieldName(keysAsString);

      if (filterParams) {
        // Initialize variables to store the values
        // Follow search params' key

        setFilterSetting(filterParams);
        setFilterForm(filterParams, filterKey);
        // filterForm.setFieldValue(["fuzzySearch"], filterKey.fuzzySearch);
        // setFuzzySearchFilter(filterKey.fuzzySearch);
        filterModalForm.setFieldsValue(filterKey);
        let data = {
          statementNo: filterKey.statementNo || "",
          statementDate: filterKey.statementDate || "",
          invoiceNo: filterKey.invoiceNo || "",
          creditNoteNo: filterKey.creditNoteNo || "",
          debitNoteNo: filterKey.debitNoteNo || "",
        };
        setModalFilter(data);
        setStatusKey(filterKey.status ?? "ALL");
        const filterStatusLabel: any = items.find(
          (item: any) => item.key === filterKey.status
        )?.label;
        setStatusValue(filterStatusLabel ?? "All");
      } else {
        getStatement(true);
      }
    }
  }, [retailerAccess]);

  useEffect(() => {
    if (statusKey !== "ALL" || Object.keys(modalFilter).length) {
      const data = {
        // fuzzySearch: fuzzySearchFilter || "",
        statementNo: modalFilter.statementNo || "",
        statementDate: modalFilter.statementDate || "",
        invoiceNo: modalFilter.invoiceNo || "",
        creditNoteNo: modalFilter.creditNoteNo || "",
        debitNoteNo: modalFilter.debitNoteNo || "",
        status: statusKey === "ALL" ? "" : statusKey,
      };

      const allPropertiesEmpty = Object.values(data).every(
        (value) => value === ""
      );
      setCursor("0");

      if (!allPropertiesEmpty) {
        searchStatement(data);
      } else {
        setFilterSetting("");
      }
    }
  }, [statusKey, modalFilter]);

  useEffect(() => {
    if (filterSetting) {
      const filterParams = getParamsFromLocalStorage(
        router.pathname,
        "statementFilter"
      );

      if (filterSetting !== filterParams) {
        setParamsFromLocalStorage(
          router.pathname,
          filterSetting,
          "statementFilter"
        );
      }
      getStatement(true, false);
      // setFilterSetting("");
      setShowClearFilter(true);
    } else {
      setShowClearFilter(false);
      if (data.length > 0) {
        localStorage.removeItem("statementFilter");
      }
      if (Object.keys(retailerAccess).length) {
        getStatement(true, false);
      }
    }
  }, [filterSetting]);

  // useEffect(() => {
  //   // if (fullData.length > 0) {
  //   remapStatement();
  //   // }
  // }, [fullData]);

  // *************************************************************************************
  // *** Scolling Function - useEffect ***
  // *************************************************************************************
  // Check scrolling position
  useEffect(() => {
    if (cursor === "") return;
    const handleScroll = () => {
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight - 50;
      const scrollPosition = window.scrollY;
      if (windowHeight + scrollPosition >= documentHeight) {
        // Stop API calling when cursor is equal to '0'
        if (cursor !== "0") {
          getStatement();
        }
      }
    };
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [cursor]);

  const handleScrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  // *************************************************************************************
  // *** Get API ***
  // *************************************************************************************

  let isLoading = false;

  const getStatement = async (isRefresh = false, isClearFilter = false) => {
    // const accessBranches = otherViewBranchAccess === false ? retailerAccess.companyBranchId : otherCompanyBranchesIds;
    if (isLoading) return; // Skip if a request is already in progress
    setTableLoading(true);
    let currentOutletId = localStorage.getItem("currentOutletId");
    setTimeout(() => {
      let params: any = {
        sort: "createdAt",
        sortOrder: "-1",
        companyId: retailerAccess.companyId,
        // companyBranchId: retailerAccess.companyBranchId,
        outletId: currentOutletId,
        isCurrentStatement: "TRUE",
      };

      if (isRefresh === false) {
        params.cursor = cursor;
      }

      // if (!isAdmin) {
      //   params.companyBranchId = accessBranches;
      // }
      // const checkAdminRights = isAdmin ? params : params + "&companyId=" + retailerAccess.companyId;
      const checkFilterRights =
        filterSetting && !isClearFilter
          ? filterSetting +
          "&isCurrentStatement=TRUE" +
          (cursor && cursor !== "0" ? `&cursor=${cursor}` : "")
          : encodeParams(params);
      const dataSource = new DataSource("statements", checkFilterRights, false);

      // !isAdmin && !otherViewBranchAccess ? filterForm.setFieldValue("companyId", retailerAccess.companyId) : null;

      // if ((accessBranches && accessBranches.length > 0) || isAdmin) {
      dataSource
        .load()
        .then(async (res: any) => {
          if (res.items !== null) {
            let data = res.items;
            const objectMap = data.reduce((accumulator: any, current: any) => {
              // accumulator["outletId"] = accumulator["outletId"] || [];
              accumulator["productId"] = accumulator["productId"] || [];
              accumulator["productUOMId"] = accumulator["productUOMId"] || [];
              accumulator["shippingAddressId"] =
                accumulator["shippingAddressId"] || [];
              accumulator["invoiceId"] = accumulator["invoiceId"] || [];
              accumulator["companyId"] = accumulator["companyId"] || [];

              // if (
              //   current.outletId &&
              //   !outletMap.has(current.outletId) &&
              //   !accumulator["outletId"].includes(current.outletId)
              // ) {
              //   accumulator["outletId"].push(current.outletId);
              // }

              if (current?.invoices?.length > 0) {
                current.invoices.forEach((invoices: any) => {
                  if (
                    invoices?.shippingAddressId &&
                    !shippingAddressMap.has(invoices?.shippingAddressId) &&
                    !accumulator["shippingAddressId"].includes(
                      invoices?.shippingAddressId
                    )
                  ) {
                    accumulator["shippingAddressId"].push(
                      invoices?.shippingAddressId
                    );
                  }
                  if (invoices?.invoiceProducts?.length > 0) {
                    invoices?.invoiceProducts.forEach(
                      (invoiceProduct: ProductOrdered) => {
                        // if (
                        //   invoiceProduct?.productId &&
                        //   !productMap.has(invoiceProduct?.productId) &&
                        //   !accumulator["productId"].includes(
                        //     invoiceProduct?.productId
                        //   )
                        // ) {
                        //   accumulator["productId"].push(
                        //     invoiceProduct?.productId
                        //   );
                        // }

                        if (
                          invoiceProduct?.productUOMId &&
                          !uomMap.has(invoiceProduct?.productUOMId) &&
                          !accumulator["productUOMId"].includes(
                            invoiceProduct?.productUOMId
                          )
                        ) {
                          accumulator["productUOMId"].push(
                            invoiceProduct?.productUOMId
                          );
                        }
                      }
                    );
                  }
                });
              }
              if (current?.creditNotes?.length > 0) {
                current?.creditNotes.forEach((creditNotes: any) => {
                  if (
                    creditNotes?.invoiceId &&
                    !invoiceMap.has(creditNotes?.invoiceId) &&
                    !accumulator["invoiceId"].includes(creditNotes?.invoiceId)
                  ) {
                    accumulator["invoiceId"].push(creditNotes?.invoiceId);
                  }

                  if (
                    creditNotes?.companyId &&
                    !invoiceMap.has(creditNotes?.companyId) &&
                    !accumulator["companyId"].includes(creditNotes?.companyId)
                  ) {
                    accumulator["companyId"].push(creditNotes?.companyId);
                  }

                  if (creditNotes?.creditNoteProducts?.length > 0) {
                    creditNotes?.creditNoteProducts?.forEach(
                      (creditNoteProducts: ProductOrdered) => {
                        if (
                          creditNoteProducts?.productId &&
                          !productMap.has(creditNoteProducts?.productId) &&
                          !accumulator["productId"].includes(
                            creditNoteProducts?.productId
                          )
                        ) {
                          accumulator["productId"].push(
                            creditNoteProducts?.productId
                          );
                        }

                        if (
                          creditNoteProducts?.productUOMId &&
                          !uomMap.has(creditNoteProducts?.productUOMId) &&
                          !accumulator["productUOMId"].includes(
                            creditNoteProducts?.productUOMId
                          )
                        ) {
                          accumulator["productUOMId"].push(
                            creditNoteProducts?.productUOMId
                          );
                        }
                      }
                    );
                  }
                });
              }
              return accumulator;
            }, {});
            await getProduct(objectMap["productId"]);
            // await getOutlets(objectMap["outletId"]);
            await getUOM(objectMap["productUOMId"]);
            await getShippingAddress(objectMap["shippingAddressId"]);
            await getInvoice(objectMap["invoiceId"]);
            await getCompany(objectMap["companyId"]);
            data.map((item: any) => {
              // item.outletCode = outletMap.get(item.outletId)?.outletCode;
              // item.outletName = outletMap.get(item.outletId)?.name;
              let invoiceListData: Invoice[] = [];
              let creditNoteListData: any[] = [];
              let debitNoteListData: any[] = [];
              let statementAmount = 0;
              let paidAmount = 0;
              let outstandingAmount = 0;
              if (item.date !== undefined) {
                item.statementDate = formateDate(item.date);
              }
              // Get invoices
              if (item.invoices) {
                item.invoices.forEach((invoice: Invoice) => {
                  // let totalAmountInProduct = 0;
                  // invoice.invoiceProducts!.map((product: ProductOrdered) => {
                  //   if (
                  //     product.discount !== undefined &&
                  //     product.quantity !== undefined &&
                  //     product.quantity !== 0 &&
                  //     product.price !== undefined &&
                  //     product.taxRate !== undefined
                  //   ) {
                  //     let unitDiscount = product.discount / product.quantity;
                  //     let subTotalPrice =
                  //       (product.price - unitDiscount) * product.quantity;
                  //     let taxPrice = subTotalPrice * (product.taxRate / 100);
                  //     let total = subTotalPrice + taxPrice;
                  //     totalAmountInProduct += parseFloat(total.toFixed(2));
                  //   }
                  // });
                  // invoice?.invoiceToteboxes?.forEach((item) => {
                  //   if (
                  //     "toteboxProducts" in item &&
                  //     item?.toteboxProducts &&
                  //     item?.toteboxProducts?.length > 0
                  //   ) {
                  //     item?.toteboxProducts.map((product: ProductOrdered) => {
                  //       if (
                  //         product.discount !== undefined &&
                  //         product.quantity !== undefined &&
                  //         product.quantity !== 0 &&
                  //         product.price !== undefined &&
                  //         product.taxRate !== undefined
                  //       ) {
                  //         let unitDiscount =
                  //           product.discount / product.quantity;
                  //         let subTotalPrice =
                  //           (product.price - unitDiscount) * product.quantity;
                  //         let taxPrice =
                  //           subTotalPrice * (product.taxRate / 100);
                  //         let total = subTotalPrice + taxPrice;
                  //         totalAmountInProduct += parseFloat(total.toFixed(2));
                  //       }
                  //     });
                  //   }
                  // });
                  let formatInvoiceDate: any = invoice.invoiceDate;
                  let invoice1: any = {
                    invoiceNo: invoice.invoiceNo,
                    invoiceDate: formatInvoiceDate,
                    amount: parseFloat((invoice.netAmount ?? 0).toFixed(2)),
                    paidAmount: parseFloat(
                      (invoice.paidAmount ?? 0).toFixed(2)
                    ),
                    ...invoice,
                  };
                  statementAmount += invoice1.amount;
                  paidAmount +=
                    invoice1.processingAmount && invoice1.processingAmount !== 0
                      ? invoice1.processingAmount
                      : invoice1.paidAmount;

                  // outstandingAmount += totalAmountInProduct + (invoice.shippingFee ?? 0) - (invoice.paidAmount ?? 0);
                  outstandingAmount +=
                    invoice1.amount +
                    (invoice.shippingFee ?? 0) -
                    (invoice1.paidAmount ?? 0);
                  invoiceListData.push(invoice1);
                });
              }

              if (item.creditNotes) {
                item.creditNotes.forEach((creditNote: any) => {
                  let formatCreditNoteDate: any = creditNote.creditNoteDate;
                  let creditNote1 = {
                    creditNoteNo: creditNote.creditNoteNo,
                    creditNoteDate: formatCreditNoteDate,
                    amount: parseFloat((creditNote.usedAmount ?? 0).toFixed(2)),
                    grossAmount: parseFloat(
                      (creditNote.grossAmount ?? 0).toFixed(2)
                    ),
                    taxAmount: parseFloat(
                      (creditNote.taxAmount ?? 0).toFixed(2)
                    ),
                    ...creditNote,
                  };
                  statementAmount -=
                    creditNote1.grossAmount + creditNote1.taxAmount;
                  paidAmount -= creditNote1.amount;
                  outstandingAmount -=
                    (creditNote.grossAmount ?? 0) +
                    (creditNote.taxAmount ?? 0) -
                    (creditNote.usedAmount ?? 0);
                  creditNoteListData.push(creditNote1);
                });
              }

              if (item.debitNotes) {
                item.debitNotes?.forEach((debitNote: any) => {
                  let formatDebitNoteDate: any = debitNote.debitNoteDate;
                  let debitNote1 = {
                    debitNoteNo: debitNote.debitNoteNo,
                    debitNoteDate: formatDebitNoteDate,
                    amount: parseFloat((debitNote.paidAmount ?? 0).toFixed(2)),
                    grossAmount: parseFloat(
                      (debitNote.grossAmount ?? 0).toFixed(2)
                    ),
                    taxAmount: parseFloat(
                      (debitNote.taxAmount ?? 0).toFixed(2)
                    ),
                    //   outstandingAmount: parseFloat(((creditNote.grossAmount ?? 0) + (creditNote.taxAmount ?? 0) - (creditNote.usedAmount ?? 0)).toFixed(2)),
                    ...debitNote,
                  };
                  statementAmount +=
                    debitNote1.grossAmount + debitNote1.taxAmount;
                  paidAmount += debitNote1.amount;
                  outstandingAmount +=
                    (debitNote.grossAmount ?? 0) +
                    (debitNote.taxAmount ?? 0) -
                    (debitNote.paidAmount ?? 0);
                  debitNoteListData.push(debitNote1);
                });
              }

              let statementDocumentList: DocumentList[] = invoiceListData.map(
                (invoice: any) => {
                  return {
                    documentNo: invoice.invoiceNo,
                    documentDate: formateDate(invoice.invoiceDate),
                    documentType: "INVOICE",
                    documentAmount: invoice.amount,
                    paidAmount: invoice.paidAmount,
                    ...invoice,
                  };
                }
              );

              creditNoteListData.map((creditNote: any) => {
                let data: DocumentList = {
                  documentNo: creditNote?.creditNoteNo,
                  documentDate: formateDate(creditNote.creditNoteDate),
                  documentType: "CREDITNOTE",
                  documentAmount: creditNote.grossAmount + creditNote.taxAmount,
                  paidAmount: creditNote?.amount,
                  ...creditNote,
                };
                statementDocumentList.push(data);
              });

              debitNoteListData.map((debitNote: any) => {
                let data: DocumentList = {
                  documentNo: debitNote?.debitNoteNo,
                  documentDate: formateDate(debitNote?.debitNoteDate),
                  documentType: "DEBITNOTE",
                  documentAmount: debitNote.grossAmount + debitNote.taxAmount,
                  paidAmount: debitNote?.amount,
                  ...debitNote,
                };
                statementDocumentList.push(data);
              });

              item.documentList = statementDocumentList;
              // .filter((document: any) => {
              //   const documentAmount = document.documentAmount.toFixed(2);
              //   const paidAmount = document.paidAmount.toFixed(2);
              //   const difference = documentAmount - paidAmount;
              //   return difference;
              // });
              item.statementAmount = statementAmount;
              item.paidAmount = paidAmount;
              item.outstandingAmount = outstandingAmount;
              item.statementStatus = item.status;
              return item;
            });
            // getCompanyBranch(objectMap["companyBranchId"]);
            const nextCursor = res.cursor; // Get the cursor from the last item in the response
            if (nextCursor !== cursor || isRefresh) {
              // Avoid duplicates
              if (!isRefresh) {
                // setFullData((prevData) => [...prevData, ...data]);
                setData((prevData) => [...prevData, ...data]);
                setTableLoading(false);
                setShowButtonLoader(false); //path to check
              } else {
                // setFullData([...data]);
                setData([...data]);
                setTableLoading(false);
                setShowButtonLoader(false); //path to check
              }
              // cursor = nextCursor;
              setCursor(nextCursor);
            }
            // setFullData(data);
            // setTableLoading(false);
          } else {
            setData([]);
            setTableLoading(false);
            setShowButtonLoader(false);
          }
        })
        .catch(() => {
          //* This Part need re-edit*//
        });
      // } else {
      //   setGoodsReturnListingInfoData([]);
      // }
    }, 500);
  };

  const getProduct = async (id: string[] = []) => {
    let tempProductMap = new Map(productMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      //   status: "ACTIVE",
      id: [],
    };

    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "productCatalogues",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setProductMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Product) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Product) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getCompany = async (id: string[] = []) => {
    const dataSource = new DataSource(
      "companies",
      encodeParams({ id: id }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setCompanyMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: CompanyGeneralInfo) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getInvoice = async (id: string[] = []) => {
    let tempProductMap = new Map(invoiceMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      // status: "ACTIVE",
      includedField: ["_id", "invoiceNo"],
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSourceWithPageNumber(
          "invoices",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setInvoiceMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Invoice) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Invoice) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getShippingAddress = async (id: string[] = []) => {
    let tempProductMap = new Map(uomMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "shippingAddresses",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setShippingAddressMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: OutletShippingAddress) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: UOM) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getUOM = async (id: string[] = []) => {
    let tempProductMap = new Map(uomMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource("uoms", encodeParams(params), false);
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setUomMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: UOM) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: UOM) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  // const getOutlets = async (id: string[] = []) => {
  //   let tempProductMap = new Map(outletMap);
  //   if (!id?.length) return tempProductMap;

  //   const params: any = {
  //     status: "ACTIVE",
  //     id: [],
  //   };
  //   try {
  //     while (id?.length) {
  //       params.id = id?.splice(0, 50);
  //       const dataSource = new DataSource(
  //         "outlets",
  //         encodeParams(params),
  //         false
  //       );
  //       const res: any = await dataSource.load().catch(() => {
  //         id = [];
  //         //* This Part need re-edit*//
  //       });
  //       if (res !== null && res.items.length > 0) {
  //         setOutletMap((prevDataMap) => {
  //           const newDataMap = new Map(prevDataMap);
  //           res.items.forEach((item: Outlet) => {
  //             if (!newDataMap.has(item.id)) {
  //               newDataMap.set(item.id, item);
  //             }
  //           });
  //           return newDataMap;
  //         });
  //         res.items?.map((item: Outlet) => {
  //           tempProductMap.set(item.id, item);
  //         });
  //       }
  //     }
  //   } catch (err) {
  //     return tempProductMap;
  //   }
  //   return tempProductMap;
  // };

  // const remapStatement = () => {
  //   fullData.map((item: any) => {
  //     item.outletCode = outletMap.get(item.outletId)?.outletCode;
  //     item.outletName = outletMap.get(item.outletId)?.name;
  //     let invoiceListData: Invoice[] = [];
  //     let creditNoteListData: any[] = [];
  //     let debitNoteListData: any[] = [];
  //     let statementAmount = 0;
  //     let paidAmount = 0;
  //     if (item.date !== undefined) {
  //       item.statementDate = formateDate(item.date);
  //     }
  //     // Get invoices
  //     item.invoices.forEach((invoice: Invoice) => {
  //       let totalAmountInProduct = 0;
  //       invoice.invoiceProducts!.map((product: ProductOrdered) => {
  //         if (
  //           product.discount !== undefined &&
  //           product.quantity !== undefined &&
  //           product.price !== undefined &&
  //           product.taxRate !== undefined
  //         ) {
  //           let unitDiscount = product.discount / product.quantity;
  //           let subTotalPrice =
  //             (product.price - unitDiscount) * product.quantity;
  //           let taxPrice = subTotalPrice * (product.taxRate / 100);
  //           let total = subTotalPrice + taxPrice;
  //           totalAmountInProduct += parseFloat(total.toFixed(2));
  //         }
  //       });
  //       let formatInvoiceDate: any = invoice.createdAt;
  //       let invoice1: any = {
  //         invoiceNo: invoice.invoiceNo,
  //         invoiceDate: formatInvoiceDate,
  //         amount: parseFloat(
  //           (totalAmountInProduct + (invoice.shippingFee ?? 0)).toFixed(2)
  //         ),
  //         paidAmount: parseFloat((invoice.paidAmount ?? 0).toFixed(2)),
  //         ...invoice,
  //       };
  //       statementAmount += invoice1.amount;
  //       paidAmount += invoice1.paidAmount;

  //       // outstandingAmount += totalAmountInProduct + (invoice.shippingFee ?? 0) - (invoice.paidAmount ?? 0);
  //       invoiceListData.push(invoice1);
  //     });

  //     if (item.creditNotes) {
  //       item.creditNotes.forEach((creditNote: any) => {
  //         let formatCreditNoteDate: any = creditNote.createdAt;
  //         let creditNote1 = {
  //           creditNoteNo: creditNote.creditNoteNo,
  //           creditNoteDate: formatCreditNoteDate,
  //           amount: parseFloat((creditNote.usedAmount ?? 0).toFixed(2)),
  //           grossAmount: parseFloat((creditNote.grossAmount ?? 0).toFixed(2)),
  //           taxAmount: parseFloat((creditNote.taxAmount ?? 0).toFixed(2)),
  //           ...creditNote,
  //         };
  //         statementAmount -= creditNote1.grossAmount + creditNote1.taxAmount;
  //         paidAmount += creditNote1.amount;
  //         // outstandingAmount -= (creditNote.grossAmount ?? 0) + (creditNote.taxAmount ?? 0) - (creditNote.usedAmount ?? 0);
  //         creditNoteListData.push(creditNote1);
  //       });
  //     }

  //     if (item.debitNotes) {
  //       item.debitNotes?.forEach((debitNote: any) => {
  //         let formatDebitNoteDate: any = debitNote.createdAt;
  //         let debitNote1 = {
  //           debitNoteNo: debitNote.debitNoteNo,
  //           debitNoteDate: formatDebitNoteDate,
  //           amount: parseFloat((debitNote.paidAmount ?? 0).toFixed(2)),
  //           grossAmount: parseFloat((debitNote.grossAmount ?? 0).toFixed(2)),
  //           taxAmount: parseFloat((debitNote.taxAmount ?? 0).toFixed(2)),
  //           //   outstandingAmount: parseFloat(((creditNote.grossAmount ?? 0) + (creditNote.taxAmount ?? 0) - (creditNote.usedAmount ?? 0)).toFixed(2)),
  //           ...debitNote,
  //         };
  //         statementAmount += debitNote1.grossAmount + debitNote1.taxAmount;
  //         paidAmount += debitNote1.amount;
  //         // outstandingAmount += (debitNote.grossAmount ?? 0) + (debitNote.taxAmount ?? 0) - (debitNote.paidAmount ?? 0);
  //         debitNoteListData.push(debitNote1);
  //       });
  //     }

  //     let statementDocumentList: DocumentList[] = invoiceListData.map(
  //       (invoice: any) => {
  //         return {
  //           documentNo: invoice.invoiceNo,
  //           documentDate: formateDate(invoice.invoiceDate),
  //           documentType: "INVOICE",
  //           documentAmount: invoice.amount,
  //           paidAmount: invoice.paidAmount,
  //           ...invoice,
  //         };
  //       }
  //     );

  //     creditNoteListData.map((creditNote: any) => {
  //       let data: DocumentList = {
  //         documentNo: creditNote?.creditNoteNo,
  //         documentDate: formateDate(creditNote.creditNoteDate),
  //         documentType: "CREDITNOTE",
  //         documentAmount: creditNote.grossAmount + creditNote.taxAmount,
  //         paidAmount: creditNote?.amount,
  //         ...creditNote,
  //       };
  //       statementDocumentList.push(data);
  //     });

  //     debitNoteListData.map((debitNote: any) => {
  //       let data: DocumentList = {
  //         documentNo: debitNote?.debitNoteNo,
  //         documentDate: formateDate(debitNote?.debitNoteDate),
  //         documentType: "DEBITNOTE",
  //         documentAmount: debitNote.grossAmount + debitNote.taxAmount,
  //         paidAmount: debitNote?.amount,
  //         ...debitNote,
  //       };
  //       statementDocumentList.push(data);
  //     });

  //     item.documentList = statementDocumentList;
  //     // .filter((document: any) => {
  //     //   const documentAmount = document.documentAmount.toFixed(2);
  //     //   const paidAmount = document.paidAmount.toFixed(2);
  //     //   const difference = documentAmount - paidAmount;
  //     //   return difference;
  //     // });
  //     item.statementAmount = statementAmount;
  //     item.paidAmount = paidAmount;
  //     item.statementStatus = item.status;
  //     return item;
  //   });
  //   setData(fullData);
  // };

  // const getCreditNoteData = async () => {
  //   const dataSource = new DataSource("creditNotes", "status=ACTIVE", true);
  //   const res: any = await dataSource.load();

  //   if (res !== null) {
  //     let nameList: SelectOption[] = [];
  //     res.map((value: any) => {
  //       nameList.push({
  //         value: value.id,
  //         label: value.creditNoteNo,
  //       });
  //     });
  //     setCreditNoteNoOption(nameList);
  //   }
  // };

  // const getDebitNoteData = async () => {
  //   const dataSource = new DataSource("debitNotes", "status=ACTIVE", true);
  //   const res: any = await dataSource.load();

  //   if (res !== null) {
  //     let nameList: SelectOption[] = [];
  //     res.map((value: any) => {
  //       nameList.push({
  //         value: value.id,
  //         label: value.debitNoteNo,
  //       });
  //     });
  //     setDebitNoteNoOption(nameList);
  //   }
  // };

  const expandedRowRender = (record: any) => {
    // Design Note: This is the table UI column where you can feed in types of column and what data to render
    const column: any = [
      {
        title: t("Statement.number"),
        dataIndex: "documentNo",
        key: "documentNo",
        render: (row: any) => {
          return <p className="tableRowNameDesign">{row}</p>;
        },
      },
      {
        title: t("Statement.type"),
        dataIndex: "documentType",
        key: "documentType",
        render: (row: any) => {
          return <p className="tableRowNameDesign">{capitalize(row)}</p>;
        },
      },
      {
        title: t("Statement.documentaAmount"),
        dataIndex: "documentAmount",
        key: "documentAmount",
        render: (row: any) => {
          return (
            <p className="tableRowNameDesign">{NumberThousandSeparator(row)}</p>
          );
        },
      },
      {
        title: t("Statement.paidAmountUsed"),
        dataIndex: "paidAmount",
        key: "paidAmount",
        render: (_: any, row: any) => {
          if (row.documentType === "CREDIT NOTE") {
            return (
              <p className="tableRowNameDesign">
                {"( " + NumberThousandSeparator(row.paidAmount) + " )"}
              </p>
            );
          } else {
            return (
              <p className="tableRowNameDesign">
                {NumberThousandSeparator(row.paidAmount)}
              </p>
            );
          }
        },
      },
      {
        title: t("Statement.processingAmount"),
        dataIndex: "processingAmount",
        key: "processingAmount",
        render: (_: any, row: any) => {
          // if (row.documentType === "CREDIT NOTE") {
          //   return (
          //     <p className="tableRowNameDesign">
          //       {"( " + NumberThousandSeparator(row.processingAmount) + " )"}
          //     </p>
          //   );
          // } else {
          return (
            <p className="tableRowNameDesign">
              {NumberThousandSeparator(row.processingAmount)}
            </p>
          );
          // }
        },
      },
      {
        title: t("Statement.invoiceDate"),
        dataIndex: "documentDate",
        key: "documentDate",
        render: (row: any) => {
          return <p className="tableRowNameDesign">{row}</p>;
        },
      },
      {
        title: t("Statement.daysDifferent"),
        dataIndex: "daysDifference",
        key: "daysDifference",
        render: (_: any, row: any) => {
          const currentDate = moment();
          const documentDate = moment(row.documentDate, "DD/MM/YYYY");
          const dateDiff = currentDate.diff(documentDate, "days");
          let statusColor: any;
          let statusBackgroundColor: any;
          let statusBorderColor: any;
          // if (row.documentType === "INVOICE") {
          if (dateDiff < 30) {
            statusColor = "greenStatusColor";
            statusBackgroundColor = "greenStatusBackgroundColor";
            statusBorderColor = "greenStatusBorderColor";
          } else if (29 < dateDiff && dateDiff < 91) {
            statusColor = "orangeStatusColor";
            statusBackgroundColor = "orangeStatusBackgroundColor";
            statusBorderColor = "orangeStatusBorderColor";
          } else {
            statusColor = "redStatusColor";
            statusBackgroundColor = "redStatusBackgroundColor";
            statusBorderColor = "redStatusBorderColor";
          }
          // }

          return (
            <div
              className={`tableRowNameDesign statusTag ${statusColor} ${statusBackgroundColor} ${statusBorderColor}`}
            >
              {dateDiff < 30 ? (
                <p className="text-green-600">&lt;30 days</p>
              ) : 29 < dateDiff && dateDiff < 91 ? (
                <p className="text-orange-600"> 60 to 90 days</p>
              ) : (
                <p className="text-red-600"> 90+ days</p>
              )}
            </div>
          );
          // <p className="tableRowNameDesign">{row.documentType === "INVOICE" ? dateDiff < 30 ? <p className="text-green-600">&lt;30 days</p> : 29 < dateDiff && dateDiff < 91 ? <p className="text-orange-600"> 60 to 90 days</p> : <p className="text-red-600"> 90+ days</p> : null}</p>;
        },
      },

      {
        // Action
        title: t("Action"),
        dataIndex: "action",
        key: "action",
        fixed: "right",
        width: 100,
        render: (_: any, record: any) => {
          // const containsRorC = /[RU]/.test(userAccess?.policies?.["goodsReturn"] || "");
          // if ((containsRorC || isAdmin || isCompanyAdmin) && record.status !== "UNVERIFIED") {
          return (
            <div className="flex items-center">
              <Button
                type="link"
                onClick={() => {
                  // setIsModalOpen(true);
                  // setModalData([record]);
                  if (record.documentType === "INVOICE") {
                    let productOrderedRecords = record?.invoiceProducts ?? [];
                    record?.invoiceToteboxes?.forEach((item: any) => {
                      if (
                        "toteboxProducts" in item &&
                        item?.toteboxProducts &&
                        item?.toteboxProducts?.length > 0
                      ) {
                        item?.toteboxProducts?.forEach((val: any) => {
                          productOrderedRecords = [
                            ...productOrderedRecords,
                            val,
                          ];
                        });
                      }
                    });
                    setInvoiceProductData(productOrderedRecords ?? []);
                    // setInvoiceProductData(record.invoiceProducts ?? []);
                    invoiceDetailForm.setFieldsValue({
                      invoiceNo: record.invoiceNo,
                      invoiceDate: formateDateAndTime(record.invoiceDate),
                      overdueDate: record.overdueDate,
                      invoiceAmount: NumberThousandSeparator(
                        record.documentAmount
                      ),
                      doNo: record.doNo,
                      doDate: formateDateAndTime(record.doDate),
                      shippingAddress: shippingAddressMap.get(
                        record.shippingAddressId
                      )?.shippingAddressDescription,
                    });
                    setIsInvoiceModalOpen(true);
                  }

                  if (record.documentType === "CREDITNOTE") {
                    setCreditNoteProductData(record.creditNoteProducts ?? []);
                    creditNoteDetailForm.setFieldsValue({
                      company: capitalize(
                        companyMap.get(record.companyId)?.name
                      ),
                      creditNoteNo: record.creditNoteNo,
                      creditNoteDate: formateDateAndTime(record.creditNoteDate),
                      invoice: invoiceMap.get(record.invoiceId)?.invoiceNo,
                      type: record.type,
                      status: record.status,
                      invoiceAmount: NumberThousandSeparator(
                        record.documentAmount
                      ),
                      grossAmount: NumberThousandSeparator(record.grossAmount),
                      taxAmount: NumberThousandSeparator(record.taxAmount),
                      nettAmount: NumberThousandSeparator(
                        (record.grossAmount ?? 0) + (record.taxAmount ?? 0)
                      ),
                    });
                    setIsCreditNoteModalOpen(true);
                  }

                  if (record.documentType === "DEBITNOTE") {
                    // setDebitNoteProductData(record.debitNoteProducts ?? []);
                    debitNoteDetailForm.setFieldsValue({
                      company: capitalize(
                        companyMap.get(record.companyId)?.name
                      ),
                      debitNoteNo: record.debitNoteNo,
                      debitNoteDate: formateDateAndTime(record.debitNoteDate),
                      invoice: invoiceMap.get(record.invoiceId)?.invoiceNo,
                      type: record.type,
                      status: record.status,
                      invoiceAmount: NumberThousandSeparator(
                        record.documentAmount
                      ),
                      grossAmount: NumberThousandSeparator(record.grossAmount),
                      taxAmount: NumberThousandSeparator(record.taxAmount),
                      nettAmount: NumberThousandSeparator(
                        (record.grossAmount ?? 0) + (record.taxAmount ?? 0)
                      ),
                    });
                    setIsDebitNoteModalOpen(true);
                  }
                }}
                className="flex items-center  text-xs ml-0 p-2"
              >
                <Tooltip title={t("ViewMore")}>
                  <EyeOutlined style={{ color: "green" }} />
                </Tooltip>
              </Button>
              {record.documentType === "INVOICE" &&
                !record.documentNo.startsWith("IV") ? (
                <Tooltip title={t("Statement.printInvoice")}>
                  {/*Function Note: Tooltip is used to showcase text when hovering over the item  */}
                  <DownloadOutlined
                    style={{ color: "orange" }}
                    onClick={() => {
                      setIsDownload(true);
                      // if (record.creditNoteDocument !== "") {
                      //   PicSignedUrl(record.creditNoteDocument!)
                      //     .then((res: any) => {
                      //       const link = document.createElement("a");
                      //       link.href = res;
                      //       link.target = "_blank"; // Open the link in a new tab (optional)
                      //       document.body.appendChild(link);
                      //       link.click();
                      //       // Cleanup: Remove the link from the DOM
                      //       document.body.removeChild(link);
                      //       setIsDownload(false);
                      //     })
                      //     .catch(() => {
                      //       setIsDownload(false);
                      //     });
                      // } else {
                      let url = "invoice/pdf?id="
                      if (record.eInvoiceSubmissionType === "EINVOICE") {
                        url = "invoice/ePdf?id="
                      }

                      apiHelper
                        .GET(url + record.id)
                        .then((res: any) => {
                          PicSignedUrl(res.item)
                            .then((res: any) => {
                              const link = document.createElement("a");
                              link.href = res;
                              link.target = "_blank"; // Open the link in a new tab (optional)
                              document.body.appendChild(link);
                              link.click();
                              // Cleanup: Remove the link from the DOM
                              document.body.removeChild(link);
                              setIsDownload(false);
                            })
                            .catch(() => {
                              setIsDownload(false);
                            });
                        })
                        .catch(() => {
                          setIsDownload(false);
                        });
                      // }
                    }}
                  />
                </Tooltip>
              ) : null}
              {record.documentType === "CREDITNOTE" ? (
                <Tooltip title={t("Statement.printCreditNote")}>
                  {/*Function Note: Tooltip is used to showcase text when hovering over the item  */}
                  <DownloadOutlined
                    style={{ color: "orange" }}
                    onClick={() => {
                      setIsDownload(true);
                      // if (record.creditNoteDocument !== "") {
                      //   PicSignedUrl(record.creditNoteDocument!)
                      //     .then((res: any) => {
                      //       const link = document.createElement("a");
                      //       link.href = res;
                      //       link.target = "_blank"; // Open the link in a new tab (optional)
                      //       document.body.appendChild(link);
                      //       link.click();
                      //       // Cleanup: Remove the link from the DOM
                      //       document.body.removeChild(link);
                      //       setIsDownload(false);
                      //     })
                      //     .catch(() => {
                      //       setIsDownload(false);
                      //     });
                      // } else {

                      let url = "creditNote/pdf?id="
                      if (record.eInvoiceSubmissionType === "EINVOICE" && record.creditNoteRefInvoices && record.creditNoteRefInvoices.length === 0) {
                        url = "creditNote/ePdf?id="
                      } else if (record.eInvoiceSubmissionType === "EINVOICE" && record.creditNoteRefInvoices && record.creditNoteRefInvoices.length > 0) {
                        url = "creditNote/ePdf/multiInvoice?id="
                      } else if (record.creditNoteRefInvoices && record.creditNoteRefInvoices.length > 0) {
                        url = "creditNote/pdf/multiInvoice?id="
                      }

                      apiHelper
                        .GET(url + record.id)
                        .then((res: any) => {
                          PicSignedUrl(res.item)
                            .then((res: any) => {
                              const link = document.createElement("a");
                              link.href = res;
                              link.target = "_blank"; // Open the link in a new tab (optional)
                              document.body.appendChild(link);
                              link.click();
                              // Cleanup: Remove the link from the DOM
                              document.body.removeChild(link);
                              setIsDownload(false);
                            })
                            .catch(() => {
                              setIsDownload(false);
                            });
                        })
                        .catch(() => {
                          setIsDownload(false);
                        });
                      // }
                    }}
                  />
                </Tooltip>
              ) : null}
              {record.documentType === "DEBITNOTE" ? (
                <Tooltip title={t("Statement.printDebitNote")}>
                  {/*Function Note: Tooltip is used to showcase text when hovering over the item  */}
                  <DownloadOutlined
                    style={{ color: "orange" }}
                    onClick={() => {
                      setIsDownload(true);
                      // if (record.debitNoteDocument !== "") {
                      //   PicSignedUrl(record.debitNoteDocument!)
                      //     .then((res: any) => {
                      //       const link = document.createElement("a");
                      //       link.href = res;
                      //       link.target = "_blank"; // Open the link in a new tab (optional)
                      //       document.body.appendChild(link);
                      //       link.click();
                      //       // Cleanup: Remove the link from the DOM
                      //       document.body.removeChild(link);
                      //       setIsDownload(false);
                      //     })
                      //     .catch(() => {
                      //       setIsDownload(false);
                      //     });
                      // } else {
                      let url = "debitNote/pdf?id="
                      if (record.eInvoiceSubmissionType === "EINVOICE") {
                        url = "debitNote/ePdf?id="
                      }

                      apiHelper
                        .GET(url + record.id)
                        .then((res: any) => {
                          PicSignedUrl(res.item)
                            .then((res: any) => {
                              const link = document.createElement("a");
                              link.href = res;
                              link.target = "_blank"; // Open the link in a new tab (optional)
                              document.body.appendChild(link);
                              link.click();
                              // Cleanup: Remove the link from the DOM
                              document.body.removeChild(link);
                              setIsDownload(false);
                            })
                            .catch(() => {
                              setIsDownload(false);
                            });
                        })
                        .catch(() => {
                          setIsDownload(false);
                        });
                      // }
                    }}
                  />
                </Tooltip>
              ) : null}
            </div>
          );
          // }
          // return null;
        },
      },
    ];

    //   const data1: ProductTransfers[] = record.productTransfers || [];
    const data1: DocumentList[] = record.documentList || [];

    // dataSource using [data[index] as using same dataSource as other columns
    return (
      <Table
        columns={column}
        dataSource={data1}
        bordered
        pagination={false}
        className="my-2"
      />
    );
  };

  const column = [
    // {
    //   // Outlet Code
    //   title: t("Outlet") + " " + t("Code"),
    //   dataIndex: "outletCode",
    //   onFilter: (value: string, record: { outletId: string }) =>
    //     record.outletId.indexOf(value) === 0,
    //   sorter: (a: any, b: any) => {
    //     const first = outletMap.get(a.outletId)?.outletCode || "";
    //     const second = outletMap.get(b.outletId)?.outletCode || "";

    //     return first?.localeCompare(second);
    //   },
    //   showSorterTooltip: false,
    //   key: "outletCode",
    //   render: (_: any, record: StatementList) => {
    //     return (
    //       <p className="tableRowNameDesign">
    //         {outletMap.get(record.outletId)?.outletCode}
    //       </p>
    //     );
    //   },
    // },
    // {
    //   // Outlet Name
    //   title: t("Statement.Listing.3"),
    //   dataIndex: "outletId",
    //   onFilter: (value: string, record: { outletId: string }) =>
    //     record.outletId.indexOf(value) === 0,
    //   sorter: (a: any, b: any) => {
    //     const first = outletMap.get(a.outletId)?.name || "";
    //     const second = outletMap.get(b.outletId)?.name || "";

    //     return first?.localeCompare(second);
    //   },
    //   showSorterTooltip: false,
    //   key: "outletId",
    //   render: (_: any, record: StatementList) => {
    //     return (
    //       <p className="tableRowNameDesign">
    //         {outletMap.get(record.outletId)?.name}
    //       </p>
    //     );
    //   },
    // },
    {
      // Statement No
      title: t("Statement.statementNo"),
      dataIndex: "statementNo",
      sorter: (a: any, b: any) => a.statementNo.localeCompare(b.statementNo),
      showSorterTooltip: false,
      key: "statementNo",
      render: (_: any, record: StatementList) => {
        return <p className="tableRowNameDesign">{record.statementNo}</p>;
      },
    },
    {
      // Statement Date
      title: t("Statement.statementDate"),
      dataIndex: "date",
      sorter: (a: any, b: any) => moment(a.date).unix() - moment(b.date).unix(),
      showSorterTooltip: false,
      key: "date",
      render: (_: any, record: StatementList) => {
        return (
          <p className="tableRowNameDesign">
            {formateDateAndTime(record.date)}
          </p>
        );
      },
    },
    {
      // Statement Amount
      title: t("Statement.statementAmount"),
      dataIndex: "statementAmount",
      sorter: (a: any, b: any) => a.statementAmount - b.statementAmount,
      showSorterTooltip: false,
      key: "statementAmount",
      render: (_: any, record: StatementList) => {
        return (
          <p className="tableRowNameDesign">
            {NumberThousandSeparator(record.statementAmount ?? 0)}
          </p>
        );
      },
    },
    {
      // Paid Amount
      title: t("Statement.paidAmount"),
      dataIndex: "paidAmount",
      sorter: (a: any, b: any) => a.paidAmount - b.paidAmount,
      showSorterTooltip: false,
      key: "paidAmount",
      render: (_: any, record: StatementList) => {
        return (
          <p className="tableRowNameDesign">
            {NumberThousandSeparator(record.paidAmount ?? 0)}
          </p>
        );
      },
    },
    {
      // Paid Amount
      title: t("Statement.outstandingAmount"),
      dataIndex: "outstanding",
      sorter: (a: any, b: any) => a.outstanding - b.outstanding,
      showSorterTooltip: false,
      key: "outstanding",
      render: (_: any, record: StatementList) => {
        return (
          <p className="tableRowNameDesign">
            {NumberThousandSeparator(record.outstandingAmount ?? 0)}
          </p>
        );
      },
    },
    {
      title: t("Statement.dueDate"),
      dataIndex: "dueDate",
      sorter: (a: any, b: any) =>
        moment(a.dueDate).unix() - moment(b.dueDate).unix(),
      showSorterTooltip: false,
      key: "dueDate",
      render: (_: any, record: any) => {
        return (
          <p className="tableRowNameDesign">
            {formateDateAndTime(record.dueDate)}
          </p>
        );
      },
    },
    {
      // Statement status
      title: t("Statement.status"),
      dataIndex: "statementStatus",
      sorter: (a: any, b: any) =>
        a.statementStatus.localeCompare(b.statementStatus),
      showSorterTooltip: false,
      key: "statementStatus",
      width: 150,
      render: (_: any, record: StatementList) => {
        return statusApproval(record);
      },
    },
    // {
    //   // Action
    //   title: t("Action"),
    //   dataIndex: "action",
    //   key: "action",
    //   fixed: "right",
    //   width: 100,
    //   render: (_: any, record: StatementList) => {
    //     // const containsRorC = /[RU]/.test(userAccess?.policies?.["goodsReturn"] || "");
    //     // if ((containsRorC || isAdmin || isCompanyAdmin) && record.status !== "UNVERIFIED") {
    //     return (
    //       <div className="flex">
    //         <Button
    //           type="link"
    //           onClick={() => {}}
    //           className="flex items-center  text-xs ml-0 p-2"
    //         >
    //           <Tooltip title={t("ViewMore")}>
    //             <EyeOutlined style={{ color: "green" }} />
    //           </Tooltip>
    //         </Button>
    //       </div>
    //     );
    //     // }
    //     // return null;
    //   },
    // },
  ];

  const rowSelection = {
    onChange: (selectedRowKeys: string[], selectedRows: []) => {
      setSelectedRowData(selectedRows);
      setSelectedRowKeys(selectedRowKeys);
    },

    // getCheckboxProps: (record: any) => {
    //   if (
    //     record.statementStatus === "COMPLETED" ||
    //     record.statementStatus === "PENDINGVERIFY"
    //   ) {
    //     return {
    //       disabled: true,
    //     };
    //   }
    // },
  };

  const generatePDF = () => {
    setPDFLoading(true);

    selectedRowData.forEach((item: StatementList) => {
      // if (item.document !== "") {
      //   PicSignedUrl(item.document!)
      //     .then((res: any) => {
      //       const link = document.createElement("a");
      //       link.href = res;
      //       link.target = "_blank"; // Open the link in a new tab (optional)
      //       document.body.appendChild(link);
      //       link.click();
      //       // Cleanup: Remove the link from the DOM
      //       document.body.removeChild(link);
      //     })
      //     .catch(() => {
      //       setPDFLoading(false);
      //     });
      // } else {
      apiHelper
        .GET("statement/pdf?id=" + item.id)
        .then((res: any) => {
          PicSignedUrl(res.item)
            .then((res: any) => {
              const link = document.createElement("a");
              link.href = res;
              link.target = "_blank"; // Open the link in a new tab (optional)
              document.body.appendChild(link);
              link.click();
              // Cleanup: Remove the link from the DOM
              document.body.removeChild(link);
            })
            .catch(() => {
              setPDFLoading(false);
            });
        })
        .catch(() => {
          setPDFLoading(false);
        });
      // }
    });

    setPDFLoading(false);
  };

  const handleFirstButtonClick = () => {
    const concatenatedString: string = selectedRowKeys.join("/");
    localStorage.setItem("paymentSelected", concatenatedString);

    // router.push(`/payment/toPay?ids=${selectedRowKeys}`);
    router.push(`/payment/toPay?paymentMade=paymentSelected`);
  };

  const buttons = [
    {
      label: t("Statement.makePayment"),
      onClick: handleFirstButtonClick,
      disabled:
        selectedRowData.length === 0
          ? !(selectedRowData.length > 0 || selectedRowData.length > 1)
          : selectedRowData.some(
            (item: any) => item.statementStatus !== "UNSETTLED"
          ),
    },
    {
      label: t("Statement.printStatement"),
      onClick: generatePDF,
      disabled: !(selectedRowData.length > 0),
    },
  ];

  // *************************************************************************************
  // *** Filter Modal ***
  // *************************************************************************************

  const filterFormOnfinish = (values: any) => {
    setModalFilter(values);
  };

  const searchStatement = (values: any) => {
    //convert to empty string when no value is entered as default value is undefined.
    for (const key in values) {
      if (values[key] === undefined) {
        values[key] = "";
      }
    }

    // return true or false
    let isAnyKeyFilled = Object.keys(values).some(
      (key) => values[key] !== "" && values[key] !== undefined
    );

    const date = values.statementDate
      ? values.statementDate.format("YYYY-MM-DDT00:00:00") + "Z"
      : "";

    let currentOutletId = localStorage.getItem("currentOutletId");

    const params =
      encodeParams({
        companyId: retailerAccess.companyId,
        // companyBranchId: retailerAccess.companyBranchId,
        outletId: currentOutletId,
        fuzzySearch: values.fuzzySearch,
        statementNo: values.statementNo || "",
        statementDate: date,
        invoiceId: values.invoiceNo || "",

        creditNoteId: values.creditNoteNo || "",
        debitNoteId: values.debitNoteNo || "",

        status: values.status,
      }) + "&sort=createdAt&sortOrder=-1";

    if (isAnyKeyFilled) {
      // setCursor("0");
      setFilterSetting(params);
    }
  };

  const items = statementStatusFilterOption;
  const handleStatusMenuClick: MenuProps["onClick"] = ({ key }) => {
    setStatusKey(key);
    // Access the label property of the selected item
    const selectedLabel = items.find(
      (menuItem: any) => menuItem.key === key
    )?.label;
    if (selectedLabel) {
      setStatusValue(selectedLabel);
    }
  };

  const props: UploadProps = {
    name: "file",
    multiple: false,
    maxCount: 1,
    showUploadList: {
      showPreviewIcon: false,
    },
    beforeUpload: (file) => {
      if (
        file.type !== "image/png" &&
        file.type !== "image/jpg" &&
        file.type !== "image/jpeg" &&
        file.type !== "application/pdf"
      ) {
        MessageErrorUI(
          `${file.name} is an invalid file format. Please change the file extension to either .pdf, .png, .jpg, .jpeg.`
        );
        return Upload.LIST_IGNORE;
      } else if (file.size > 5242880) {
        MessageErrorUI(
          `${file.name} is too large. Please upload another document that is smaller than 5MB.`
        );
        return Upload.LIST_IGNORE;
      } else {
        return false;
      }
    },
  };

  const productColumn = [
    {
      title: t("Invoice.product"),
      dataIndex: "productId",
      sorter: (a: any, b: any) => a.productId.localeCompare(b.productId),
      showSorterTooltip: false,
      key: "id",
      render: (_: any, record: ProductOrdered) => {
        const item = productMap.get(record.productId);
        if (item) {
          return (
            <div>
              <Col className="flex items-center w-full">
                <img
                  className="object-contain h-[80px] min-w-[80px] p-2"
                  src={
                    item.productUOM.find(
                      (item: ProductUOM) =>
                        record.productUOMId === item.productUOMId
                    )?.pictures
                      ? PUBLIC_BUCKET_URL +
                      item.productUOM.find(
                        (item: ProductUOM) =>
                          record.productUOMId === item.productUOMId
                      )?.pictures[1]
                      : defaultImage.src
                  }
                  loading="lazy"
                ></img>
                <div className="flex flex-col w-full">
                  <p className="font-bold text-[14px]">{item.name}&nbsp;</p>
                  <p className="text-gray-500 text-[10px] w-full flex ">
                    <span>
                      {t("Invoice.productCode")}: {item.sku}
                    </span>
                  </p>
                  <p className="text-gray-500 text-[10px] w-full flex ">
                    <span>
                      {t("Invoice.uom")}:{" "}
                      {uomMap.get(record.productUOMId)?.name}
                    </span>
                  </p>
                  <p className="text-gray-500 text-[10px] w-full flex ">
                    <span>
                      {t("Invoice.unitPrice")}: {record.price}
                    </span>
                  </p>
                </div>
              </Col>
            </div>
          );
        } else return null;
      },
    },

    {
      title: t("Invoice.quantity"),
      dataIndex: "quantity",
      sorter: (a: any, b: any) => a.quantity.localeCompare(b.quantity),
      showSorterTooltip: false,
      key: "quantity",
      render: (_: any, record: ProductOrdered) => {
        return (
          <p className="tableRowNameDesign">
            {record.quantity} {uomMap.get(record.productUOMId)?.name}
          </p>
        );
      },
    },
    {
      title: t("Invoice.discount"),
      dataIndex: "discount",
      sorter: (a: any, b: any) => a.discount.localeCompare(b.discount),
      showSorterTooltip: false,
      key: "discount",
      render: (_: any, record: ProductOrdered) => {
        return (
          <p className="tableRowNameDesign">
            {NumberThousandSeparator(record.discount ?? 0)}
          </p>
        );
      },
    },
    {
      title: t("Invoice.totalPrice"),
      dataIndex: "totalPrice",
      sorter: (a: any, b: any) => a.totalPrice.localeCompare(b.totalPrice),
      showSorterTooltip: false,
      key: "totalPrice",
      render: (_: any, record: ProductOrdered) => {
        const total =
          (record.price ?? 0) * (record.quantity ?? 0) - (record.discount ?? 0);
        return (
          <p className="tableRowNameDesign">{NumberThousandSeparator(total)}</p>
        );
      },
    },
  ];

  const invoiceModal = () => {
    return (
      <div className="w-full">
        <Col>
          <Form
            className="w-full pt-3.5"
            form={invoiceDetailForm}
            layout="vertical"
            scrollToFirstError
          >
            <Row className="flex flex-row space-x-4">
              <Form.Item
                name="invoiceNo"
                className="flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Invoice.invoice") + " " + t("Common.no")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={
                    t("Common.eg.") + " " + t("Placeholder.OutletName")
                  }
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="invoiceDate"
                className="flex-1"
                // rules={[
                //   {
                //     required: true,
                //     message: t("ssm") + " " + t("Validation.requiredField"),
                //   },
                // ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Invoice.invoiceDate")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.eg.") + " " + t("Placeholder.Email")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>

            <Row className="flex flex-row space-x-4">
              <Form.Item
                name="overdueDate"
                className="flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Invoice.overdueDate")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.eg") + " " + t("Invoice.overdueDate")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="invoiceAmount"
                className="flex-1"
                // rules={[{ required: true, mess age: t("GPSLocation") + " " + t("Validation.Is.Required") }]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Invoice.invoiceAmount")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.eg") + " " + t("Invoice.unitNo")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>
            <Row className="flex flex-row space-x-4">
              <Form.Item
                name="doNo"
                className="flex-1"
                // rules={[
                //   {
                //     required: true,
                //     message: t("Address1") + " " + t("Validation.Is.Required"),
                //   },
                // ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Invoice.do") + " " + t("Common.no")}
                  </p>
                }
              >
                <FormTextInput disabled placeholder={""} maxLength={100} />
              </Form.Item>
              <Form.Item
                name="doDate"
                className="flex-1"
                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Invoice.doDate")}
                  </p>
                }
              >
                <FormTextInput disabled placeholder={""} maxLength={100} />
              </Form.Item>
            </Row>
            <Row className="flex flex-row space-x-4">
              <Form.Item
                name="shippingAddress"
                className="flex-1"
                // rules={[
                //   {
                //     required: true,
                //     message: t("City") + " " + t("Validation.Is.Required"),
                //   },
                // ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Invoice.shippingAddress")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.15")}
                  maxLength={100}
                />
                {/* disabled={cityList.length !== 0 ? false : true} /> */}
              </Form.Item>
            </Row>
            <Form.Item
              className="flex-1 p-2"
              label={
                <p className="text-neutral700 text-[12px]">
                  {t("Invoice.supportedDocument")}
                </p>
              }
            >
              <Upload
                {...props}
                disabled
                onPreview={(val: any) => {
                  // only uploaded photo can download and show
                  // new upload wont be able to click
                  if (val.url != undefined) {
                    let link = document.createElement("a");
                    link.target = "_blank";
                    link.href = val.url;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  }
                }}
                onChange={(info) => {
                  if (info.fileList.length > 0) {
                    if (
                      info.fileList.at(-1) !== undefined &&
                      info.file.status !== "removed"
                    ) {
                      let file = info.fileList.at(-1);
                      if (file !== undefined) {
                        file.status = "done";
                        let fileObj = file.originFileObj;
                        setAllFiles({
                          ...allFiles,
                          doDocument: fileObj,
                        });
                      }
                    }
                  }
                }}
                onRemove={() => {
                  setAllFiles({
                    ...allFiles,
                    ["doDocument"]: null,
                  });
                }}
                fileList={
                  allFiles["doDocument"] === undefined ||
                    allFiles["doDocument"] === null
                    ? []
                    : [allFiles["doDocument"]]
                }
              >
                <div className="flex items-center gap-x-4 p-2 text-buttonPurple bg-lightPurple font-semibold">
                  <PlusOutlined />
                  <p>{t("Invoice.supportedDocument")}</p>
                </div>
              </Upload>
            </Form.Item>
            <span className="m-1"></span>
            <ListingTableUI
              // EditableCell={EditableCell}
              bordered
              dataSource={invoiceProductData}
              columns={productColumn}
              // rowClassName="editable-row"
              rowKey={(record: any) => record.id}
              cursor={false}
              // loader={showButtonLoader}
              pagination={false}
              endMessage={""}
              loading={tableLoading}
            />
          </Form>
        </Col>
      </div>
    );
  };

  const creditNoteModal = () => {
    return (
      <div className="w-full">
        <Col>
          <Form
            className="w-full pt-3.5"
            form={creditNoteDetailForm}
            layout="vertical"
            scrollToFirstError
          >
            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="creditNoteNo"
                className="flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("CreditNote.creditNote") + " " + t("Common.no")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={
                    t("Common.eg.") + " " + t("Placeholder.OutletName")
                  }
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="creditNoteDate"
                className="flex-1"
                // rules={[
                //   {
                //     required: true,
                //     message: t("ssm") + " " + t("Validation.requiredField"),
                //   },
                // ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("CreditNote.creditNoteDate")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.eg") + " " + t("common.email")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>

            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="company"
                className="flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("CreditNote.company")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.egEg") + " " + t("CreditNote.unitNo")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="invoice"
                className="flex-1"
                // rules={[{ required: true, mess age: t("GPSLocation") + " " + t("Validation.Is.Required") }]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("CreditNote.invoice") + " " + t("Common.no")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.eg") + " " + t("CreditNote.unitNo")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>
            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="type"
                className="flex-1"
                // rules={[
                //   {
                //     required: true,
                //     message: t("Address1") + " " + t("Validation.Is.Required"),
                //   },
                // ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("CreditNote.type")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.11")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="status"
                className="flex-1"
                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.status")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.12")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>
            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="grossAmount"
                className="flex-1"
                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                  <p className="text-neutral700 text-[12px]">
                    {t("CreditNote.grossAmount") + " " + t("RM")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.12")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="taxAmount"
                className="flex-1"
                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                  <p className="text-neutral700 text-[12px]">
                    {t("CreditNote.taxAmount") + " " + t("RM")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.12")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="nettAmount"
                className="flex-1"
                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                  <p className="text-neutral700 text-[12px]">
                    {t("CreditNote.nettAmount") + " " + t("RM")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.12")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>
            <Form.Item
              className="flex-1 p-2"
              label={
                <p className="text-neutral700 text-[12px]">
                  {t("CreditNote.supportedDocument")}
                </p>
              }
            >
              <Upload
                {...props}
                disabled
                onPreview={(val: any) => {
                  // only uploaded photo can download and show
                  // new upload wont be able to click
                  if (val.url != undefined) {
                    let link = document.createElement("a");
                    link.target = "_blank";
                    link.href = val.url;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  }
                }}
                onChange={(info) => {
                  if (info.fileList.length > 0) {
                    if (
                      info.fileList.at(-1) !== undefined &&
                      info.file.status !== "removed"
                    ) {
                      let file = info.fileList.at(-1);
                      if (file !== undefined) {
                        file.status = "done";
                        let fileObj = file.originFileObj;
                        setAllFiles({
                          ...allFiles,
                          doDocument: fileObj,
                        });
                      }
                    }
                  }
                }}
                onRemove={() => {
                  setAllFiles({
                    ...allFiles,
                    ["doDocument"]: null,
                  });
                }}
                fileList={
                  allFiles["doDocument"] === undefined ||
                    allFiles["doDocument"] === null
                    ? []
                    : [allFiles["doDocument"]]
                }
              >
                <div className="flex items-center gap-x-4 p-2 text-buttonPurple bg-lightPurple font-semibold">
                  <PlusOutlined />
                  <p>{t("CreditNote.supportedDocument")}</p>
                </div>
              </Upload>
            </Form.Item>
            <span className="m-1"></span>
            {creditNoteProductData.length > 0 ? (
              <ListingTableUI
                // EditableCell={EditableCell}
                bordered
                dataSource={creditNoteProductData}
                columns={productColumn}
                // rowClassName="editable-row"
                rowKey={(record: any) => record.id}
                cursor={false}
                // loader={showButtonLoader}
                pagination={false}
                endMessage={""}
                loading={tableLoading}
              />
            ) : null}
          </Form>
        </Col>
      </div>
    );
  };

  const debitNoteModal = () => {
    return (
      <div className="w-full">
        <Col>
          <Form
            className="w-full pt-3.5"
            form={debitNoteDetailForm}
            layout="vertical"
            scrollToFirstError
          >
            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="debitNoteNo"
                className="flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("DebitNote.debitNote") + " " + t("Common.no")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.eg") + " " + t("DebitNote.OutletName")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="debitNoteDate"
                className="flex-1"
                // rules={[
                //   {
                //     required: true,
                //     message: t("ssm") + " " + t("Validation.requiredField"),
                //   },
                // ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("DebitNote.debitNoteDate")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.e") + " " + t("Placeholder.Email")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>

            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="company"
                className="flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("DebitNote.company")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.eg") + " " + t("DebitNote.unitNo")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="invoice"
                className="flex-1"
                // rules={[{ required: true, mess age: t("GPSLocation") + " " + t("Validation.Is.Required") }]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("DebitNote.invoice")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.eg") + " " + t("DebitNote.unitNo")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>
            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="type"
                className="flex-1"
                // rules={[
                //   {
                //     required: true,
                //     message: t("Address1") + " " + t("Validation.Is.Required"),
                //   },
                // ]}
                label={
                  <p className="text-neutral700 text-[12px]">{t("Type")}</p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.11")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="status"
                className="flex-1"
                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                  <p className="text-neutral700 text-[12px]">{t("Status")}</p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.12")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>
            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="grossAmount"
                className="flex-1"
                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                  <p className="text-neutral700 text-[12px]">
                    {t("DebitNote.grossAmount") + " " + t("RM")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.12")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="taxAmount"
                className="flex-1"
                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                  <p className="text-neutral700 text-[12px]">
                    {t("DebitNote.taxAmount") + " " + t("RM")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.12")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="nettAmount"
                className="flex-1"
                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                  <p className="text-neutral700 text-[12px]">
                    {t("DebitNote.nettAmount") + " " + t("RM")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.12")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>
            <Form.Item
              className="flex-1 p-2"
              label={
                <p className="text-neutral700 text-[12px]">
                  {t("DebitNote.supportedDocument")}
                </p>
              }
            >
              <Upload
                {...props}
                disabled
                onPreview={(val: any) => {
                  // only uploaded photo can download and show
                  // new upload wont be able to click
                  if (val.url != undefined) {
                    let link = document.createElement("a");
                    link.target = "_blank";
                    link.href = val.url;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  }
                }}
                onChange={(info) => {
                  if (info.fileList.length > 0) {
                    if (
                      info.fileList.at(-1) !== undefined &&
                      info.file.status !== "removed"
                    ) {
                      let file = info.fileList.at(-1);
                      if (file !== undefined) {
                        file.status = "done";
                        let fileObj = file.originFileObj;
                        setAllFiles({
                          ...allFiles,
                          doDocument: fileObj,
                        });
                      }
                    }
                  }
                }}
                onRemove={() => {
                  setAllFiles({
                    ...allFiles,
                    ["doDocument"]: null,
                  });
                }}
                fileList={
                  allFiles["doDocument"] === undefined ||
                    allFiles["doDocument"] === null
                    ? []
                    : [allFiles["doDocument"]]
                }
              >
                <div className="flex items-center gap-x-4 p-2 text-buttonPurple bg-lightPurple font-semibold">
                  <PlusOutlined />
                  <p>{t("DebitNote.supportedDocument")}</p>
                </div>
              </Upload>
            </Form.Item>
            <span className="m-1"></span>
            {creditNoteProductData.length > 0 ? (
              <ListingTableUI
                // EditableCell={EditableCell}
                bordered
                dataSource={creditNoteProductData}
                columns={productColumn}
                // rowClassName="editable-row"
                rowKey={(record: any) => record.id}
                cursor={false}
                // loader={showButtonLoader}
                pagination={false}
                endMessage={""}
                loading={tableLoading}
              />
            ) : null}
          </Form>
        </Col>
      </div>
    );
  };

  const filterModal = () => {
    return (
      <div className="w-full">
        <Form
          onFinish={filterFormOnfinish}
          form={filterModalForm}
          className=""
          layout="vertical"
        >
          <h1 className="font-bold text-base pb-4 hidden sm:flex">
            {t("Filter")}
          </h1>
          {/* First Row of Filter Input */}
          <Row className="filterBlockForm flex-col space-y-3">
            <Row className="flex sm:flex-row flex-col gap-x-4 sm:gap-y-0 gap-y-4">
              <Form.Item
                name="statementNo"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("Statement.statement") +
                      " " +
                      t("Common.no") +
                      "?"}
                  </p>
                }
              >
                <SelectInput
                  placeholder={t("Common.eg") + " " + t("Statement.statement")}
                  options={statementNoOption}
                />
              </Form.Item>
              <Form.Item
                name="statementDate"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("Statement.statementDate") +
                      "?"}
                  </p>
                }
              >
                <SingleDateInput
                  placeholder={
                    t("SearchBy") + " " + t("Statement.statementDate")
                  }
                  onChange={() => {
                    filterForm.submit();
                  }}
                />
              </Form.Item>
            </Row>

            <Row className="flex sm:flex-row flex-col gap-x-4 sm:gap-y-0 gap-y-4">
              <Form.Item
                name="invoiceNo"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("Statement.invoice") +
                      " " +
                      t("Common.no") +
                      "?"}
                  </p>
                }
              >
                <SelectInput
                  placeholder={
                    t("Common.eg") +
                    " " +
                    t("Statement.invoice") +
                    " " +
                    t("Common.no")
                  }
                  options={invoiceNoOption}
                />
              </Form.Item>
              <Form.Item
                name="creditNoteNo"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("Statement.creditNote") +
                      " " +
                      t("Common.no") +
                      "?"}
                  </p>
                }
              >
                <SelectInput
                  placeholder={
                    t("Common.eg") +
                    " " +
                    t("Statement.creditNote") +
                    " " +
                    t("Common.no")
                  }
                  options={creditNoteNoOption}
                />
              </Form.Item>
            </Row>
            <Row className="flex sm:flex-row flex-col gap-x-4 sm:gap-y-0 gap-y-4">
              <Form.Item
                name="debitNoteNo"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("Statement.debitNote") +
                      " " +
                      t("Common.no") +
                      "?"}
                  </p>
                }
              >
                <SelectInput
                  placeholder={
                    t("Common.eg") +
                    " " +
                    t("Statement.debitNote") +
                    " " +
                    t("Common.no")
                  }
                  options={debitNoteNoOption}
                />
              </Form.Item>
              <Form.Item name="" className="mb-0 flex-1">
                {/* <RangePickerInput /> */}
              </Form.Item>
            </Row>
          </Row>
          <Row className="flex pt-8 justify-between">
            <PrimaryButtonUI
              label={t("Common.resetAll")}
              onClick={() => {
                filterModalForm.resetFields();
                // setParamsFromLocalStorage(router.pathname, `status=${statusKey}`, "productFilter");
              }}
            />
            {/* <Row> */}
            <SecondaryButtonUI
              label={t("Common.cancel")}
              htmlType="reset"
              onClick={() => {
                setModalFilter({});
                setIsFilterModalOpen(false);
              }}
            />
            <PrimaryButtonUI
              label={t("Common.applyFilter")}
              htmlType="submit"
              onClick={() => {
                setIsFilterModalOpen(false);
              }}
            />
            {/* </Row> */}
          </Row>
        </Form>
      </div>
    );
  };

  const showContent = () => {
    return (
      <div>
        <BackButtonUI
          // label={t("Common.back")}
          // onClick={() => router.push("/landing")}
          // icon={<LeftOutlined />}
          buttons={buttons}
          title={t("Statement.statement")}
        ></BackButtonUI>
        <Row className="mb-4 w-full">
          <FilterFormComponent
            filterForm={filterForm}
            isShowFuzzySearchInput={false}
            // onDebouncedChange={(value) => {
            //   filterModalForm.resetFields();
            //   setModalFilter({});
            //   setFuzzySearchFilter(value);
            // }}
            // fieldName={fieldName}
            clearButtonOnChange={() => {
              filterForm.resetFields();
              setStatusKey("ALL");
              setStatusValue("All");
              // setFuzzySearchFilter("");
              setModalFilter({});
              filterModalForm.resetFields();
              // setData([...fullData]);
              setShowClearFilter(false);
              // setCursor(tempCursor);
              setFilterSetting("");
              localStorage.removeItem("statementFilter");
            }}
            filterModalButtonOnClick={() => {
              setIsFilterModalOpen(true);
              filterForm.resetFields();
              // setFuzzySearchFilter("");
            }}
            modalFilterValue={modalFilter}
            option={items}
            handleStatusMenuClick={handleStatusMenuClick}
            clearFilterDisable={showClearFilter === true ? false : true}
            statusValue={statusValue}
          // debounceValue={fuzzySearchFilter}
          ></FilterFormComponent>
        </Row>
        <ListingTableUI
          // EditableCell={EditableCell}
          bordered
          dataSource={data}
          columns={column}
          // rowClassName="editable-row"
          rowKey={(record: any) => record.id}
          cursor={cursor}
          // loader={showButtonLoader}
          pagination={false}
          rowSelection={rowSelection}
          expandable={{
            expandIcon: ExpandIcon,
            expandedRowRender,
            expandedRowKeys,
            onExpand: (expanded, record) => {
              if (expanded) {
                setExpandedRowKeys([...expandedRowKeys, record?.id || ""]);
              } else {
                setExpandedRowKeys(
                  expandedRowKeys.filter((key) => key !== record?.id)
                );
              }
            },
          }}
          loading={tableLoading}
        />
      </div>
    );
  };

  const showDownload = () => (
    <Modal className="mt-24" closable={false} open={isDownload} footer={null}>
      <div className="h-160px flex justify-center items-center flex-col">
        <p className="font-bold text-xl mb-4">Downloading...</p>
        <Progress type="circle" percent={0 * 100} />
      </div>
    </Modal>
  );

  return (
    <div className="flex flex-col w-full min-h-screen bg-bgOrange">
      <Header items={headerItems} hasSearch={false} values={() => { }} />
      <Content className="flex flex-col mt-3 w-full sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2">
        {showContent()}
        {showDownload()}
      </Content>
      <Row className="justify-center w-full pt-4">
        {showScrollButton && (
          <div className="fixed bottom-20 right-8 z-50">
            <button
              className={`flex items-center justify-center rounded-full w-10 h-10 text-white text-lg font-semibold focus:outline-none bg-blue-500 hover:bg-blue-600`}
              onClick={handleScrollToTop}
            >
              <ArrowUpOutlined style={{ fontSize: "24px" }} />
            </button>
          </div>
        )}
      </Row>
      {isSmallScreen ? (
        <Drawer
          title="Filter"
          placement="bottom"
          closable={false}
          onClose={() => setIsFilterModalOpen(false)}
          open={isFilterModalOpen}
          height="80vh"
          className="rounded-t-lg"
        >
          {filterModal()}
        </Drawer>
      ) : (
        <ModalUI
          // title={"More Filter"}
          width="70%"
          className={"modalFilterBody"}
          visible={isFilterModalOpen}
          onOk={() => setIsFilterModalOpen(false)}
          onCancel={() => setIsFilterModalOpen(false)}
          content={filterModal()}
          title={""}
        ></ModalUI>
      )}
      <ModalUI
        title={t("Invoice.details")}
        width="80%"
        visible={isInvoiceModalOpen}
        // onOk={handleOk}
        onCancel={() => setIsInvoiceModalOpen(false)}
        content={invoiceModal()}
      ></ModalUI>
      <ModalUI
        title={t("Invoice.details")}
        width="80%"
        visible={isCreditNoteModalOpen}
        // onOk={handleOk}
        onCancel={() => setIsCreditNoteModalOpen(false)}
        content={creditNoteModal()}
      ></ModalUI>
      <ModalUI
        title={t("Invoice.details")}
        width="80%"
        visible={isDebitNoteModalOpen}
        // onOk={handleOk}
        onCancel={() => setIsDebitNoteModalOpen(false)}
        content={debitNoteModal()}
      ></ModalUI>
      <AppFooter retailerAccessValues={retailerAccess} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default StatementListing;
