import React, { use, useEffect, useState } from "react";
import { Content } from "antd/lib/layout/layout";
import defaultImage from "../../assets/default/emptyImage.png";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import {
  Button,
  Card,
  Col,
  Drawer,
  Form,
  MenuProps,
  Row,
  Steps,
  Tooltip,
  Upload,
  UploadProps,
} from "antd";
import Header, { supportedLocales } from "../../components/header";
import {
  FormTextInput,
  RangePickerInput,
  SelectInput,
  SingleDateInput,
} from "@/components/input";
import {
  BackButtonUI,
  PrimaryButtonUI,
  SecondaryButtonUI,
} from "@/components/buttonUI";
import { useRouter } from "next/router";
import { ArrowUpOutlined, EyeOutlined, PlusOutlined } from "@ant-design/icons";
import {
  getStatusStyles,
  ListingTableUI,
  MessageErrorUI,
  MessageInfoUI,
  MessageSuccessUI,
  statusApproval,
} from "@/components/ui";
import {
  DataSource,
  PicSignedUrl,
  encodeParams,
  PUBLIC_BUCKET_URL,
  NumberThousandSeparator,
  formateDateAndTime,
  setParamsFromLocalStorage,
  getParamsFromLocalStorage,
  setFilterForm,
  getSelectOptions,
  SignedUrl,
  DataSourceWithPageNumber,
} from "@/stores/utilize";
import {
  Invoice,
  Outlet,
  Product,
  ProductUOM,
  UOM,
  ProductOrdered,
  SalesOrder,
  OutletShippingAddress,
  User,
  SelectOption,
  Retailer,
  InvoiceToteBoxes,
  Totebox,
} from "@/components/type";
import { ModalUI } from "@/components/modalUI";
import moment from "moment";
import useRetailerStore from "@/stores/store";
import { getRetailerData } from "@/stores/authContext";
import {
  invoiceStatusFilterOption,
  requiredEinvoiceOption,
} from "@/components/config";
import FilterFormComponent from "@/components/filter";
import { ComponentFilterSelect } from "@/components/filterSelectInput";
import AppFooter from "@/components/footer";
import apiHelper from "../api/apiHelper";
import { PDFDocument } from "pdf-lib";
import { RcFile, UploadChangeParam, UploadFile } from "antd/lib/upload";
import { capitalize } from "lodash";
import OrderInactiveIcon from "../../assets/orderStatusLogo/orderGray.svg";
import OrderActiveIcon from "../../assets/orderStatusLogo/orderGreen.svg";
import DeliveryInactiveIcon from "../../assets/orderStatusLogo/deliveryGray.svg";
import DeliveryActiveIcon from "../../assets/orderStatusLogo/deliveryGreen.svg";
import UnloadInactiveIcon from "../../assets/orderStatusLogo/unloadGray.svg";
import UnloadActiveIcon from "../../assets/orderStatusLogo/unloadGreen.svg";

function InvoiceListing() {
  const { t } = useTranslation("common");
  const router = useRouter();
  const [filterForm] = Form.useForm();
  const [filterModalForm] = Form.useForm();
  const [invoiceDetailForm] = Form.useForm();
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [showButtonLoader, setShowButtonLoader] = useState(false);
  const [cursor, setCursor] = useState("");
  const [tempCursor, setTempCursor] = useState("");
  const [tableLoading, setTableLoading] = useState(false);
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({});
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const [salesOrderMap, setSalesOrderMap] = useState(new Map());
  const [outletMap, setOutletMap] = useState(new Map());
  const [productMap, setProductMap] = useState(new Map());
  const [shippingAddressMap, setShippingAddressMap] = useState(new Map());
  const [uomMap, setUomMap] = useState(new Map());
  const [toteboxMap, setToteboxMap] = useState(new Map());
  const [invoiceProductData, setInvoiceProductData] = useState<
    ProductOrdered[]
  >([]);
  const [invoiceToteboxProductData, setInvoiceToteboxProductData] = useState<
    InvoiceToteBoxes[]
  >([]);

  // const [productNameOption, setProductNameOption] = useState<SelectOption[]>(
  //   []
  // );
  // const [productSkuOption, setProductSkuOption] = useState<SelectOption[]>([]);
  // const [salesOrderNoOption, setSalesOrderNoOption] = useState<SelectOption[]>(
  //   []
  // );

  const [pdfLoading, setPDFLoading] = useState(false);
  const [allFiles, setAllFiles] = useState<{ [key: string]: any }>({});
  const [previewImage, setPreviewImage] = useState<UploadFile[]>([]);
  const [picture, setPicture] = useState<any[]>([]);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalData, setModalData] = useState<any>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRowData, setSelectedRowData] = useState<any[]>([]);
  const [useDot, setUseDot] = useState(false);

  // ======================================================================
  // filter function - state()
  // ======================================================================
  const [showClearFilter, setShowClearFilter] = useState(false);
  const [fuzzySearchFilter, setFuzzySearchFilter] = useState("");
  const [modalFilter, setModalFilter] = useState<any>({});
  const [filterSetting, setFilterSetting] = useState("");
  const [statusValue, setStatusValue] = useState("All");
  const [statusKey, setStatusKey] = useState("ALL");
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [fieldName, setFieldName] = useState("");

  const [currentOutletId, setCurrentOutletId] = useState("");
  const [isTimeWindows, setIsTimeWindows] = useState<Boolean>(false);

  const headerItems = [
    {
      label: t("Header.dashboard"),
      route: "/profile/dashboard",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("Dashboard.invoice"),
      route: "/invoice/invoiceListing",
      className: "labelTextStyle",
    },
  ];
  useEffect(() => {
    if (Object.keys(modalData).length > 0) {
      setIsModalOpen(true);
    }
  }, [modalData]);

  useEffect(() => {
    // Function to check screen size and update state
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth <= 950); // Define your breakpoint for small screens
      setUseDot(window.innerWidth < 550);
    };

    // Add event listener for window resize
    window.addEventListener("resize", handleResize);

    // Call handleResize initially to set initial screen size
    handleResize();

    // Clean up the event listener on component unmount
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    let outletId = localStorage.getItem("currentOutletId");
    setCurrentOutletId(outletId ?? "");

    const handleScroll = () => {
      setShowScrollButton(window.pageYOffset > 0);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    if (Object.keys(useRetailerStore.getState()).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }
    }
  }, [Object.keys(useRetailerStore.getState()).length]);

  useEffect(() => {
    if (retailerAccess && Object.keys(retailerAccess).length > 0) {
      // getInvoice();

      // Get from localStorage
      const filterParams: any = getParamsFromLocalStorage(
        router.pathname,
        "invoiceFilter"
      );

      const filterKey: any = {
        fuzzySearch: "",
        productSku: null,
        productName: null,
        salesOrderNo: null,
        invoiceNo: null,
        invoiceBefore: "",
        invoiceAfter: "",
        overdueDateBefore: "",
        overdueDateAfter: "",
        status: null,
        sort: null,
        // remark: null,
      };

      const clonedFilterKey = { ...filterKey };
      delete clonedFilterKey.fuzzySearch;

      Object.keys(clonedFilterKey).forEach((key) => {
        const capitalizedKey = key
          .split(/(?=[A-Z])|\s+/)
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
        clonedFilterKey[capitalizedKey] = clonedFilterKey[key];
        delete clonedFilterKey[key];
      });

      const keysAsString: string = Object.keys(clonedFilterKey).join(", ");
      setFieldName(keysAsString);

      if (filterParams) {
        // Initialize variables to store the values
        // Follow search params' key

        setFilterSetting(filterParams);
        setFilterForm(filterParams, filterKey);
        filterForm.setFieldValue(["fuzzySearch"], filterKey.fuzzySearch);
        filterModalForm.setFieldsValue(filterKey);
        setFuzzySearchFilter(filterKey.fuzzySearch);
        let data = {
          productSku: filterKey.productSku || "",
          productName: filterKey.productName || "",
          invoiceDate:
            filterKey.invoicedAfter && filterKey.invoicedBefore
              ? [
                moment(filterKey.invoicedAfter),
                moment(filterKey.invoicedBefore),
              ]
              : null,
          overdueDate:
            filterKey.overdueDateAfter && filterKey.overdueDateBefore
              ? [
                moment(filterKey.overdueDateAfter),
                moment(filterKey.overdueDateBefore),
              ]
              : null,
          salesOrderNo: filterKey.salesOrderNo || "",
          invoiceNo: filterKey.invoiceNo || "",
          sort: filterKey.sort || undefined
        };
        setModalFilter(data);
        setStatusKey(filterKey.status ?? "ALL");
        const filterStatusLabel: any = invoiceStatusFilterOption.find(
          (item: any) => item.key === filterKey.status
        )?.label;
        setStatusValue(filterStatusLabel ?? "All");
      } else {
        getInvoice();
      }

      // getSelectOptions(
      //   "salesOrders",
      //   encodeParams({
      //     companyId: retailerAccess.companyId,
      //     companyBranchId: retailerAccess.companyBranchId,
      //     outletId: currentOutletId,
      //   }),
      //   "salesOrderNo",
      //   "id",
      //   true
      // ).then((value) => {
      //   setSalesOrderNoOption(value);
      // });
      // getProductData();

      // When able to select outlet, this is something that need to change
      apiHelper
        .GET(`outlets?id=${currentOutletId ?? ""}`)
        .then((res: any) => {
          if (res.items && res.items.length) {
            // setOutletData(res.items);

            const timeWindowDay =
              res.items[0]?.timeWindowEnabled === "TRUE" ? true : false;
            setIsTimeWindows(timeWindowDay);
          }
        });
    }
  }, [retailerAccess]);

  useEffect(() => {
    const data = {
      fuzzySearch: fuzzySearchFilter || "",
      overdueDate: modalFilter.overdueDate || "",
      invoiceNo: modalFilter.invoiceNo || "",
      eInvoiceSubmissionType: modalFilter.eInvoiceSubmissionType || "",
      salesOrderNo: modalFilter.salesOrderNo || "",
      invoiceDate: modalFilter.invoiceDate || "",
      productSku: modalFilter.productSku || "",
      productName: modalFilter.productName || "",
      sort: modalFilter.sort || "",
      sortOrder: modalFilter.sort ? "1" : undefined,
      status: statusKey === "ALL" ? "" : statusKey,
    };

    const allPropertiesEmpty = Object.values(data).every(
      (value) => value === ""
    );

    if (!allPropertiesEmpty) {
      searchInvoice(data);
    } else {
      setFilterSetting("");
    }
  }, [fuzzySearchFilter, statusKey, modalFilter]);

  useEffect(() => {
    // Check params whether is same
    const filterParams = getParamsFromLocalStorage(
      router.pathname,
      "invoiceFilter"
    );
    if (retailerAccess && Object.keys(retailerAccess).length > 0) {
      if (filterSetting) {
        getInvoice(true, false);

        if (filterSetting !== filterParams) {
          setParamsFromLocalStorage(
            router.pathname,
            filterSetting,
            "invoiceFilter"
          );
        }
        setShowClearFilter(true);
      } else {
        setShowClearFilter(false);
        if (data.length > 0) {
          localStorage.removeItem("invoiceFilter");
        }
        getInvoice(true, false);
      }
    }
  }, [filterSetting]);

  // *************************************************************************************
  // *** Scolling Function - useEffect ***
  // *************************************************************************************
  // Check scrolling position
  useEffect(() => {
    if (cursor === "") return;
    const handleScroll = () => {
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight - 50;
      const scrollPosition = window.scrollY;
      if (windowHeight + scrollPosition >= documentHeight) {
        // Stop API calling when cursor is equal to '0'
        if (cursor !== "0") {
          getInvoice();
        }
      }
    };
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [cursor]);

  const handleScrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  let isLoading = false;

  const getInvoice = (isRefresh = false, isClearFilter = false) => {
    // let currentOutletId = localStorage.getItem("currentOutletId");
    if (isLoading) return;
    setTableLoading(true);
    setShowButtonLoader(true);
    // const accessBranches = otherViewBranchAccess === false ? retailerAccess.companyBranchIds : otherCompanyBranchesIds;
    isLoading = true;
    setTimeout(() => {
      let params: any = {
        sort: "createdAt",
        sortOrder: "-1",
        companyId: retailerAccess.companyId,
        // companyBranchId: retailerAccess.companyBranchId,
        // outletId: retailerAccess.outletIds,
        outletId: currentOutletId,
        maxResultsPerPage: 30,
      };

      if (isRefresh === false) {
        params.cursor = cursor;
      }

      // if (!isAdmin) {
      //   params.companyBranchIds = accessBranches;
      // }
      // const checkAdminRights = isAdmin ? params : params + "&companyId=" + retailerAccess.companyId;
      const checkFilterRights =
        filterSetting && !isClearFilter
          ? filterSetting +
          (cursor && cursor !== "0" ? `&cursor=${cursor}` : "")
          : encodeParams(params);

      const dataSource = new DataSourceWithPageNumber(
        "invoices",
        checkFilterRights,
        false
      );

      // !isAdmin && !otherViewBranchAccess ? filterForm.setFieldValue("companyId", retailerAccess.companyId) : null;

      // if ((accessBranches && accessBranches.length > 0) || isAdmin) {
      dataSource
        .load()
        .then((res: any) => {
          if (res && res.items !== null) {
            let data = res.items;
            const objectMap = data.reduce(
              (accumulator: any, current: Invoice) => {
                accumulator["outletId"] = accumulator["outletId"] || [];
                if (
                  current.outletId &&
                  !outletMap.has(current.outletId) &&
                  !accumulator["outletId"].includes(current.outletId)
                ) {
                  accumulator["outletId"].push(current.outletId ?? "");
                }

                accumulator["salesOrderId"] = accumulator["salesOrderId"] || [];
                if (
                  current.salesOrderId &&
                  !salesOrderMap.has(current.salesOrderId) &&
                  !accumulator["salesOrderId"].includes(current.salesOrderId)
                ) {
                  accumulator["salesOrderId"].push(current.salesOrderId ?? "");
                }

                accumulator["shippingAddressId"] =
                  accumulator["shippingAddressId"] || [];
                if (
                  current.shippingAddressId &&
                  !shippingAddressMap.has(current.shippingAddressId) &&
                  !accumulator["shippingAddressId"].includes(
                    current.shippingAddressId
                  )
                ) {
                  accumulator["shippingAddressId"].push(
                    current.shippingAddressId ?? ""
                  );
                }

                current.invoiceProducts?.reduce(
                  (acc: any, product: ProductOrdered) => {
                    accumulator["productId"] = accumulator["productId"] || [];
                    if (
                      product.productId &&
                      !productMap.has(product.productId) &&
                      !accumulator["productId"].includes(product.productId)
                    ) {
                      accumulator["productId"].push(product.productId ?? "");
                    }

                    accumulator["productUOMId"] =
                      accumulator["productUOMId"] || [];
                    if (
                      product.productUOMId &&
                      !uomMap.has(product.productUOMId) &&
                      !accumulator["productUOMId"].includes(
                        product.productUOMId
                      )
                    ) {
                      accumulator["productUOMId"].push(
                        product.productUOMId ?? ""
                      );
                    }

                    return acc;
                  },
                  {}
                );

                current.invoiceToteboxes?.reduce(
                  (acc: any, totebox: InvoiceToteBoxes) => {
                    accumulator["toteboxId"] = accumulator["toteboxId"] || [];
                    if (
                      totebox.toteboxId &&
                      !toteboxMap.has(totebox.toteboxId) &&
                      !accumulator["toteboxId"].includes(totebox.toteboxId)
                    ) {
                      accumulator["toteboxId"].push(totebox.toteboxId ?? "");
                    }

                    totebox.toteboxProducts?.reduce(
                      (acc1: any, product1: ProductOrdered) => {
                        accumulator["productId"] =
                          accumulator["productId"] || [];
                        if (
                          product1.productId &&
                          !productMap.has(product1.productId) &&
                          !accumulator["productId"].includes(product1.productId)
                        ) {
                          accumulator["productId"].push(
                            product1.productId ?? ""
                          );
                        }

                        accumulator["productUOMId"] =
                          accumulator["productUOMId"] || [];
                        if (
                          product1.productUOMId &&
                          !uomMap.has(product1.productUOMId) &&
                          !accumulator["productUOMId"].includes(
                            product1.productUOMId
                          )
                        ) {
                          accumulator["productUOMId"].push(
                            product1.productUOMId ?? ""
                          );
                        }

                        return acc1;
                      },
                      {}
                    );

                    return acc;
                  },
                  {}
                );

                return accumulator;
              },
              {}
            );

            getOutlets(objectMap["outletId"]);
            getSalesOrder(objectMap["salesOrderId"]);
            getProduct(objectMap["productId"]);
            getTotebox(objectMap["toteboxId"]);
            getUOM(objectMap["productUOMId"]);
            getShippingAddress(objectMap["shippingAddressId"]);
            // getCompanyBranch(objectMap["companyBranchId"]);
            const nextCursor = res.cursor; // Get the cursor from the last item in the response
            if (nextCursor !== cursor || isRefresh) {
              // Avoid duplicates
              if (!isRefresh) {
                // setFullData((prevData) => [...prevData, ...data]);
                setData((prevData) => [...prevData, ...data]);
                setTableLoading(false);
                setShowButtonLoader(false); //path to check
              } else {
                // setFullData([...data]);
                setData([...data]);
                setTableLoading(false);
                setShowButtonLoader(false); //path to check
              }
              // cursor = nextCursor;
              setCursor(nextCursor);
              setTempCursor(nextCursor);
            }
          }
          isLoading = false;
        })
        .catch(() => {
          //* This Part need re-edit*//
          isLoading = false;
        });
      // } else {
      //   setGoodsReturnListingInfoData([]);
      // }
    }, 500);
  };

  const getOutlets = async (id: string[] = []) => {
    let tempProductMap = new Map(outletMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "outlets",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });
        if (res !== null && res.items.length > 0) {
          setOutletMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Outlet) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Outlet) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getTotebox = async (id: string[] = []) => {
    let tempProductMap = new Map(toteboxMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "toteboxes",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });
        if (res !== null && res.items.length > 0) {
          setToteboxMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Totebox) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Totebox) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getSalesOrder = async (id: string[] = []) => {
    let tempProductMap = new Map(salesOrderMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      //   status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "salesOrders",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setSalesOrderMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: SalesOrder) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: SalesOrder) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getProduct = async (id: string[] = []) => {
    let tempProductMap = new Map(productMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      //   status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "productCatalogues",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setProductMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Product) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Product) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getUOM = async (id: string[] = []) => {
    let tempProductMap = new Map(uomMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource("uoms", encodeParams(params), false);
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setUomMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: UOM) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: UOM) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getShippingAddress = async (id: string[] = []) => {
    let tempProductMap = new Map(uomMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "shippingAddresses",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setShippingAddressMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: OutletShippingAddress) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: UOM) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  // const getProductData = async () => {
  //   const dataSource = new DataSource(
  //     "productCatalogues",
  //     "status=ACTIVE",
  //     true
  //   );
  //   const res: any = await dataSource.load();

  //   if (res !== null) {
  //     let nameList: SelectOption[] = [];
  //     let skuList: SelectOption[] = [];
  //     res.map((value: any) => {
  //       nameList.push({
  //         value: value.id,
  //         label: value.name,
  //       });
  //       skuList.push({
  //         value: value.id,
  //         label: value.sku,
  //       });
  //     });
  //     setProductNameOption(nameList);
  //     setProductSkuOption(skuList);
  //   }
  // };

  const props: UploadProps = {
    name: "file",
    multiple: false,
    maxCount: 1,
    showUploadList: {
      showPreviewIcon: false,
    },
    beforeUpload: (file) => {
      if (
        file.type !== "image/png" &&
        file.type !== "image/jpg" &&
        file.type !== "image/jpeg" &&
        file.type !== "application/pdf"
      ) {
        MessageErrorUI(
          `${file.name} is an invalid file format. Please change the file extension to either .pdf, .png, .jpg, .jpeg.`
        );
        return Upload.LIST_IGNORE;
      } else if (file.size > 5242880) {
        MessageErrorUI(
          `${file.name} is too large. Please upload another document that is smaller than 5MB.`
        );
        return Upload.LIST_IGNORE;
      } else {
        return false;
      }
    },
  };

  const getBase64 = (file: RcFile): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });

  // Handle the image preview
  const handleImagePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile);
    }
  };

  // Handle the image change
  const handleImageChange = (info: UploadChangeParam<UploadFile<any>>) => {
    if (info.fileList.length > 0) {
      if (info.fileList.at(-1) !== undefined) {
        let pic: any[] = picture !== null ? picture : [];
        let preview = previewImage !== null ? previewImage : [];
        let file = info.fileList.at(-1);
        if (file !== undefined) {
          let uid = file.uid;
          let checkExist = pic.filter((item: any) => item.uid === uid);
          if (checkExist.length === 0) {
            file.status = "done";
            pic.push(file.originFileObj);
            preview.push(file);
            setPreviewImage([...preview]);
            setPicture([...pic]);
          }
        }
      }
    }
  };

  // To remove image
  // const handleImageRemove = (info: UploadFile<any>) => {
  //   let pic = picture.filter((item: any) => item.uid !== info.uid);
  //   let preview = previewImage.filter((item: any) => item.uid !== info.uid);
  //   setPreviewImage(preview);
  //   setPicture(pic);
  // };

  // Handle the image before upload
  const handleImageBeforeUpload = (file: RcFile) => {
    if (
      file.type !== "image/png" &&
      file.type !== "image/jpg" &&
      file.type !== "image/jpeg"
    ) {
      MessageErrorUI(
        `${file.name} is an invalid file format. Please change the file extension to either .png, .jpg, .jpeg.`
      );
      return Upload.LIST_IGNORE;
    } else if (file.size > 5242880) {
      MessageErrorUI(
        `${file.name} is too large. Please upload another document that is smaller than 5MB.`
      );
      return Upload.LIST_IGNORE;
    } else {
      return false;
    }
  };

  const getOrderIcon = (status: string, index: number) => {
    if (index === 1) {
      if (status !== "wait") {
        return (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: 60,
              width: 60,
            }}
          >
            <OrderActiveIcon
              width="60"
              height="60"
              className="flex justify-center items-center"
            />
          </div>
        );
      } else {
        return (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: 60,
              width: 60,
            }}
          >
            <OrderInactiveIcon
              width="60"
              height="60"
              className="flex justify-center items-center"
            />
          </div>
        );
      }
    } else if (index === 2) {
      if (status !== "wait") {
        return (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: 60,
              width: 60,
            }}
          >
            <DeliveryActiveIcon
              width="60"
              height="60"
              className="flex justify-center items-center"
            />
          </div>
        );
      } else {
        return (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: 60,
              width: 60,
            }}
          >
            <DeliveryInactiveIcon
              width="60"
              height="60"
              className="flex justify-center items-center"
            />
          </div>
        );
      }
    } else if (index === 3) {
      if (status !== "wait") {
        return (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: 60,
              width: 60,
            }}
          >
            <UnloadActiveIcon
              width="60"
              height="60"
              className="flex justify-center items-center"
            />
          </div>
        );
      } else {
        return (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: 60,
              width: 60,
            }}
          >
            <UnloadInactiveIcon
              width="60"
              height="60"
              className="flex justify-center items-center"
            />
          </div>
        );
      }
    }
  };

  const status1 = modalData.status !== "PENDING" ? "finish" : "wait";
  const hasPick =
    modalData.pickStatus !== undefined && modalData.pickStatus !== "";
  const hasUnload =
    modalData.unloadStatus !== undefined && modalData.unloadStatus !== "";

  const status2 =
    !hasPick && !hasUnload
      ? "wait"
      : hasPick && !hasUnload
        ? "process"
        : "finish";

  const status3 = hasUnload ? "finish" : "wait";

  const showModal = () => {
    return (
      <div className="w-full">
        <Row className="">
          <Card
            className=" rounded-lg w-full"
            style={{
              boxShadow: "rgba(0, 0, 0, 0.09) 0px 3px 12px",
              // background:
              //   "linear-gradient(0deg,rgba(204, 204, 204, 1) 0%, rgba(247, 245, 245, 1) 61%)",
            }}
            bordered={false}
          >
            <Steps
              progressDot={useDot}
              className="greenLine"
              labelPlacement="vertical"
              items={[
                {
                  title: "Order Confirmed",
                  description:
                    status1 === "finish" ? (
                      <div className="text-labelDark/80 text-[11px]">
                        {formateDateAndTime(modalData.createdAt)}
                      </div>
                    ) : (
                      ""
                    ),
                  status: status1,
                  icon: useDot ? "" : getOrderIcon(status1, 1),
                },
                {
                  title: "Order Shipped out",
                  description:
                    status2 !== "wait" ? (
                      <div className="text-labelDark/80 text-[11px]">
                        {formateDateAndTime(modalData.pickedAt)}
                      </div>
                    ) : (
                      ""
                    ),
                  status: status2,
                  icon: useDot ? "" : getOrderIcon(status2, 2),
                },
                {
                  title: "Delivered",
                  description:
                    status3 === "finish" ? (
                      <div className="text-labelDark/80 text-[11px]">
                        <div>{formateDateAndTime(modalData.unloadedAt)}</div>
                        <div>{modalData.unloadRemark}</div>
                      </div>
                    ) : null,
                  status: status3,
                  icon: useDot ? "" : getOrderIcon(status3, 3),
                },
              ]}
            />
          </Card>
        </Row>
        <Col>
          <Form
            className="w-full pt-3.5"
            form={invoiceDetailForm}
            layout="vertical"
            scrollToFirstError
          >
            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="invoiceNo"
                className="flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Invoice.invoice") + " " + t("Common.no")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={
                    t("Common.eg.") + " " + t("Placeholder.OutletName")
                  }
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="invoiceDate"
                className="flex-1"
                // rules={[
                //   {
                //     required: true,
                //     message: t("ssm") + " " + t("Validation.requiredField"),
                //   },
                // ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Invoice.invoiceDate")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.eg.") + " " + t("Placeholder.Email")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>

            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="overdueDate"
                className="flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Invoice.overdueDate")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.eg") + " " + t("Invoice.overdueDate")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="invoiceAmount"
                className="flex-1"
                // rules={[{ required: true, mess age: t("GPSLocation") + " " + t("Validation.Is.Required") }]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Invoice.invoiceAmount")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.eg") + " " + t("Invoice.unitNo")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>
            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="doNo"
                className="flex-1"
                // rules={[
                //   {
                //     required: true,
                //     message: t("Address1") + " " + t("Validation.Is.Required"),
                //   },
                // ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Invoice.do") + " " + t("Common.no")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.11")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="doDate"
                className="flex-1"
                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Invoice.doDate")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.12")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>
            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="shippingAddress"
                className="flex-1"
                // rules={[
                //   {
                //     required: true,
                //     message: t("City") + " " + t("Validation.Is.Required"),
                //   },
                // ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Invoice.shippingAddress")}
                  </p>
                }
              >
                <FormTextInput disabled placeholder={t("")} maxLength={100} />
                {/* disabled={cityList.length !== 0 ? false : true} /> */}
              </Form.Item>
            </Row>
            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                className="flex-1 p-2"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Invoice.supportedDocument")}
                  </p>
                }
              >
                <Upload
                  {...props}
                  disabled
                  onPreview={(val: any) => {
                    // only uploaded photo can download and show
                    // new upload wont be able to click
                    if (val.url != undefined) {
                      let link = document.createElement("a");
                      link.target = "_blank";
                      link.href = val.url;
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    }
                  }}
                  onChange={(info) => {
                    if (info.fileList.length > 0) {
                      if (
                        info.fileList.at(-1) !== undefined &&
                        info.file.status !== "removed"
                      ) {
                        let file = info.fileList.at(-1);
                        if (file !== undefined) {
                          file.status = "done";
                          let fileObj = file.originFileObj;
                          setAllFiles({
                            ...allFiles,
                            doDocument: fileObj,
                          });
                        }
                      }
                    }
                  }}
                  onRemove={() => {
                    setAllFiles({
                      ...allFiles,
                      ["doDocument"]: null,
                    });
                  }}
                  fileList={
                    allFiles["doDocument"] === undefined ||
                      allFiles["doDocument"] === null
                      ? []
                      : [allFiles["doDocument"]]
                  }
                >
                  <div className="flex items-center gap-x-4 p-2 text-buttonPurple bg-lightPurple font-semibold">
                    <PlusOutlined />
                    <p>{t("Invoice.supportedDocument")}</p>
                  </div>
                </Upload>
              </Form.Item>
              <Form.Item
                className="flex-1 p-2"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Invoice.proofOfDelivery")}
                  </p>
                }
              >
                <Upload
                  name="file"
                  multiple={true}
                  maxCount={5}
                  listType="picture-card"
                  onPreview={handleImagePreview}
                  showUploadList={{ showPreviewIcon: false }}
                  beforeUpload={handleImageBeforeUpload}
                  onChange={(info) => handleImageChange(info)}
                  // onRemove={(info) => handleImageRemove(info)}
                  fileList={previewImage}
                  className="custom-upload"
                >
                  {/* {previewImage.length === 0 ? (
                    <div>
                      <PlusOutlined />
                      <div style={{ marginTop: 8 }}>Upload</div>
                    </div>
                  ) : null} */}
                </Upload>
              </Form.Item>
            </Row>
            <span className="m-1"></span>
            <h1>{t("Invoice.ProductOrdered")}</h1>
            <ListingTableUI
              // EditableCell={EditableCell}
              bordered
              dataSource={invoiceProductData}
              columns={productColumn}
              // rowClassName="editable-row"
              rowKey={(record: any) => record.id}
              cursor={false}
              // loader={showButtonLoader}
              pagination={false}
              endMessage={""}
            />
            {invoiceToteboxProductData.map((totebox) => (
              <div key={totebox.toteboxId} className="totebox-container">
                <span className="m-1"></span>
                <p className="font-bold">
                  {t("Invoice.Totebox")}:{" "}
                  {toteboxMap.get(totebox.toteboxId)?.code}
                </p>
                <p>
                  {t("Invoice.SealId")}: {totebox.sealId}
                </p>
                <ListingTableUI
                  // EditableCell={EditableCell}
                  bordered
                  dataSource={totebox.toteboxProducts ?? []}
                  columns={productColumn}
                  // rowClassName="editable-row"
                  rowKey={(record: { id: any }) => record.id}
                  cursor={false}
                  // loader={showButtonLoader}
                  pagination={false}
                  endMessage={""}
                />
              </div>
            ))}
          </Form>
        </Col>
      </div>
    );
  };

  const productColumn = [
    {
      title: t("Invoice.product"),
      dataIndex: "productId",
      sorter: (a: any, b: any) => a.productId.localeCompare(b.productId),
      showSorterTooltip: false,
      key: "id",
      render: (_: any, record: ProductOrdered) => {
        const item = productMap.get(record.productId);
        if (item) {
          return (
            <div>
              <Col className="flex items-center w-full">
                <img
                  className="object-contain h-[80px] min-w-[80px] p-2"
                  src={
                    item.productUOM.find(
                      (item: ProductUOM) =>
                        record.productUOMId === item.productUOMId
                    )?.pictures
                      ? PUBLIC_BUCKET_URL +
                      item.productUOM.find(
                        (item: ProductUOM) =>
                          record.productUOMId === item.productUOMId
                      )?.pictures[1]
                      : defaultImage.src
                  }
                  loading="lazy"
                ></img>
                <div className="flex flex-col w-full">
                  <p className="font-bold text-[14px]">{item.name}&nbsp;</p>
                  <p className="text-gray-500 text-[10px] w-full flex ">
                    <span>
                      {t("Invoice.productCode")}: {item.sku}
                    </span>
                  </p>
                  <p className="text-gray-500 text-[10px] w-full flex ">
                    <span>
                      {t("Invoice.uom")}:{" "}
                      {uomMap.get(record.productUOMId)?.name}
                    </span>
                  </p>
                  <p className="text-gray-500 text-[10px] w-full flex ">
                    <span>
                      {t("Invoice.unitPrice")}: {record.price}
                    </span>
                  </p>
                </div>
              </Col>
            </div>
          );
        } else return null;
      },
    },
    {
      title: t("Invoice.quantity"),
      dataIndex: "quantity",
      sorter: (a: any, b: any) => a.quantity.localeCompare(b.quantity),
      showSorterTooltip: false,
      key: "quantity",
      render: (_: any, record: ProductOrdered) => {
        return <p className="tableRowNameDesign">{record.quantity}</p>;
      },
    },
    {
      title: t("Invoice.discount"),
      dataIndex: "discount",
      sorter: (a: any, b: any) => a.discount.localeCompare(b.discount),
      showSorterTooltip: false,
      key: "discount",
      render: (_: any, record: ProductOrdered) => {
        return (
          <p className="tableRowNameDesign">
            {NumberThousandSeparator(record.discount ?? 0)}
          </p>
        );
      },
    },
    {
      title: t("Invoice.totalPrice"),
      dataIndex: "totalPrice",
      sorter: (a: any, b: any) => a.totalPrice.localeCompare(b.totalPrice),
      showSorterTooltip: false,
      key: "totalPrice",
      render: (_: any, record: ProductOrdered) => {
        const total =
          (record.price ?? 0) * (record.quantity ?? 0) - (record.discount ?? 0);
        return (
          <p className="tableRowNameDesign">{NumberThousandSeparator(total)}</p>
        );
      },
    },
  ];

  const column = [
    // {
    //   title: t("Company"),
    //   dataIndex: "companyId",
    //   key: "companyId",
    //   width: 160,
    //   render: (record: any) => {
    //     return (
    //       <p className="tableRowNameDesign">{companyMap.get(record)?.name}</p>
    //     );
    //   },
    // },

    {
      title: t("Invoice.invoice") + " " + t("Common.no"),
      dataIndex: "invoiceNo",
      key: "invoiceNo",
      width: 100,
      render: (record: any) => {
        return <p className="tableRowNameDesign">{record}</p>;
      },
    },
    {
      title: t("Invoice.invoiceDate"),
      dataIndex: "invoiceDate",
      key: "invoiceDate",
      width: 100,
      render: (_: any, record: any) => {
        return (
          <p className="tableRowNameDesign">
            {formateDateAndTime(record.invoiceDate)}
          </p>
        );
      },
    },
    // {
    //   title: t("Invoice.outletCode"),
    //   dataIndex: "outletCode",
    //   sorter: (a: any, b: any) => {
    //     const first = outletMap.get(a.outletId)?.outletCode || "";
    //     const second = outletMap.get(b.outletId)?.outletCode || "";

    //     return first?.localeCompare(second);
    //   },
    //   showSorterTooltip: false,
    //   key: "outletCode",
    //   width: 100,
    //   render: (_: any, record: any) => {
    //     return (
    //       <p className="tableRowNameDesign">
    //         {outletMap.get(record.outletId)?.outletCode}
    //       </p>
    //     );
    //   },
    // },
    // {
    //   title: t("Invoice.outlet"),
    //   dataIndex: "outletId",
    //   key: "outletId",
    //   width: 180,
    //   sorter: (a: any, b: any) => {
    //     const first = outletMap.get(a.outletId)?.name || "";
    //     const second = outletMap.get(b.outletId)?.name || "";

    //     return first?.localeCompare(second);
    //   },
    //   render: (_: any, record: any) => {
    //     return (
    //       <p className="tableRowNameDesign">
    //         {outletMap.get(record.outletId)?.name}
    //       </p>
    //     );
    //   },
    // },
    {
      title: t("Invoice.overdueDate"),
      dataIndex: "overdueDate",
      key: "overdueDate",
      width: 120,
      render: (_: any, record: any) => {
        // let creditTerm: any = outletMap.get(record.outletId)?.creditTerm;
        // let invoiceDate = moment(record.invoiceDate);
        // // let overdueDate = invoiceDate.add(creditTerm, "days").format("DD/MM/YYYY h:mm A");
        // let overdueDate = formateDateAndTime(
        //   invoiceDate.add(creditTerm, "days").toISOString()
        // );
        return <p className="tableRowNameDesign">{record?.overdueDate && record?.overdueDate !== "0001-01-01T00:00:00Z" ? formateDateAndTime(record?.overdueDate) : ""}</p>;
      },
    },

    {
      title: t("Invoice.po") + " " + t("Common.no"),
      dataIndex: "salesOrderId",
      key: "salesOrderId",
      width: 120,
      render: (_: any, record: any) => {
        return (
          <p className="tableRowNameDesign">
            {salesOrderMap.get(record.salesOrderId)?.salesOrderNo}
          </p>
        );
      },
    },
    {
      title: t("Invoice.createdDate"),
      dataIndex: "createdAt",
      sorter: (a: any, b: any) =>
        moment(a.createdAt).unix() - moment(b.createdAt).unix(),
      showSorterTooltip: false,
      key: "createdAt",
      width: 120,
      render: (_: any, record: any) => {
        return (
          <p className="tableRowNameDesign">
            {formateDateAndTime(record.createdAt)}
          </p>
        );
      },
    },
    {
      title: t("Invoice.totalAmount"),
      dataIndex: "totalAmount",
      key: "totalAmount",
      width: 100,
      render: (_: any, record: any) => {
        let amount = 0;
        if (record.netAmount) {
          amount = record.netAmount + (record.totalTax ?? 0);
        } else {
          amount =
            record.subTotal - record.subTotalDiscount + (record.totalTax ?? 0);
        }

        return (
          <p className="tableRowNameDesign">
            {NumberThousandSeparator(amount)}
          </p>
        );
      },
    },
    {
      title: t("Invoice.paidAmount"),
      dataIndex: "paidAmount",
      key: "paidAmount",
      width: 100,
      render: (_: any, record: any) => {
        return (
          <p className="tableRowNameDesign">
            {NumberThousandSeparator(record.paidAmount)}
          </p>
        );
      },
    },
    {
      title: t("Invoice.outStandingAmount"),
      dataIndex: "outStandingAmount",
      key: "outStandingAmount",
      width: 100,
      render: (_: any, record: any) => {
        let amount = 0;
        if (record.netAmount) {
          amount = record.netAmount + (record.totalTax ?? 0);
        } else {
          amount =
            record.subTotal - record.subTotalDiscount + (record.totalTax ?? 0);
        }
        return (
          <p className="tableRowNameDesign">
            {NumberThousandSeparator(amount - record.paidAmount)}
          </p>
        );
      },
    },
    {
      title: t("Invoice.deliveryStatus"),
      dataIndex: "deliveryStatus",
      key: "deliveryStatus",
      onFilter: (value: string, record: any) =>
        record.deliveryStatus.indexOf(value) === 0,
      sorter: (a: any, b: any) =>
        a.deliveryStatus.localeCompare(b.deliveryStatus),
      showSorterTooltip: false,
      width: 100,
      render: (_: any, record: any) => {
        const [statusColor, statusBackgroundColor, statusBorderColor] =
          getStatusStyles(record.deliveryStatus);
        return (
          <div
            className={`tableRowNameDesign statusTag ${statusColor} ${statusBackgroundColor} ${statusBorderColor}`}
          >
            {capitalize(record.deliveryStatus)}
          </div>
        );
      },
    },
    {
      title: t("Invoice.deliveredDate"),
      dataIndex: "deliveredDate",
      key: "deliveredDate",
      width: 120,
      render: (_: any, record: any) => {
        // let creditTerm: any = outletMap.get(record.outletId)?.creditTerm;
        // let invoiceDate = moment(record.invoiceDate);
        // // let overdueDate = invoiceDate.add(creditTerm, "days").format("DD/MM/YYYY h:mm A");
        // let overdueDate = formateDateAndTime(
        //   invoiceDate.add(creditTerm, "days").toISOString()
        // );
        return (
          <p className="tableRowNameDesign">
            {formateDateAndTime(record.unloadedAt)}
          </p>
        );
      },
    },
    {
      title: t("Common.status"),
      dataIndex: "status",
      key: "status",
      onFilter: (value: string, record: any) =>
        record.status.indexOf(value) === 0,
      sorter: (a: any, b: any) => a.status.localeCompare(b.status),
      showSorterTooltip: false,
      width: 100,
      render: (_: any, record: any) => {
        return statusApproval(record);
      },
    },
    // {
    //   title: t("Invoice.eInvoiceLastPrintTime"),
    //   dataIndex: "einvoiceLastPrintTime",
    //   sorter: (a: any, b: any) => {
    //     const first = a?.einvoiceLastPrintTime ?? "0001-01-01T00:00:00Z";
    //     const second = b?.einvoiceLastPrintTime ?? "0001-01-01T00:00:00Z";

    //     return first?.localeCompare(second);
    //   },
    //   showSorterTooltip: false,
    //   key: "einvoiceLastPrintTime",
    //   width: 200,
    //   render: (_: any, record: any) => {
    //     return (
    //       <p className="tableRowNameDesign">
    //         {formateDateAndTime(record?.einvoiceLastPrintTime)}
    //       </p>
    //     );
    //   },
    // },
    // {
    //   title: t("Invoice.eInvoiceSubmissionType"),
    //   dataIndex: "eInvoiceSubmissionType",
    //   key: "eInvoiceSubmissionType",
    //   width: 200,
    //   render: (record: any) => {
    //     return <p className="tableRowNameDesign">{record}</p>;
    //   },
    // },
    // {
    //   title: t("Invoice.eInvoiceUuid"),
    //   dataIndex: "eInvoiceUuid",
    //   key: "eInvoiceUuid",
    //   width: 200,
    //   render: (record: any) => {
    //     return <p className="tableRowNameDesign">{record}</p>;
    //   },
    // },
    // {
    //   title: t("Invoice.eInvoiceSubmissionUid"),
    //   dataIndex: "eInvoiceSubmissionUid",
    //   key: "eInvoiceSubmissionUid",
    //   width: 200,
    //   render: (record: any) => {
    //     return <p className="tableRowNameDesign">{record}</p>;
    //   },
    // },
    // {
    //   title: t("Invoice.eInvoiceSubmittedAt"),
    //   dataIndex: "eInvoiceSubmittedAt",
    //   sorter: (a: any, b: any) =>
    //     moment(a.eInvoiceSubmittedAt).unix() -
    //     moment(b.eInvoiceSubmittedAt).unix(),
    //   showSorterTooltip: false,
    //   key: "eInvoiceSubmittedAt",
    //   width: 200,
    //   render: (_: any, record: any) => {
    //     return (
    //       <p className="tableRowNameDesign">
    //         {formateDateAndTime(record.eInvoiceSubmittedAt)}
    //       </p>
    //     );
    //   },
    // },
    // {
    //   title: t("Invoice.eInvoiceValidatedAt"),
    //   dataIndex: "eInvoiceValidatedAt",
    //   sorter: (a: any, b: any) =>
    //     moment(a.eInvoiceValidatedAt).unix() -
    //     moment(b.eInvoiceValidatedAt).unix(),
    //   showSorterTooltip: false,
    //   key: "eInvoiceValidatedAt",
    //   width: 200,
    //   render: (_: any, record: any) => {
    //     return (
    //       <p className="tableRowNameDesign">
    //         {formateDateAndTime(record.eInvoiceValidatedAt)}
    //       </p>
    //     );
    //   },
    // },
    // {
    //   title: t("Invoice.eInvoiceRespondedAt"),
    //   dataIndex: "eInvoiceRespondedAt",
    //   sorter: (a: any, b: any) =>
    //     moment(a.eInvoiceRespondedAt).unix() -
    //     moment(b.eInvoiceRespondedAt).unix(),
    //   showSorterTooltip: false,
    //   key: "eInvoiceRespondedAt",
    //   width: 200,
    //   render: (_: any, record: any) => {
    //     return (
    //       <p className="tableRowNameDesign">
    //         {formateDateAndTime(record.eInvoiceRespondedAt)}
    //       </p>
    //     );
    //   },
    // },
    // {
    //   title: t("Invoice.eInvoiceRequestCancelAt"),
    //   dataIndex: "eInvoiceRequestCancelAt",
    //   sorter: (a: any, b: any) =>
    //     moment(a.eInvoiceRequestCancelAt).unix() -
    //     moment(b.eInvoiceRequestCancelAt).unix(),
    //   showSorterTooltip: false,
    //   key: "eInvoiceRequestCancelAt",
    //   width: 200,
    //   render: (_: any, record: any) => {
    //     return (
    //       <p className="tableRowNameDesign">
    //         {formateDateAndTime(record.eInvoiceRequestCancelAt)}
    //       </p>
    //     );
    //   },
    // },
    // {
    //   title: t("Invoice.eInvoiceCancelledAt"),
    //   dataIndex: "eInvoiceCancelledAt",
    //   sorter: (a: any, b: any) =>
    //     moment(a.eInvoiceCancelledAt).unix() -
    //     moment(b.eInvoiceCancelledAt).unix(),
    //   showSorterTooltip: false,
    //   key: "eInvoiceCancelledAt",
    //   width: 200,
    //   render: (_: any, record: any) => {
    //     return (
    //       <p className="tableRowNameDesign">
    //         {formateDateAndTime(record.eInvoiceCancelledAt)}
    //       </p>
    //     );
    //   },
    // },
    {
      title: t("Invoice.eInvoiceStatus"),
      dataIndex: "eInvoiceStatus",
      key: "eInvoiceStatus",
      onFilter: (value: string, record: any) =>
        record.eInvoiceStatus.indexOf(value) === 0,
      sorter: (a: any, b: any) =>
        a.eInvoiceStatus.localeCompare(b.eInvoiceStatus),
      showSorterTooltip: false,
      width: 200,
      render: (_: any, record: any) => {
        return statusApproval(record, true);
      },
    },

    // {
    //   title: t("Invoice.eInvoiceSignature"),
    //   dataIndex: "eInvoiceSignature",
    //   key: "eInvoiceSignature",
    //   width: 200,
    //   render: (record: any) => {
    //     return <p className="tableRowNameDesign">{record}</p>;
    //   },
    // },
    // {
    //   title: t("Common.approvalStatus"),
    //   dataIndex: "approvalStatus",
    //   key: "approvalStatus",
    //   onFilter: (value: string, record: any) =>
    //     record.approvalStatus.indexOf(value) === 0,
    //   sorter: (a: any, b: any) =>
    //     a.approvalStatus?.localeCompare(b.approvalStatus),
    //   showSorterTooltip: false,
    //   width: 100,
    //   render: (_: any, record: any) => {
    //     return statusApproval(record);
    //   },
    // },
    {
      // Action
      title: t("Common.action"),
      dataIndex: "action",
      key: "action",
      fixed: "right",
      width: 100,
      render: (_: any, record: Invoice) => {
        const product = record.invoiceProducts;
        let totals = 0;

        // if (product) {
        //   product.map((item: ProductOrdered) => {
        //     if (item) {
        //       const subtotal = (item.quantity ?? 0) * (item?.price ?? 0);
        //       const taxAmount = subtotal * (item.taxRate ?? 0);
        //       const total = subtotal - (item.discount ?? 0) - taxAmount;
        //       totals = totals + total;
        //     }
        //   });
        // }

        let creditTerm: any = outletMap.get(record.outletId)?.creditTerm;
        let invoiceDate = moment(record.invoiceDate);
        let overdueDate = formateDateAndTime(
          invoiceDate.add(creditTerm, "days").toISOString()
        );

        // const containsRorC = /[RU]/.test(userAccess?.policies?.["goodsReturn"] || "");
        // if ((containsRorC || isAdmin || isCompanyAdmin) && record.status !== "UNVERIFIED") {
        return (
          <div className="flex">
            <Button
              type="link"
              onClick={async () => {
                setModalData(record);
                setInvoiceProductData(record.invoiceProducts ?? []);
                if (record.invoiceToteboxes) {
                  setInvoiceToteboxProductData(record.invoiceToteboxes ?? []);
                }

                if (record.doSignedDocument) {
                  let docs: { [key: string]: any } = {};
                  const data = record.doSignedDocument;
                  await SignedUrl(record.doSignedDocument).then((res: any) => {
                    //split and get the file name only from API value.
                    let fileName = data.split("/");
                    let preview: UploadFile = {
                      uid: data,
                      name: fileName.at(-1)!,
                      url: res,
                      fileName: fileName.at(-1),
                    };
                    invoiceDetailForm.setFieldValue("doDocument", preview);
                    docs.doDocument = preview;
                  });
                  setAllFiles(docs);
                } else {
                  setAllFiles({});
                }

                if (
                  record.unloadedProductImages &&
                  record.unloadedProductImages.length > 0
                ) {
                  const promises = record.unloadedProductImages.map((image) =>
                    PicSignedUrl(image).then((res: any) => ({
                      uid: image ?? "",
                      name: image ?? "",
                      thumbUrl: res,
                    }))
                  );

                  Promise.all(promises).then((previewArray: UploadFile[]) => {
                    setPreviewImage(previewArray);
                  });
                } else setPreviewImage([]);

                invoiceDetailForm.setFieldsValue({
                  invoiceNo: record.invoiceNo,
                  invoiceDate: formateDateAndTime(record.invoiceDate),
                  overdueDate: overdueDate,
                  invoiceAmount: NumberThousandSeparator(record.netAmount ?? 0),
                  doNo: record.doNo,
                  doDate: formateDateAndTime(record.doDate),
                  shippingAddress: shippingAddressMap.get(
                    record.shippingAddressId
                  )?.shippingAddressDescription,
                });
              }}
              className="flex items-center  text-xs ml-0 p-2"
            >
              <Tooltip title={t("Common.viewMore")}>
                <EyeOutlined style={{ color: "green" }} />
              </Tooltip>
            </Button>
          </div>
        );
        // }
        // return null;
      },
    },
  ];

  const rowSelection = {
    fixed: true, // This will fix the checkbox column to the left
    onChange: (selectedRowKeys: string[], selectedRows: []) => {
      setSelectedRowData(selectedRows);
      setSelectedRowKeys(selectedRowKeys);
    },

    getCheckboxProps: (record: any) => {
      if (record?.netAmount - record?.paidAmount - (record?.processingAmount ?? 0) === 0) {
        return {
          disabled: true,
        };
      }
    },
  };

  // const generatePDF = () => {
  //   setPDFLoading(true);
  //   selectedRowData.forEach((item: Invoice) => {
  //     const dataSource = new DataSource(
  //       "invoice/pdf",
  //       encodeParams({ id: item.id }),
  //       false
  //     );
  //     dataSource
  //       .load()
  //       .then((res: any) => {
  //         PicSignedUrl(res)
  //           .then((res: any) => {
  //             const link = document.createElement("a");
  //             link.href = res;
  //             link.target = "_blank"; // Open the link in a new tab (optional)
  //             document.body.appendChild(link);
  //             link.click();
  //             // Cleanup: Remove the link from the DOM
  //             document.body.removeChild(link);
  //           })
  //           .catch(() => {
  //             setPDFLoading(false);
  //           });
  //       })
  //       .catch(() => {
  //         setPDFLoading(false);
  //       });
  //     // }
  //   });

  //   setPDFLoading(false);
  // };

  //function for generate pdf.
  const generatePDF = async () => {
    setPDFLoading(true);

    try {
      const pdfDoc = await PDFDocument.create();
      let numberPagePrinted = 0;

      for (const invoiceData of selectedRowData) {
        let donorPdfBytes;

        // if (invoiceData.invoiceDocument !== "") {
        // const res: any = await PicSignedUrl(invoiceData.invoiceDocument!);
        //   const pdfBytesResponse = await fetch(res);
        //   donorPdfBytes = await pdfBytesResponse.arrayBuffer();
        // } else {
        let res: any;

        if (
          invoiceData.eInvoiceSubmissionType === "EINVOICE" &&
          invoiceData.eInvoiceStatus === "VALID"
        ) {
          res = await apiHelper.GET("invoice/ePdf?id=" + invoiceData.id);
          numberPagePrinted += 1;
        } else if (invoiceData.eInvoiceSubmissionType !== "EINVOICE") {
          res = await apiHelper.GET("invoice/pdf?id=" + invoiceData.id);
          numberPagePrinted += 1;
        }

        if (res) {
          const documentSigned: any = await PicSignedUrl(res.item); //need to remove after confirmed
          const pdfBytesResponse = await fetch(documentSigned);
          donorPdfBytes = await pdfBytesResponse.arrayBuffer();
          // }

          const donorPdfDoc = await PDFDocument.load(donorPdfBytes);

          // Copy all pages from donorPdfDoc to pdfDoc
          const donorPages = await pdfDoc.copyPages(
            donorPdfDoc,
            donorPdfDoc.getPageIndices()
          );
          donorPages.forEach((page: any) => pdfDoc.addPage(page));
        }
      }
      if (numberPagePrinted === 0) {
        MessageInfoUI("No Valid Document(s) found");
        return;
      }
      if (numberPagePrinted > 0) {
        MessageInfoUI(numberPagePrinted + " Valid Document(s) Printed");
      }

      // Convert the merged PDF to a blob
      const mergedPdfBytes = await pdfDoc.save();

      // Create a blob URL and open it in a new tab
      const blob = new Blob([mergedPdfBytes], { type: "application/pdf" });
      const blobUrl = URL.createObjectURL(blob);
      window.open(blobUrl, "_blank");
    } catch (error) {
      console.error(error);
      setPDFLoading(false);
    }

    setPDFLoading(false);
  };

  // const generateDoPDF = () => {
  //   setPDFLoading(true);
  //   selectedRowData.forEach((item: Invoice) => {
  //     const dataSource = new DataSource(
  //       "invoice/doPdf",
  //       encodeParams({ id: item.id }),
  //       false
  //     );
  //     dataSource
  //       .load()
  //       .then((res: any) => {
  //         PicSignedUrl(res)
  //           .then((res: any) => {
  //             const link = document.createElement("a");
  //             link.href = res;
  //             link.target = "_blank"; // Open the link in a new tab (optional)
  //             document.body.appendChild(link);
  //             link.click();
  //             // Cleanup: Remove the link from the DOM
  //             document.body.removeChild(link);
  //           })
  //           .catch(() => {
  //             setPDFLoading(false);
  //           });
  //       })
  //       .catch(() => {
  //         setPDFLoading(false);
  //       });
  //     // }
  //   });

  //   setPDFLoading(false);
  // };

  // const generateEInvoicePDF = () => {
  //   setPDFLoading(true);
  //   selectedRowData.forEach((item: Invoice) => {
  //     const dataSource = new DataSource(
  //       "invoice/pdf",
  //       encodeParams({ id: item.id }),
  //       false
  //     );
  //     dataSource
  //       .load()
  //       .then((res: any) => {
  //         PicSignedUrl(res)
  //           .then((res: any) => {
  //             const link = document.createElement("a");
  //             link.href = res;
  //             link.target = "_blank"; // Open the link in a new tab (optional)
  //             document.body.appendChild(link);
  //             link.click();
  //             // Cleanup: Remove the link from the DOM
  //             document.body.removeChild(link);
  //           })
  //           .catch(() => {
  //             setPDFLoading(false);
  //           });
  //       })
  //       .catch(() => {
  //         setPDFLoading(false);
  //       });
  //     // }
  //   });

  //   setPDFLoading(false);
  // };

  const handleFirstButtonClick = () => {
    const concatenatedString: string = selectedRowKeys.join("/");
    localStorage.setItem("invoiceSelected", concatenatedString);

    router.push(`/payment/toPay?paymentMade=invoiceSelected`);
  };

  const buttons = [
    !isTimeWindows && {
      label: t("Invoice.makePayment"),
      onClick: handleFirstButtonClick,
      disabled: !(selectedRowData.length > 0),
    },
    // {
    //   label: t("Invoice.makeGoodsReturn"),
    //   onClick: handleSecondButtonClick,
    //   disabled: !(selectedRowData.length > 0),
    // },
    // {
    //   label: t("Invoice.printeInvoice"),
    //   onClick: generatePDF,
    //   disabled: !(selectedRowData.length > 0),
    // },
    {
      label: t("Invoice.printInvoice"),
      onClick: generatePDF,
      disabled: !(selectedRowData.length > 0),
    },
    // {
    //   label: t("Invoice.printDo"),
    //   onClick: generateDoPDF,
    //   disabled: !(selectedRowData.length > 0),
    // },
  ].filter(Boolean) as {
    label: string;
    onClick: () => void;
    disabled?: boolean;
    buttonColor?: string;
    loading?: boolean;
  }[];

  // *************************************************************************************
  // *** Filter Modal ***
  // *************************************************************************************

  const filterFormOnfinish = (values: any) => {
    setModalFilter(values);
  };

  const searchInvoice = (values: any) => {
    //convert to empty string when no value is entered as default value is undefined.
    for (const key in values) {
      if (values[key] === undefined) {
        values[key] = "";
      }
    }

    // return true or false
    let isAnyKeyFilled = Object.keys(values).some(
      (key) => values[key] !== "" && values[key] !== undefined
    );

    // const invoiceDate = values.invoiceDate
    //   ? values.invoiceDate.format("YYYY-MM-DDT00:00:00") + "Z"
    //   : "";
    // const overdueDate = values.overdueDate
    //   ? values.overdueDate.format("YYYY-MM-DDT00:00:00") + "Z"
    //   : "";

    let invoiceDateAfter = "";
    let invoicedateBefore = "";
    if (values?.invoiceDate?.length) {
      invoiceDateAfter =
        values.invoiceDate?.[0]?.format("YYYY-MM-DDT00:00:00") + "Z";
      invoicedateBefore =
        values.invoiceDate?.[1]?.format("YYYY-MM-DDT23:59:59") + "Z";
    }

    let overdueDateAfter = "";
    let overdueDateBefore = "";
    if (values?.overdueDate?.length) {
      overdueDateAfter =
        values.overdueDate?.[0]?.format("YYYY-MM-DDT00:00:00") + "Z";
      overdueDateBefore =
        values.overdueDate?.[1]?.format("YYYY-MM-DDT23:59:59") + "Z";
    }
    // let currentOutletId = localStorage.getItem("currentOutletId");

    const params =
      encodeParams({
        companyId: retailerAccess.companyId,
        // companyBranchId: retailerAccess.companyBranchId,
        outletId: currentOutletId,
        fuzzySearch: values.fuzzySearch,
        invoiceNo: values.invoiceNo || "",
        invoicedBefore: invoicedateBefore,
        invoicedAfter: invoiceDateAfter,
        // invoiceDate: invoiceDate,
        eInvoiceSubmissionType: values.eInvoiceSubmissionType,
        overdueBeforeDate: overdueDateBefore,
        overdueAfterDate: overdueDateAfter,
        productId:
          values.productSku && values.productName
            ? [values.productSku, values.productName]
            : values.productSku || values.productName || "",
        salesOrderId: values.salesOrderNo || "",
        status: values.status,
        sort: values.sort,
        sortOrder: values.sort ? "1" : undefined,
      }) + `${values.sort ? "" : "&sort=createdAt&sortOrder=-1"}`;

    if (isAnyKeyFilled) {
      setCursor("0");
      setFilterSetting(params);
    }
  };

  const handleStatusMenuClick: MenuProps["onClick"] = ({ key }) => {
    const items = invoiceStatusFilterOption;
    setStatusKey(key);
    // Access the label property of the selected item
    const selectedLabel = items.find(
      (menuItem: any) => menuItem.key === key
    )?.label;
    if (selectedLabel) {
      setStatusValue(selectedLabel);
    }
  };

  const filterModal = () => {
    return (
      <div className="w-full">
        <Form
          onFinish={filterFormOnfinish}
          form={filterModalForm}
          className=""
          layout="vertical"
        >
          <h1 className="font-bold text-base pb-4 hidden sm:flex">
            {t("Filter")}
          </h1>
          {/* First Row of Filter Input */}
          <Row className="filterBlockForm flex-col space-y-3">
            <Row className="flex sm:flex-row flex-col gap-x-4 sm:gap-y-0 gap-y-4">
              <Form.Item
                name="invoiceNo"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("Invoice.invoice") +
                      " " +
                      t("Common.no") +
                      "?"}
                  </p>
                }
              >
                <FormTextInput
                  placeholder={
                    t("Common.eg") +
                    " " +
                    t("Invoice.invoice") +
                    " " +
                    t("Common.no")
                  }
                  maxLength={30}
                />
              </Form.Item>
              <Form.Item
                name="eInvoiceSubmissionType"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("Invoice.eInvoiceSubmissionType") +
                      "?"}
                  </p>
                }
              >
                <SelectInput
                  placeholder={
                    t("Common.eg") + " " + t("Invoice.eInvoiceSubmissionType")
                  }
                  onChange={() => {
                    filterModalForm.submit();
                  }}
                  options={requiredEinvoiceOption}
                />
              </Form.Item>
            </Row>
            <Row className="flex sm:flex-row flex-col gap-x-4 sm:gap-y-0 gap-y-4">
              <Form.Item
                name="invoiceDate"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("Invoice.invoiceDate") +
                      "?"}
                  </p>
                }
              >
                <RangePickerInput
                  placeholder1={t("Common.startAfter")}
                  placeholder2={t("Common.endBefore")}
                />
              </Form.Item>
              <Form.Item
                name="sort"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {("Invoice.sortAscOrderBy")}
                  </p>
                }
              >
                <SelectInput
                  placeholder={
                    t("Common.eg") + " " + t("Invoice.sortAscOrderBy")
                  }
                  options={[{ value: "overdueDate", label: "Overdue Date" }, { value: "invoiceDate", label: "Invoice Date" }, { value: "createdAt", label: "Created At" }, { value: "invoiceNo", label: "Invoice No" }]}
                />
              </Form.Item>
            </Row>
            <Row className="flex sm:flex-row flex-col gap-x-4 sm:gap-y-0 gap-y-4">
              <Form.Item
                name="overdueDate"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("Invoice.overdueDate") +
                      "?"}
                  </p>
                }
              >
                {/* <SingleDateInput
                  placeholder={t("Common.eg") + " " + t("Invoice.overdueDate")}
                  onChange={() => {
                    filterModalForm.submit();
                  }}
                /> */}
                <RangePickerInput
                  placeholder1={t("Common.startAfter")}
                  placeholder2={t("Common.endBefore")}
                />
              </Form.Item>
              <Form.Item
                name="salesOrderNo"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("Invoice.salesOrder") +
                      " " +
                      t("Common.no") +
                      "?"}
                  </p>
                }
              >
                <ComponentFilterSelect
                  placeholder={
                    t("Common.eg") +
                    " " +
                    t("Invoice.salesOrder") +
                    " " +
                    t("Common.no")
                  }
                  dbName={"salesOrders"}
                  customParams={{
                    companyId: retailerAccess.companyId ?? "",
                    // companyBranchId: retailerAccess.companyBranchId ?? "",
                    outletId: currentOutletId,
                  }}
                  displayExpr={"sku"}
                  valueExpr={"id"}
                  userAccess={retailerAccess}
                />
              </Form.Item>
            </Row>
            <Row className="flex sm:flex-row flex-col gap-x-4 sm:gap-y-0 gap-y-4">
              <Form.Item
                name="productSku"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("CreditNote.productSKU") +
                      "?"}
                  </p>
                }
              >
                <ComponentFilterSelect
                  placeholder={
                    t("Common.eg") + " " + t("GoodsReturn.productSKU")
                  }
                  dbName={"productCatalogues"}
                  customParams={{ status: "ACTIVE" }}
                  displayExpr={"sku"}
                  valueExpr={"id"}
                  userAccess={retailerAccess}
                />
              </Form.Item>
              <Form.Item
                name="productName"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("CreditNote.productName") +
                      "?"}
                  </p>
                }
              >
                <ComponentFilterSelect
                  placeholder={
                    t("Common.eg") + " " + t("GoodsReturn.productName")
                  }
                  dbName={"productCatalogues"}
                  customParams={{ status: "ACTIVE" }}
                  displayExpr={"name"}
                  valueExpr={"id"}
                  userAccess={retailerAccess}
                />
              </Form.Item>
            </Row>
          </Row>
          <Row className="flex pt-8 justify-between">
            <PrimaryButtonUI
              label={t("Common.resetAll")}
              onClick={() => {
                filterModalForm.resetFields();
                // setParamsFromLocalStorage(router.pathname, `status=${statusKey}`, "productFilter");
              }}
            />
            {/* <Row> */}
            <SecondaryButtonUI
              label={t("Common.cancel")}
              htmlType="reset"
              onClick={() => {
                setModalFilter({});
                setIsFilterModalOpen(false);
              }}
            />
            <PrimaryButtonUI
              label={t("Common.applyFilter")}
              htmlType="submit"
              onClick={() => {
                setIsFilterModalOpen(false);
              }}
            />
            {/* </Row> */}
          </Row>
        </Form>
      </div>
    );
  };

  const showContent = () => {
    return (
      <div>
        <BackButtonUI
          title={t("Invoice.invoice")}
          buttons={buttons}
        ></BackButtonUI>
        <Row className="mb-4 w-full">
          <FilterFormComponent
            filterForm={filterForm}
            onDebouncedChange={(value) => {
              if (value === "") {
                filterForm.resetFields();
                setStatusKey("ALL");
                setStatusValue("All");
                setFuzzySearchFilter("");
                setModalFilter({});
                filterModalForm.resetFields();
                // setData([...fullData]);
                setShowClearFilter(false);
                setCursor(tempCursor);
                setFilterSetting("");
                localStorage.removeItem("invoiceFilter");
              } else {
                filterModalForm.resetFields();
                setModalFilter({});
                setFuzzySearchFilter(value);
              }
            }}
            fieldName={fieldName}
            clearButtonOnChange={() => {
              filterForm.resetFields();
              setStatusKey("ALL");
              setStatusValue("All");
              setFuzzySearchFilter("");
              setModalFilter({});
              filterModalForm.resetFields();
              // setData([...fullData]);
              setShowClearFilter(false);
              setCursor(tempCursor);
              setFilterSetting("");
              localStorage.removeItem("invoiceFilter");
            }}
            filterModalButtonOnClick={() => {
              setIsFilterModalOpen(true);
              filterForm.resetFields();
              setFuzzySearchFilter("");
            }}
            modalFilterValue={modalFilter}
            option={invoiceStatusFilterOption}
            handleStatusMenuClick={handleStatusMenuClick}
            clearFilterDisable={showClearFilter === true ? false : true}
            statusValue={statusValue}
            debounceValue={fuzzySearchFilter}
          ></FilterFormComponent>
        </Row>
        <ListingTableUI
          // EditableCell={EditableCell}
          bordered
          dataSource={data}
          columns={column}
          // rowClassName="editable-row"
          rowKey={(record: any) => record.id}
          cursor={cursor}
          loader={showButtonLoader}
          pagination={false}
          rowSelection={rowSelection}
          loading={tableLoading}
        />
      </div>
    );
  };

  return (
    <div className="flex flex-col w-full min-h-screen bg-bgOrange">
      <Header items={headerItems} hasSearch={false} values={() => { }} />
      <Content className="flex flex-col mt-3 w-full sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2">
        {showContent()}
      </Content>
      <Row className="justify-center w-full pt-4">
        {showScrollButton && (
          <div className="fixed bottom-20 right-8 z-50">
            <button
              className={`flex items-center justify-center rounded-full w-10 h-10 text-white text-lg font-semibold focus:outline-none bg-blue-500 hover:bg-blue-600`}
              onClick={handleScrollToTop}
            >
              <ArrowUpOutlined style={{ fontSize: "24px" }} />
            </button>
          </div>
        )}
      </Row>
      <ModalUI
        title={
          <div className="text-[24px] font-semibold">
            {t("Invoice.invoice") + " " + t("Invoice.details")}
          </div>
        }
        width="80%"
        visible={isModalOpen}
        // onOk={handleOk}
        onCancel={() => {
          setIsModalOpen(false);
          setModalData([]);
        }}
        content={showModal()}
      ></ModalUI>
      {isSmallScreen ? (
        <Drawer
          title="Filter"
          placement="bottom"
          closable={false}
          onClose={() => setIsFilterModalOpen(false)}
          open={isFilterModalOpen}
          height="80vh"
          className="rounded-t-lg"
        >
          {filterModal()}
        </Drawer>
      ) : (
        <ModalUI
          // title={"More Filter"}
          width="70%"
          className={"modalFilterBody"}
          visible={isFilterModalOpen}
          onOk={() => setIsFilterModalOpen(false)}
          onCancel={() => setIsFilterModalOpen(false)}
          content={filterModal()}
          title={""}
        ></ModalUI>
      )}
      <AppFooter retailerAccessValues={retailerAccess} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default InvoiceListing;
