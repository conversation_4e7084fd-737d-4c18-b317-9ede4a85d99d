import { useRouter } from "next/router";
import { Form, Input, Row, Tooltip, Dropdown, Space, Col, Select } from "antd";
import Logo from "../assets/logo/Neuroforce.svg";
import { IconButtonUI } from "./buttonUI";
import { i18n, useTranslation } from "next-i18next";
import Cart<PERSON>ogo from "../assets/logo/cart.svg";
import Setting from "../assets/logo/setting_active.svg";
import Dashboard from "../assets/logo/dashboard_active.svg";
import Ticketing from "../assets/logo/ticketing.svg";
import Outlet from "../assets/logo/outlet.svg";
import { DebounceFilterTextInput, SelectInput } from "./input";
import {
  DownOutlined,
  QuestionCircleOutlined,
  SearchOutlined,
  HomeFilled,
  MenuOutlined,
  RightOutlined,
  LogoutOutlined,
  GlobalOutlined,
} from "@ant-design/icons";
import { use, useEffect, useState } from "react";
import { Cart, Retailer } from "../components/type";
import type { MenuProps } from "antd";
import { DataSource, capitalize, encodeParams } from "@/stores/utilize";
import useRetailerStore from "@/stores/store";
import { getOutletData, getRetailerData } from "@/stores/authContext";
import { ModalUI } from "./modalUI";
import { languageDropDownOption } from "@/components/config";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";

interface HeaderComponentProps {
  items: any;
  hasSearch: boolean;
  debounceValue?: string;
  onDebouncedChange?: (item: any) => void;
  values: (item: any) => void;
  fieldValue?: string | string[];
  inputDisabled?: boolean;
}

interface option {
  label: string;
  key: string;
}

export const supportedLocales = ["en", "bm", "zh", "ta"];

const Header: React.FC<HeaderComponentProps> = ({
  items,
  hasSearch,
  debounceValue,
  values,
  fieldValue,
  inputDisabled,
}) => {
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const [filterForm] = Form.useForm();
  const [outletKey, setOutletKey]: any = useState("");
  const [outletSelection, setOutletSelection] = useState<
    MenuProps["items"] | any
  >([]);
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({});
  const [productMap, setProductMap] = useState(new Map());

  const [isSmallScreen, setIsSmallScreen] = useState(false);

  const currentLanguage =
    typeof window !== "undefined"
      ? localStorage.getItem("language") || "en"
      : "en";

  const [selectedLanguage, setSelectedLanguage] = useState(currentLanguage);
  const { t } = useTranslation("common");

  const handleLanguageChange = (value: string) => {
    try {
      setSelectedLanguage(value);
      localStorage.setItem("language", value);

      i18n?.changeLanguage(value);

      // Dispatch a custom event to signal language change
      // const languageChangeEvent = new Event("languageChange");
      // window.dispatchEvent(languageChangeEvent);

      // Trigger a page reload
      // window.location.reload()
    } catch (error) {
      console.error("Error saving language to local storage:", error);
    }
  };

  const navigateTo = (path: string) => {
    router.push(path);
  };
  const searchBar = hasSearch;
  const cartCount = useRetailerStore((state) => state.cart);
  const [isHamburgerModal, setHamburgerModal] = useState(false);

  const passedRouter = useRouter();
  const queryInfo = passedRouter.query;

  useEffect(() => {
    if (router.isReady && Object.keys(useRetailerStore.getState()).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => {
          setRetailerAccess(value);
        });
      }
    }
  }, [router.isReady, Object.keys(useRetailerStore.getState()).length]);

  useEffect(() => {
    if (fieldValue) {
      filterForm.setFieldValue("fuzzySearch", fieldValue);
    }
  }, [fieldValue]);

  useEffect(() => {
    retrieveOutletData();
  }, []);

  useEffect(() => {
    if (retailerAccess.id) {
      retrieveCartCountData();
    }
  }, [retailerAccess]);

  useEffect(() => {
    // Close the modal when it becomes hidden on tablet and below
    if (!isHamburgerModal) {
      setHamburgerModal(false);
    }
  }, [isHamburgerModal]);

  useEffect(() => {
    // Check if running on the client side before using localStorage
    if (typeof window !== "undefined") {
      const storedLanguage = localStorage.getItem("language");
      if (storedLanguage) {
        setSelectedLanguage(storedLanguage);
        i18n?.changeLanguage(storedLanguage);
      }
    }
  }, []);

  useEffect(() => {
    // Function to check screen size and update state
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth <= 950); // Define your breakpoint for small screens

      setHamburgerModal(false);
    };

    // Add event listener for window resize
    window.addEventListener("resize", handleResize);

    // Call handleResize initially to set initial screen size
    handleResize();

    // Clean up the event listener on component unmount
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  //First step to get data.
  const retrieveOutletData = async () => {
    try {
      // Check if Zustand store is available
      const retailerStore = useRetailerStore.getState();
      if (!retailerStore.outletSelection?.length) {
        // If outlet data is not available in Zustand, fetch it using getOutletByToken
        let retailerOutletIds = await getRetailerOutletIds();
        if (retailerOutletIds.length) {
          await getOutletByIds(retailerOutletIds);
        }
      } else {
        setOutletKey(retailerStore.currentOutletData?.id);
        setOutletSelection(retailerStore.outletSelection);
      }
    } catch (error) {
      console.error("Error retrieving outlet data:", error);
    }
  };

  // Get retailer outlet ids.
  const getRetailerOutletIds = async () => {
    try {
      const dataSource = new DataSource("retailer", "", false, "v2");
      const res: any = await dataSource.load();

      if (res !== null) {
        return res?.outletIds;
      }
    } catch (error) {
      console.error("Error fetching retailer:", error);
    }
  };

  // Get outlet details by ids and set the current name of outlet and remap the select option.
  const getOutletByIds = async (ids: string | string[] = []) => {
    let currentOutletId = localStorage.getItem("currentOutletId");

    try {
      const dataSource = new DataSource(
        "outlet/name",
        encodeParams({ id: ids }),
        false
      );

      const res: any = await dataSource.load();
      if (res.items !== null && res.items.length > 0) {
        let outletSelectOption = res.items.map((item: any) => {
          return {
            key: item.id,
            label: <span onClick={() => { }}>{capitalize(item.name)}</span>,
          };
        });
        useRetailerStore.getState().setOutletSelection(outletSelectOption);
        setOutletSelection(outletSelectOption);
        let currentOutletData = res.items.find(
          (item: any) => item.id === currentOutletId
        );

        if (currentOutletData) {
          useRetailerStore.getState().setCurrentOutlet(currentOutletData);
          setOutletKey(currentOutletData.id);
        }
      }
    } catch (error) {
      console.error("Error fetching outlets:", error);
    }
  };

  const retrieveCartCountData = () => {
    const dataSource = new DataSource(
      "retailerCart/count",
      "status=PENDING&retailerId=" +
      retailerAccess.id +
      "&outletId=" +
      localStorage.getItem("currentOutletId"),
      false,
      "v2"
    );

    dataSource
      .load()
      .then((res: any) => {
        if (res !== null) {
          useRetailerStore.getState().setCart(res || 0);
        }
      })
      .catch((err) => {
        // Handle error
        console.error("Error fetching cart data");
      });
  };

  // This onclick is for when retailer switch the outlet.
  // Then it will change the outlet id to local storage.
  const onClick: MenuProps["onClick"] = async ({ key }) => {
    try {
      localStorage.setItem("currentOutletId", key);
      const outletNameData = await getOutletData()
      useRetailerStore.setState({ outletNameData: outletNameData })

      let name = outletSelection?.find(
        (item: option) => item.key === key
      )?.label;
      if (name) {
        setOutletKey(name); // Assuming setCurrentOutlet is a function to update the current outlet
        setLoading(false);

        // check if the current page is in checkout page, will route back to cart page
        if (router.pathname === "/checkout") {
          router.push("/cart/myCart");
        } else {
          router.reload();
        }
      }
    } catch (error) {
      console.error("Error setting current outlet:", error);
    }
  };

  const hamburgersModel = () => {
    return (
      <div className="text-black ">
        {/* Translation Section */}

        <div className="menuModalDesign py-1  cursor-pointer">
          <div className="w-full flex items-center">
            <GlobalOutlined className="text-gray-400 w-[30px] font-bold text-xl flex justify-start items-center" />
            <SelectInput
              className="responsive-select"
              value={selectedLanguage}
              options={languageDropDownOption}
              onChange={(val) => {
                handleLanguageChange(val);
              }}
            />
          </div>
        </div>

        {/* Home Section */}
        <div
          className="menuModalDesign cursor-pointer py-1"
          onClick={() => {
            navigateTo("/landing");
            setHamburgerModal(false);
          }}
        >
          <div className="w-2/3 flex items-center ">
            <HomeFilled
              // src={Dashboard.src}
              // alt="Dashboard Icon"
              className="text-gray-400 text-[20px] w-[30px]" // Adjust the size as needed
            />
            <p>{t("Header.home")}</p>
          </div>
          <RightOutlined className="w-1/3 flex justify-end" />
        </div>

        {/* Ticketing Section */}
        <div
          className="menuModalDesign cursor-pointer py-1"
          onClick={() => {
            window.open("https://wa.me/60122418233", "_blank");
            setHamburgerModal(false);
          }}
        >
          <div className="w-2/3 flex items-center ">
            <Ticketing
              src={Ticketing.src}
              alt="Ticketing Icon"
              className="fill-gray-400 w-[30px]"
            />
            <p>{t("Header.ticketing")}</p>
          </div>
          <RightOutlined className="w-1/3 flex justify-end" />
        </div>

        {/* Dashboard Section */}
        <div
          className="menuModalDesign cursor-pointer py-1"
          onClick={() => {
            navigateTo("/profile/dashboard");
            setHamburgerModal(false);
          }}
        >
          <div className="w-2/3 flex items-center ">
            <Dashboard
              src={Dashboard.src}
              alt="Dashboard Icon"
              className="fill-gray-400 w-[30px] flex pl-[0.5px]" // Adjust the size as needed
            />
            <p>{t("Header.dashboard")}</p>
          </div>
          <RightOutlined className="w-1/3 flex justify-end" />
        </div>

        {/* Setting Section */}
        <div
          className="menuModalDesign cursor-pointer py-1"
          onClick={() => {
            navigateTo("/profile/setting");
            setHamburgerModal(false);
          }}
        >
          <div className="w-2/3 flex items-center">
            <Setting
              src={Setting.src}
              alt="Setting Icon"
              className="fill-gray-400 w-[30px] items-center" // Adjust the size as needed
            />
            <p>{t("Header.setting")}</p>
          </div>
          <RightOutlined className="w-1/3 flex justify-end" />
        </div>

        {/* Logout Section */}
        <div
          className="menuModalDesign cursor-pointer py-1"
          onClick={() => {
            setHamburgerModal(false);
            useRetailerStore.setState({ retailer: null });
            // localStorage.clear();
            localStorage.removeItem("accessToken");
            localStorage.removeItem("remember");
            localStorage.removeItem("currentOutletId");
            localStorage.removeItem("popupClosed");

            router.push("/login");
          }}
        >
          <div className="w-2/3 flex items-center">
            {/* <img
              src={}
              alt="Logout Icon"
              className="fill-gray-400 w-[30px] items-center" // Adjust the size as needed
            /> */}
            <LogoutOutlined className="text-gray-400 w-[30px] font-bold text-xl flex justify-start items-center" />
            <p>{t("Header.logout")}</p>
          </div>
          <RightOutlined className="w-1/3 flex justify-end" />
        </div>
      </div>
    );
  };

  const modalTitle = (
    <div className="border-b-2 border-primaryBlue pb-2 mb-2 text-primaryBlue font-bold">
      {`${retailerAccess?.firstName ?? ""} ${retailerAccess?.lastName ?? ""}`}
    </div>
  );

  return (
    <header>
      <div
        className={` bg-white  ${passedRouter.pathname === "/landing"
          ? "fixed top-0 left-0 right-0 transform z-[50]"
          : ""
          }`}
      >
        <nav
          className={`py-2 flex items-center page-width justify-between  ${isSmallScreen ? "w-full px-2" : "max-w-8xl"
            }`}
        >
          <ModalUI
            title={modalTitle}
            visible={isHamburgerModal && isSmallScreen}
            onCancel={() => setHamburgerModal(false)}
            width="95%"
            content={hamburgersModel()}
          />

          {/* Normal View */}
          <div className="hidden sm:flex w-full items-center justify-between">
            <div className="w-1/6 flex items-center">
              <Logo
                src={Logo.src}
                className="h-12 cursor-pointer"
                onClick={() => navigateTo("/landing")}
              />
            </div>
            <div className="w-4/6 flex justify-center">
              {searchBar && (
                <Form form={filterForm} className="w-96 pt-1">
                  <Form.Item name="fuzzySearch" className="mb-0 w-full h-full">
                    <DebounceFilterTextInput
                      prefix={<SearchOutlined />}
                      placeholder="Search"
                      maxLength={50}
                      disabled={inputDisabled}
                      debounceTime={500}
                      value={debounceValue}
                      onDebouncedChange={(item) => {
                        if (router.pathname !== "/product/productCategory") {
                          router.push(
                            `/product/productCategory?${queryInfo.category && queryInfo.categoryId
                              ? `category=${queryInfo.category}&categoryId=${queryInfo.categoryId}&`
                              : ""
                            }`
                          );
                        }
                        item
                          ? localStorage.setItem(
                            "products",
                            encodeURIComponent(item)
                          )
                          : localStorage.removeItem("products");
                        values(item);
                      }}
                      suffix={
                        <Tooltip
                          placement="right"
                          title="Search by Product SKU, Product Name, and Category"
                        >
                          <QuestionCircleOutlined className="text-gray-400" />
                        </Tooltip>
                      }
                    />
                  </Form.Item>
                </Form>
              )}
            </div>
            <div className="w-1.5/6 flex gap-x-3">
              <Tooltip placement="bottom" title={t("Header.landing")}>
                <IconButtonUI
                  onClick={() => navigateTo("/landing")}
                  icon={<HomeFilled />}
                />
              </Tooltip>
              {retailerAccess?.outletIds !== undefined &&
                retailerAccess?.outletIds?.length > 0 && (
                  <Dropdown
                    className="specific-dropdown"
                    menu={{
                      items: outletSelection,

                      onClick,
                      selectable: true,
                      activeKey: outletKey,
                    }}
                  >
                    <a
                      className="text-primaryBlue font-bold"
                      onClick={(e) => e.preventDefault()}
                    >
                      <Space>
                        <IconButtonUI icon={<Outlet src={Outlet.src} />} />
                      </Space>
                    </a>
                  </Dropdown>
                )}

              <Tooltip placement="bottom" title={t("Header.cart")}>
                <div className="relative inline-block">
                  <IconButtonUI
                    onClick={() => navigateTo("/cart/myCart")}
                    icon={<CartLogo src={CartLogo.src} className="" />}
                  />
                  {cartCount > 0 && (
                    <div className="absolute top-[-5px] right-[-1px] bg-red-500 rounded-full p-1 text-white text-xs">
                      {cartCount}
                    </div>
                  )}
                </div>
              </Tooltip>

              <Tooltip placement="bottom" title={t("Header.dashboard")}>
                <div>
                  <IconButtonUI
                    onClick={() => navigateTo("/profile/dashboard")}
                    icon={<Dashboard src={Dashboard.src} className="" />}
                  />
                </div>
              </Tooltip>
              <Tooltip placement="bottom" title={t("Header.setting")}>
                <IconButtonUI
                  onClick={() => navigateTo("/profile/setting")}
                  icon={<Setting src={Setting.src} />}
                />
              </Tooltip>
              <div className="translation-select">
                <Select
                  className="h-[28px]"
                  value={selectedLanguage}
                  options={languageDropDownOption}
                  onChange={handleLanguageChange}
                />
              </div>
            </div>
          </div>

          {/* Mobile View */}
          <div className="flex sm:hidden w-full items-center gap-x-2 min-h-[40px] sm:min-h-[30px] sm:px-4">
            {searchBar ? (
              <Row className="flex w-full justify-between">
                <Form form={filterForm} className="w-full">
                  <Form.Item
                    name="fuzzySearch"
                    className="flex-1 justify-top mb-0 w-full h-full sm:h-7"
                  >
                    <DebounceFilterTextInput
                      prefix={<SearchOutlined />}
                      placeholder="Search"
                      maxLength={50}
                      disabled={inputDisabled}
                      debounceTime={500}
                      value={debounceValue}
                      onDebouncedChange={(item) => {
                        if (router.pathname !== "/product/productCategory") {
                          router.push(
                            `/product/productCategory?${queryInfo.category && queryInfo.categoryId
                              ? `category=${queryInfo.category}&categoryId=${queryInfo.categoryId}&`
                              : ""
                            }`
                          );
                        }
                        item
                          ? localStorage.setItem(
                            "products",
                            encodeURIComponent(item)
                          )
                          : localStorage.removeItem("products");
                        values(item);
                      }}
                      suffix={
                        <Tooltip
                          placement="right"
                          title="Search by Product SKU, Product Name, and Category"
                        >
                          <QuestionCircleOutlined className="text-gray-400" />
                        </Tooltip>
                      }
                    />
                  </Form.Item>
                </Form>
              </Row>
            ) : (
              <div className="flex w-full justify-end" />
            )}

            {/* Fixed Buttons */}
            <div className="flex gap-2 items-center">
              <Tooltip placement="bottom" title={t("Header.cart")}>
                <div className="relative inline-block">
                  <IconButtonUI
                    onClick={() => navigateTo("/cart/myCart")}
                    icon={<CartLogo src={CartLogo.src} />}
                  />
                  {cartCount > 0 && (
                    <div
                      className="absolute top-[-5px] right-[-1px] bg-red-500 rounded-full p-1 text-white text-xs 
            sm:top-[-3px] sm:right-[-2px] sm:p-0.5 sm:text-[10px] sm:min-w-[12px] sm:min-h-[12px] flex items-center justify-center"
                    >
                      {cartCount}
                    </div>
                  )}
                </div>
              </Tooltip>
              <Tooltip placement="bottom" title={t("Header.menu")}>
                <div>
                  <IconButtonUI
                    onClick={() => setHamburgerModal(true)}
                    icon={<MenuOutlined />}
                  />
                </div>
              </Tooltip>
            </div>
          </div>
        </nav>

        <div className=" bg-bgOrange py-2 sm:flex ">
          <div className="page-width max-w-8xl">
            <nav className={items?.className}>
              {items?.map((item: any, index: number) => {
                return (
                  <>
                    <span
                      key={index}
                      style={{ cursor: "pointer" }}
                      onClick={() => navigateTo(`${item.route}`)}
                      className={
                        "text-labelGray hover:text-buttonOrange transition-all duration-30 " +
                        item.className
                      }
                    >
                      {item.label}
                    </span>
                  </>
                );
              })}
            </nav>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
