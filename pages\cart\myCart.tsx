import React, { useEffect, useState } from "react";
import { Content } from "antd/lib/layout/layout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import {
  Button,
  Col,
  Row,
  List,
  TableProps,
  Table,
  Progress,
  Modal,
} from "antd";
import defaultImage from "../../assets/default/emptyImage.png";
import Header, { supportedLocales } from "../../components/header";
import { CheckboxInput } from "@/components/input";
import {
  BackButtonUI,
  CounterComponent,
  PrimaryButtonUI,
  SecondaryButtonUI,
} from "@/components/buttonUI";
import { useRouter } from "next/router";
import {
  FixedBottomBar,
  MessageErrorUI,
  MessageSuccessUI,
  ModalInfoUI,
  RenderPromotionCard,
  TableUI,
} from "@/components/ui";
import {
  Cart,
  CompanyAverageSales,
  CompanyGeneralInfo,
  ConfigurableField,
  Outlet,
  Product,
  ProductCartUI,
  ProductExceedQuantity,
  Promotion,
  Retailer,
  TradeInfoAggregate,
  UOM,
} from "../../components/type";
import {
  PUBLIC_BUCKET_URL,
  NumberThousandSeparator,
  DataSource,
  encodeParams,
  PicSignedUrl,
  DataSourceWithPageNumber,
  removeDuplicateArray,
} from "@/stores/utilize";
import PreOrder from "../../assets/logo/preOrder.svg";
import apiHelper from "../api/apiHelper";
import { ModalConfirmUI, ModalUI } from "@/components/modalUI";
import useRetailerStore from "@/stores/store";
import { getOutletData, getRetailerData } from "@/stores/authContext";
import { cloneDeep } from "lodash";
import moment from "moment";
import {
  getPromotionProduct,
  getPromotionRelatedOutlet,
  onInitCalculatePromotion,
  onInitManualApplyPromotionFromCarts,
} from "../api/salesOrderHelper";
import { RightOutlined, DeleteFilled } from "@ant-design/icons";
import AppFooter from "@/components/footer";
import _ from "lodash";

function CartListing() {
  const { t } = useTranslation("common");
  const router = useRouter();
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({}); //access control
  const [activeAt, setActiveAt] = useState<string>(moment().startOf("day").add(1, "millisecond").toISOString());

  const [totalItemChecked, setTotalItemChecked] = useState(0);
  const [groupByCompanyMap, setGroupByCompanyMap] = useState(new Map());
  const [companyMap, setCompanyMap] = useState(new Map());
  const [productMap, setProductMap] = useState(new Map());
  const [uomMap, setUomMap] = useState(new Map());
  const [productPriceGroupLatestMap, setProductPriceGroupLatestMap] = useState(
    new Map()
  );
  const [displayMap, setDisplayMap] = useState(new Map());
  const [tradeInfoAggreateMap, setTradeInfoAggreateMap] = useState(new Map());
  const [uomConversionMap, setUomConversionMap] = useState(new Map());
  const [checkAll, setCheckAll] = useState(false);
  const [indeterminate, setIndeterminate] = useState(false);
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  // const [viewPromotions, setViewPromotions] = useState<Promotion[]>([]);
  const [isCheckoutButton, setCheckoutButtonDisable] = useState(true);
  const [isCheckoutButtonLoading, setIsCheckoutButtonLoading] = useState(false);

  // AHS Modal Pop Out
  const [isAHSVisible, setIsAHSVisible] = useState(false);
  const [salesData, setSalesData] = useState<any[]>([]);
  const [companyAverageSales, setCompanyAverageSales] = useState<
    CompanyAverageSales[]
  >([]);

  // AHS Percentage
  const exceededProductsPercentage = 25;

  // Reset AHS when onclick Checkout
  const [AHSReset, setAHSReset] = useState(false);

  const [manualApplyPromotionList, setManualApplyPromotionList] = useState<
    Promotion[]
  >([]);
  const [
    isManualApplyPromotionModalVisible,
    setIsManualApplyPromotionModalVisible,
  ] = useState(false);
  const [manualApplyRowSelectionKey, setManualApplyRowSelectionKey] = useState<
    any[]
  >([]);
  const [doneApplyRowSelectionKey, setDoneApplyRowSelectionKey] = useState<
    any[]
  >([]);

  const [outletInfo, setOutletInfo] = useState<Outlet>();
  const [checkoutSetting, setCheckoutSetting] = useState<ConfigurableField>({
    id: '',
    name: '',
    value: '',
    status: '',
  })

  // data loading
  const [loading, setLoading] = useState(false);
  const [percent, setPercent] = useState<number>();

  const headerItems = [
    {
      label: t("Header.home"),
      route: "/landing",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    {
      label: t("Header.cart"),
      route: "/cart/myCart",
      className: "clickableLabelTextStyle",
    },
  ];

  const tableProps: TableProps<any> = {
    rowSelection: {
      onChange: (selectedRowKeys: React.Key[]) => {
        setManualApplyRowSelectionKey(selectedRowKeys);
      },
      selectedRowKeys: manualApplyRowSelectionKey,
    },
    loading: false,
    showHeader: false,
    rowKey: (record: any) => record.id,
    pagination: false,
  };

  useEffect(() => {
    // Ensure code runs only on the client-side
    if (typeof window !== "undefined") {
      window.addEventListener("scroll", function () {
        var fixedBottomBar = document.querySelector(
          ".fixed-bottom-bar"
        ) as HTMLElement;
        var appFooter = document.querySelector(".app-footer") as HTMLElement;

        if (!appFooter || !fixedBottomBar) {
          return; // Exit early if either appFooter or fixedBottomBar is null
        }

        var footerOffset = appFooter.getBoundingClientRect().top;
        var viewportHeight = window.innerHeight;

        if (footerOffset <= viewportHeight) {
          fixedBottomBar.style.bottom = viewportHeight - footerOffset + "px";
        } else {
          fixedBottomBar.style.bottom = "0";
        }
      });
    }
  }, []);

  const columns: any = [
    {
      title: "Promotion",
      dataIndex: "id",
      render: (_: any, promotion: Promotion) => {
        if (promotion?.id) {
          return RenderPromotionCard(
            promotion,
            (event: any) => {
              let manualApplyPromotionKey = [...manualApplyRowSelectionKey];
              if (manualApplyPromotionKey.includes(promotion.id)) {
                manualApplyPromotionKey = manualApplyPromotionKey.filter(
                  (key) => key !== promotion.id
                );
                setManualApplyRowSelectionKey(
                  Array.from(new Set(manualApplyPromotionKey))
                );
              } else {
                manualApplyPromotionKey = manualApplyPromotionKey.concat([
                  promotion.id,
                ]);
                // remove duplicated
                setManualApplyRowSelectionKey(
                  Array.from(new Set(manualApplyPromotionKey))
                );
              }
            },
            {
              isShowArrow: false,
              isShowQuestion: false,
            }
          );
        }
      },
    },
  ];

  useEffect(() => {
    if (router.isReady && Object.keys(useRetailerStore.getState()).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};

      getOutlet();
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }

      // get outlet info to check autoCheckout eligibility
      const outletNameData: any = useRetailerStore.getState().outletNameData || {}
      if (!Object.keys(outletNameData).length) {
        getOutletData().then((value: any) => {
          setActiveAt(value?.nextQualifiedDate ?? moment().startOf("day").add(1, "millisecond").toISOString())
        })
      } else {
        setActiveAt(outletNameData?.nextQualifiedDate ?? moment().startOf("day").add(1, "millisecond").toISOString())
      }
    }
  }, [router.isReady, Object.keys(useRetailerStore.getState()).length]);

  useEffect(() => {
    if (
      retailerAccess &&
      Object.keys(retailerAccess).length > 0 &&
      outletInfo?.id
    ) {
      getProductFromCart();
    }
  }, [retailerAccess, outletInfo]);

  useEffect(() => {
    let allChecked = true;

    displayMap.forEach((companyProducts) => {
      const isCompanyChecked = companyProducts.every(
        (product: any) => product.checked
      );
      if (!isCompanyChecked) {
        allChecked = false;
      }
    });

    // updateRetailerCart after displayMap done remapped
    if (displayMap.size > 0) {
      updateRetailerCart();
    }

    if (allChecked) {
      setCheckAll(true);
      setIndeterminate(false);
    } else {
      setCheckAll(false);
    }
  }, [displayMap]);

  // updateRetailerCart after displayMap done remapped
  const updateRetailerCart = async () => {
    if (displayMap.size === 0) return;
    const dataToUpdate = Array.from(_.cloneDeep(displayMap).values())
      .flat()
      .filter((item) => item.unitPriceChanged === true);
    if (dataToUpdate.length > 0) {
      for (let i = 0; i < dataToUpdate.length; i++) {
        await apiHelper.PUT(
          "retailerCart?id=" + dataToUpdate[i].id,
          dataToUpdate[i],
          "",
          "v2"
        );
      }
    }
  };

  useEffect(() => {
    if (
      groupByCompanyMap.size > 0 &&
      companyMap.size > 0 &&
      productMap.size > 0
    ) {
      reMap();
    }
  }, [groupByCompanyMap, companyMap, productMap]);

  // Define the useEffect hook
  // useEffect(() => {
  //   // This function will run every time companyAverageSales changes
  //   if (companyAverageSales.length > 0) {
  //     compareProductQuantity();
  //   }
  // }, [companyAverageSales]); // Add companyAverageSales as a dependency

  const getCartCount = () => {
    const dataSource = new DataSource(
      "retailerCart/count",
      "status=PENDING&retailerId=" +
      retailerAccess.id +
      "&outletId=" +
      localStorage.getItem("currentOutletId"),
      false,
      "v2"
    );

    dataSource
      .load()
      .then((res: any) => {
        if (res !== null) {
          useRetailerStore.getState().setCart(res || 0);
        }
      })
      .catch((err) => {
        // Handle error
        console.error("Error fetching cart data");
      });
  };

  const getProductFromCart = () => {
    setLoading(true);
    const dataSource = new DataSourceWithPageNumber(
      "retailerCarts",
      `status=PENDING&retailerId=${retailerAccess.id
      }&outletId=${localStorage.getItem("currentOutletId")}`,
      true,
      "v2"
    );

    dataSource
      .load()
      .then(async (res: any) => {
        if (res && res.length === 0) {
          setLoading(false);
        } else if (res !== null) {
          // useRetailerStore.getState().setCart(res || 0);
          const objectMap = res.reduce((accumulator: any, current: Cart) => {
            accumulator["companyId"] = accumulator["companyId"] || [];
            if (
              current.companyId &&
              !companyMap.has(current.companyId) &&
              !accumulator["companyId"].includes(current.companyId)
            ) {
              accumulator["companyId"].push(current.companyId ?? "");
            }

            accumulator["productId"] = accumulator["productId"] || [];
            if (
              current.productId &&
              !productMap.has(current.productId) &&
              !accumulator["productId"].includes(current.productId)
            ) {
              accumulator["productId"].push(current.productId ?? "");
            }

            accumulator["productUOMId"] = accumulator["productUOMId"] || [];
            if (
              current.productUOMId &&
              !uomMap.has(current.productUOMId) &&
              !accumulator["productUOMId"].includes(current.productUOMId)
            ) {
              accumulator["productUOMId"].push(current.productUOMId ?? "");
            }

            accumulator["defaultUOMId"] = accumulator["defaultUOMId"] || [];
            if (
              current.defaultUOMId &&
              !uomMap.has(current.defaultUOMId) &&
              !accumulator["defaultUOMId"].includes(current.defaultUOMId)
            ) {
              accumulator["defaultUOMId"].push(current.defaultUOMId ?? "");
            }

            if (
              current.companyId &&
              current.productTradeInfoId &&
              current.priceGroupId &&
              current.productUOMId
            ) {
              const reCallProductPriceGroup =
                current.companyId +
                "-" +
                current.productTradeInfoId +
                "-" +
                current.priceGroupId +
                "-" +
                current.productUOMId;
              accumulator["productPriceGroup"] =
                accumulator["productPriceGroup"] || [];
              if (
                !productPriceGroupLatestMap.has(reCallProductPriceGroup) &&
                !accumulator["productPriceGroup"].includes(
                  reCallProductPriceGroup
                )
              ) {
                accumulator["productPriceGroup"].push(
                  reCallProductPriceGroup ?? ""
                );
              }
            }

            // accumulator["promotionIds"] = accumulator["promotionIds"] || [];
            // accumulator["promotionIds"] = accumulator["promotionIds"].concat(current.promotionIds ?? "");

            return accumulator;
          }, {});

          // const totalProductCount = objectMap["productId"].length;
          // console.log("Total Product Count", totalProductCount);

          getCompany(objectMap["companyId"]);
          getProduct(cloneDeep(objectMap["productId"]));
          getUOM(objectMap["productUOMId"]);
          getUOM(objectMap["defaultUOMId"]);
          const products = await getAggreateProducts(
            cloneDeep(objectMap["productId"])
          );
          getUOMConversion(objectMap["productId"]);
          const latestProductPriceGroup: any = await getLatestProductPriceGroup(
            objectMap["productPriceGroup"]
          );
          // sorting
          // res = _.sortBy(
          //   res.map((item: any) => {
          //     if (item.productId) {
          //       item.productName =
          //         products.find(
          //           (product) => product.productCatalogueId === item.productId
          //         )?.name || "";
          //     }
          //     return item;
          //   }),
          //   "productName"
          // );

          if (!groupByCompanyMap.size) {
            const productCatalogueId = res.map(
              (product: Cart) => product.productId
            );

            const promotionRelateds = await getPromotionProduct(
              cloneDeep(productCatalogueId)
            );

            const promotionRelatedOutlets = await getPromotionRelatedOutlet(
              [...promotionRelateds],
              "TRUE",
              [],
              // outletInfo?.companyBranchId || ""
              ""
            );

            const promotionRelatedOutletIds = promotionRelatedOutlets.map(
              (promotion) => promotion.id
            );

            const promotionsCanApply = promotionRelateds.map((promotion) => {
              const intersection = promotion.promoIds.filter((item) =>
                promotionRelatedOutletIds.includes(item)
              );
              if (intersection?.length) {
                promotion.promoIds = intersection.flat();
              } else {
                promotion.promoIds = [];
              }
              return promotion;
            });
            res.map(async (product: Cart) => {
              const promotionProduct = promotionsCanApply?.find(
                (promotion) => promotion.productId === product.productId
              );
              product.promotionIds = promotionProduct?.promoIds || [];

              const currentPrice = _.cloneDeep(product.unitPrice);
              if (
                product.companyId &&
                product.productTradeInfoId &&
                product.priceGroupId &&
                product.productUOMId
              ) {
                const newKey =
                  product.companyId +
                  "-" +
                  product.productTradeInfoId +
                  "-" +
                  product.priceGroupId +
                  "-" +
                  product.productUOMId;
                const productPriceGroup = latestProductPriceGroup.get(newKey);
                if (productPriceGroup) {
                  product.unitPrice = productPriceGroup.sellingPrice;
                  product.totalPrice =
                    productPriceGroup.sellingPrice * product.quantity;

                  // temporary calculate totalNetPrice
                  // product.totalNetPrice = parseFloat(
                  //   (
                  //     product.quantity *
                  //     productPriceGroup.sellingPrice -
                  //     (product.totalDiscount || 0)
                  //   ).toFixed(2)
                  // )
                }

                // indication of unitPriceChanged
                product.unitPriceChanged = false;
                if (currentPrice !== productPriceGroup?.sellingPrice) {
                  // await apiHelper.PUT("retailerCart?id=" + product.id, product, "", "v2")
                  product.unitPriceChanged = true;
                }
              }
              return product;
            });

            setTotalItemChecked(res?.length);
            setCheckoutButtonDisable(false);
          }

          // After this function is run then it will call reMap Function
          setGroupByCompanyMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.forEach((product: any) => {
              const reMap = {
                ...product,
                checked: true,
              };

              if (!newDataMap.has(product?.companyId)) {
                newDataMap.set(product.companyId, [reMap]);
              } else {
                const existingCompany = newDataMap.get(product.companyId);
                if (existingCompany) {
                  // Check if product ID already exists in the existingCompany array
                  const productIndex = existingCompany.findIndex(
                    (item: any) => item.id === product.id
                  );

                  if (productIndex === -1) {
                    newDataMap.set(product.companyId, [
                      ...existingCompany,
                      reMap,
                    ]);
                  } else {
                    // If product ID exists, you might want to update the existing product instead
                    existingCompany[productIndex] = reMap;
                    newDataMap.set(product.companyId, [...existingCompany]);
                  }
                }
              }
            });
            return newDataMap;
          });
        }
      })
      .catch(() => {
        setLoading(false);

        //* This Part need re-edit*//
      });
  };

  const getCompany = async (id: string[] = []) => {
    const dataSource = new DataSource(
      "companies",
      encodeParams({ id: id }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setCompanyMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: CompanyGeneralInfo) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getProduct = async (id: string[] = [], withoutSetToState = false) => {
    let tempProductMap = new Map(productMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      //   status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "productCatalogues",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          if (!withoutSetToState) {
            setProductMap((prevDataMap) => {
              const newDataMap = new Map(prevDataMap);
              res.items.forEach((item: Product) => {
                if (!newDataMap.has(item.id)) {
                  newDataMap.set(item.id, item);
                }
              });
              return newDataMap;
            });
          }
          res.items?.map((item: Product) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getUOM = async (id: string[] = []) => {
    let tempProductMap = new Map(uomMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource("uoms", encodeParams(params), false);
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setUomMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: UOM) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: UOM) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getAggreateProducts = async (
    productIds: string[] = []
  ): Promise<TradeInfoAggregate[]> => {
    let currentOutletId = localStorage.getItem("currentOutletId");
    let result: any = [];
    try {
      while (productIds.length) {
        const params: any = {
          companyId: retailerAccess.companyId,
          outletId: currentOutletId,
          maxResultsPerPage: 100,
          pageNumber: "1",
          productCatalogueId: productIds.splice(0, 50),
          activeAt: activeAt
        };

        const dataSource = new DataSource(
          "productTradeInfo/aggregate",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load();

        if (!res?.items?.length) {
          productIds = [];
          return result;
        }
        const productTradeInfoAggregate: TradeInfoAggregate[] = res?.items;
        result = result.concat(productTradeInfoAggregate);
      }

      const newDataMap = new Map(tradeInfoAggreateMap);
      result.map((item: TradeInfoAggregate) => {
        const key = `${item.companyId}|${item.productCatalogueId}`;
        newDataMap.set(key, item);
      });
      setTradeInfoAggreateMap(newDataMap);
      return result;
    } catch (err) {
      return result;
    }
  };

  const getPromotions = async (promotionIds: string[] = []) => {
    let result: any = [];
    try {
      // to clear the promotion ui when does not has promotionIds
      if (!promotionIds.length) {
        setPromotions([]);
        return;
      }

      while (promotionIds.length) {
        const params = {
          activeAt: activeAt,
          status: "ACTIVE",
          id: promotionIds.splice(0, 50),
        };
        const dataSource = new DataSource(
          "promotions",
          encodeParams(params),
          true
        );
        const res: any = await dataSource.load();
        if (res?.length) {
          const updatedPromotions = await res.reduce(
            async (accumPromise: any, current: Promotion) => {
              const accum = await accumPromise;

              if (current.images?.length) {
                const image: any = await PicSignedUrl(current.images[0]);
                current.images = [image];
              }

              accum.push(current);
              return accum;
            },
            Promise.resolve([])
          );

          result = result.concat(updatedPromotions);
        }
      }
      setPromotions(result);
      return result;
    } catch (err) {
      return result;
    }
  };

  const getManualPromotion = async () => {
    let selectedCompanyProduct: ProductCartUI[] = [];
    let promotionSelectedNow: any = [];
    displayMap.forEach((productCarts) => {
      if (productCarts.length) {
        productCarts.map((item: ProductCartUI) => {
          if (item.checked) {
            selectedCompanyProduct = selectedCompanyProduct.concat(item);
            const promotionIds = (item.promotionIds || []).concat(
              item?.promotionManualApplyIds || []
            );
            promotionSelectedNow = Array.from(
              new Set(promotionSelectedNow.concat(promotionIds))
            );
          }
        });
      }
    });
    const { promotion } = await onInitManualApplyPromotionFromCarts(
      selectedCompanyProduct
    );

    setManualApplyRowSelectionKey(promotionSelectedNow);
    setManualApplyPromotionList(promotion);
    setIsManualApplyPromotionModalVisible(true);
  };

  // const getUOMConversion = async (id: string = "") => {
  //   const dataSource = new DataSource(
  //     "productCatalogue/uom/formula",
  //     encodeParams({ id: id }),
  //     false
  //   );
  //   const conversion: any = await dataSource.load();
  //   const conversionData = conversion.items[0].productUOM;
  //   return conversionData;
  // };

  const getOutlet = () => {
    let currentOutletId = localStorage.getItem("currentOutletId");
    let params: any = {
      id: currentOutletId,
    };
    const dataSource = new DataSource("outlets", encodeParams(params), false);

    dataSource
      .load()
      .then(async (res: any) => {
        const outlet: Outlet = res?.items?.length ? res.items[0] : {};

        delete outlet?.outletProductList;

        setOutletInfo(outlet);
      })
      .catch(() => { });
  };

  const getConfigureSetting = async (companyId: string = "") => {
    let params: any = {
      companyId: companyId,
      name: `RetailerWebsiteCheckoutSetting-${retailerAccess?.companyCode}`,
      status: "ACTIVE",
      activeAt: moment().startOf("day").add(1, "millisecond").toISOString()
    }

    const response: any = await apiHelper.GET(`configurableFields?${encodeParams(params)}`)
    if (response?.items?.length) {
      const items: ConfigurableField = response.items[0];
      if (items) {
        setCheckoutSetting(items)
        return items
      }
    }

    return {}
  }

  const getLatestProductPriceGroup = async (keys: string[] = []) => {
    let tempProductPriceGroupMap = new Map(productPriceGroupLatestMap);
    if (!keys?.length) return tempProductPriceGroupMap;
    // const currentDate = moment().startOf("day").toISOString();

    let manualIndex = 0;
    const totalKeysNumber = keys.length;
    try {
      while (keys?.length) {
        const data = keys?.splice(0, 1);
        const [companyId, productTradeInfoId, priceGroupId, productUOMId] =
          data[0].split("-");

        let percent = (manualIndex / totalKeysNumber) * 100;
        setPercent(Math.ceil(percent));
        manualIndex++;

        const params: any = {
          companyId: companyId,
          productTradeInfoId: productTradeInfoId,
          priceGroupId: priceGroupId,
          productUOMId: productUOMId,
          activeAt: activeAt,
        };

        const dataSource = new DataSource(
          "productPriceGroups",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          keys = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setProductPriceGroupLatestMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: any) => {
              if (!newDataMap.has(data[0])) {
                newDataMap.set(data[0], item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: any) => {
            tempProductPriceGroupMap.set(data[0], item);
          });
        }
      }
    } catch (err) {
      return tempProductPriceGroupMap;
    }
    return tempProductPriceGroupMap;
  };

  const getUOMConversion = async (id: string[] = []) => {
    let tempProductMap = new Map(uomMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "productCatalogue/uom/formula",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setUomConversionMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: any) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: any) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const handlerPromotionApply = async () => {
    const keys = [...manualApplyRowSelectionKey];
    // use for applied manual promotion key;
    setDoneApplyRowSelectionKey([...manualApplyRowSelectionKey]);

    const promotionManualApplyList = [...manualApplyPromotionList];

    const promotionNeedToRemoveFromManualApplyIds = promotionManualApplyList
      .filter((promotion: Promotion) => !keys.includes(promotion?.id))
      .map((promotion: Promotion) => promotion?.id);

    let selectedCompanyProduct: ProductCartUI[] = [];

    displayMap.forEach((productCarts) => {
      if (productCarts.length) {
        productCarts.map((item: ProductCartUI) => {
          if (item.checked) {
            selectedCompanyProduct = selectedCompanyProduct.concat(item);
          }
        });
      }
    });

    selectedCompanyProduct.map((item) => {
      item.promotionManualApplyIds = [...keys];
      // clear the promotionIds if the promotionId is not in the selected promotionIds
      item.promotionApplyIds = item.promotionApplyIds?.filter(
        (promotionId: string) => {
          return !promotionNeedToRemoveFromManualApplyIds?.includes(
            promotionId
          );
        }
      );
      // clear the promotionIds if the promotionId is not in the selected promotionIds
      item.promotionIds = item.promotionIds?.filter((promotionId: string) => {
        return !promotionNeedToRemoveFromManualApplyIds?.includes(promotionId);
      });
      return item;
    });

    let tempMap = new Map();

    // grouping
    setGroupByCompanyMap((prevDataMap) => {
      tempMap = new Map(prevDataMap);
      selectedCompanyProduct.forEach((product: any) => {
        const reMap = {
          ...product,
        };
        if (!tempMap.has(product.companyId)) {
          tempMap.set(product.companyId, [reMap]);
        } else {
          const existingCompany = tempMap.get(product.companyId);
          if (existingCompany) {
            // Check if product ID already exists in the existingCompany array
            const productIndex = existingCompany.findIndex(
              (item: any) => item.id === product.id
            );

            if (productIndex === -1) {
              tempMap.set(product.companyId, [...existingCompany, reMap]);
            } else {
              // If product ID exists, you might want to update the existing product instead
              existingCompany[productIndex] = reMap;
              tempMap.set(product.companyId, [...existingCompany]);
            }
          }
        }
      });
      return tempMap;
    });

    onInitCalculatePromotion(tempMap).then((res) => {
      handlePromotionView(res);
      setDisplayMap(res);
    });
  };

  const handlePromotionView = (tempMap: any) => {
    let promotionIds: string[] = [];
    tempMap.forEach((companyProducts: any) => {
      companyProducts.map((item: ProductCartUI) => {
        promotionIds = promotionIds.concat(item.promotionApplyIds || []);
      });
    });
    getPromotions(promotionIds);
  };

  const reMap = () => {
    let tempMap = new Map(groupByCompanyMap);

    tempMap.forEach((companyProducts) => {
      // companyProducts.companyName = companyMap.get(
      //   companyProducts[0].companyId
      // )?.name;

      companyProducts.map(async (item: any) => {
        const product = productMap.get(item.productId);
        if (product) {
          const selectedUom = product?.productUOM.find(
            (val: any) => val.productUOMId === item.productUOMId
          );
          const key = `${companyProducts[0].companyId}|${item.productId}`;
          const tradeinfo = tradeInfoAggreateMap.get(key);
          item.name = product.name;
          item.code = product.sku;
          item.moq = tradeinfo?.minimumOrder || 0;
          item.minimumIncrement = tradeinfo?.minimumIncrement || 0;

          // item.unitPrice = tradeinfo?.sellingPrice || 0;
          // item.totalPrice = parseFloat(
          //   (item.unitPrice * item.quantity).toFixed(2)
          // )

          let result = 0;
          // const uomConversion = await getUOMConversion(item.productId);
          const uomConversionData = uomConversionMap.get(
            item.productId
          )?.productUOM;

          const conversionNumber =
            uomConversionData.find(
              (val: any) => val.productUOMId === item.defaultUOMId
            )?.conversionToSmallestUOM ?? 0;
          if (item.productUOMId !== item.defaultUOMId) {
            result = conversionNumber * item.minimumOrder;
          }

          item.conversionNumber = result;

          item.picture =
            selectedUom?.pictures && Array.isArray(selectedUom?.pictures)
              ? selectedUom?.pictures[0] ?? ""
              : "";

          if (!item.picture) {
            // find picture from product catalogue
            product.productUOM.map((uom: any) => {
              if (
                uom.pictures &&
                Array.isArray(uom.pictures) &&
                !item.picture
              ) {
                item.picture = uom.pictures[0] ?? "";
              }
            });
          }

          item.uom = uomMap.get(selectedUom?.productUOMId)?.name;
          if (item.discount !== 0) {
            item.discountRate = item.total / item.discount;
          }
        }
      });

      return companyProducts;
    });

    setTimeout(() => {
      setPercent(100);
    }, 2000);

    onInitCalculatePromotion(tempMap).then((res: any) => {
      // get the promotion and getPromotion
      handlePromotionView(res);
      setDisplayMap(res);

      setLoading(false);
    });
  };

  const totalPriceOfMyCart = Array.from(displayMap.values()).reduce(
    (accumulator: number, curr: any) => {
      const products = curr || [];
      const value = products.reduce((total: number, item: any) => {
        if (item.checked) total += item.totalPrice - item.totalDiscount || 0;
        return total;
      }, 0);
      accumulator += value;
      return accumulator;
    },
    0
  );

  const totalDiscountOfMyCart = Array.from(displayMap.values()).reduce(
    (accumulator: number, curr: any) => {
      const products = curr || [];
      const value = products.reduce((discountPrice: number, item: any) => {
        if (item.checked) discountPrice += item.totalDiscount || 0;
        return discountPrice;
      }, 0);
      accumulator += value;
      return accumulator;
    },
    0
  );

  const handleCheck = (id: string, check: boolean, company: string) => {
    const selectedCompanyProduct = displayMap.get(company);

    const selectedProductIndex = selectedCompanyProduct.findIndex(
      (item: any) => item.id === id
    );

    if (selectedProductIndex !== -1) {
      selectedCompanyProduct[selectedProductIndex].checked = check;
      // const tempViewPromotions = [...viewPromotions];

      // need to add call api on here (promotion calculation)
      // due to the ui cannot be different which api applyed.
      const someproductChecked = selectedCompanyProduct.some(
        (product: any) => product.checked
      );

      if (someproductChecked) {
        selectedCompanyProduct[0].indeterminate = true;
      } else {
        selectedCompanyProduct[0].indeterminate = false;
      }

      const everyproductChecked = selectedCompanyProduct.every(
        (product: any) => product.checked
      );

      if (everyproductChecked) {
        selectedCompanyProduct[0].checkAll = true;
        selectedCompanyProduct[0].indeterminate = false;
        setIndeterminate(true);
      } else {
        selectedCompanyProduct[0].checkAll = false;
        setIndeterminate(false);
      }

      let tempMap = new Map(displayMap);
      tempMap.set(company, [...selectedCompanyProduct]);

      onInitCalculatePromotion(tempMap).then((res) => {
        handlePromotionView(res);
        setDisplayMap(res);
      });

      const checkedItemsCount = Array.from(tempMap.entries()).reduce(
        (accumulator, [, products]: [string, any[]]) =>
          accumulator +
          products.filter((product: any) => product.checked).length,
        0
      );

      setTotalItemChecked(checkedItemsCount);
      setCheckoutButtonDisable(!check);
    }
  };

  const handleSelectAllCompanyProduct = (check: boolean, company: string) => {
    const selectedCompanyProduct = displayMap.get(company);

    selectedCompanyProduct.forEach((product: any) => {
      product.checked = check;
      product.checkAll = check;
    });

    let tempMap = new Map(displayMap);
    tempMap.set(company, [...selectedCompanyProduct]);

    onInitCalculatePromotion(tempMap).then((res) => {
      handlePromotionView(res);
      setDisplayMap(res);
    });

    setTotalItemChecked(check ? selectedCompanyProduct.length : 0);
    setCheckoutButtonDisable(!check);
  };

  const handleSelectAll = (check: boolean) => {
    const tempMap = new Map(displayMap);

    let totalProduct = 0;
    tempMap.forEach((companyProducts) => {
      totalProduct = totalProduct + companyProducts.length;
      companyProducts.forEach((product: any) => {
        product.checked = check;
      });
    });

    onInitCalculatePromotion(tempMap).then((res) => {
      handlePromotionView(res);
      setDisplayMap(res);
    });

    setTotalItemChecked(check ? totalProduct : 0);
    setCheckoutButtonDisable(!check);
  };

  const handleAddTotal = async (item: any, quantity: number) => {
    // let discountPrice = item.unitDiscount * quantity;
    // let total = item.unitPrice * quantity;

    const selectedCompanyProduct = displayMap.get(item.companyId);
    const selectedProductIndex = selectedCompanyProduct.findIndex(
      (val: any) => val.id === item.id
    );

    if (selectedProductIndex !== -1) {
      selectedCompanyProduct[selectedProductIndex].totalPrice = parseFloat(
        (item.unitPrice * quantity).toFixed(2)
      );

      selectedCompanyProduct[selectedProductIndex].quantity = quantity;
      let tempMap = new Map(displayMap);
      tempMap.set(item.companyId, [...selectedCompanyProduct]);

      // const uomConversion = await getUOMConversion(item.productId);
      // const conversionNumber =
      //   uomConversion.find((val: any) => val.productUOMId === item.defaultUOMId)
      //     ?.conversionToSmallestUOM ?? 0;

      // let pass = true;
      // let result = 0;
      // if (item.productUOMId !== item.defaultUOMId) {
      //   result = conversionNumber * item.minimumOrder;
      // }
      // if (quantity < result) {
      //   MessageErrorUI(
      //     t("Cart.meetMOQ") +
      //       " " +
      //       result +
      //       " " +
      //       uomMap.get(item.productUOMId)?.name
      //   );
      //   pass = false;
      // }

      setLoading(true);
      setPercent(0);
      onInitCalculatePromotion(tempMap).then((res) => {
        const tempCompayProduct = res.get(item.companyId);
        const index = tempCompayProduct.findIndex(
          (val: any) => val.id === item.id
        );

        // if (pass) {
        tempCompayProduct[index].totalNetPrice = parseFloat(
          (
            tempCompayProduct[index].quantity *
            tempCompayProduct[index].unitPrice -
            tempCompayProduct[index].totalDiscount
          ).toFixed(2)
        );
        submitAddToCart(tempCompayProduct[index])
          .then(() => { })
          .catch(() => { });
        // }
        // else {
        //   tempCompayProduct[index].quantity =
        //     tempCompayProduct[index].quantity + 1;
        // }

        handlePromotionView(res);
        setDisplayMap(res);
        setPercent(100);
        setLoading(false);
      });
    }
  };

  const handlePassId = async () => {
    setIsCheckoutButtonLoading(true);
    const checkedItems = Array.from(displayMap.entries()).reduce(
      (accumulator: string[], [, products]: [string, any[]]) => {
        products.forEach((product: any) => {
          if (product && product.checked) {
            accumulator.push(product.id);
          }
        });
        return accumulator;
      },
      []
    );

    // before check out calculation one time again.
    await Array.from(displayMap.entries()).reduce(
      async (accum, [, products]: [string, any[]]) => {
        await accum;
        for (let i = 0; i < products.length; i++) {
          if (products[i].checked && products[i].type !== "FOC") {
            await submitAddToCart(products[i]);
          }
        }
        return accum;
      },
      Promise.resolve()
    );

    const configureSettingIsOn: any = await getConfigureSetting(retailerAccess?.companyId ?? "")

    if (configureSettingIsOn && Object.keys(configureSettingIsOn).length) {
      ModalInfoUI({
        title: t("Notice"),
        content:
          <div
            dangerouslySetInnerHTML={{ __html: configureSettingIsOn.value }}
          />
        ,
        okText: t("Common.ok"),
        cancelText: t("Common.cancel"),
        onOk: () => { setIsCheckoutButtonLoading(false) },
        onCancel: () => { setIsCheckoutButtonLoading(false) },
      })

    } else if (checkedItems.length > 0) {
      setIsCheckoutButtonLoading(false);
      router.push(`/checkout?id=${checkedItems}`);
    }
  };

  // const handleAHSCHecking = async () => {
  //   try {
  //     let productsData: {
  //       productId: string;
  //       productName: string;
  //       productQuantity: number;
  //     }[] = [];
  //     let productIds: string[] = [];
  //     const checkedItems = Array.from(displayMap.entries()).reduce(
  //       (accumulator: string[], [, products]: [string, any[]]) => {
  //         products.forEach((product: any) => {
  //           if (product && product.checked && product.type !== "FOC") {
  //             accumulator.push(product.productId);
  //             productIds.push(product.productId);
  //             productsData.push({
  //               productId: product.productId,
  //               productName: product.name,
  //               productQuantity: product.quantity,
  //             });
  //           }
  //         });
  //         return accumulator;
  //       },
  //       []
  //     );

  //     if (checkedItems.length > 0) {
  //       setIsCheckoutButtonLoading(true);
  //       const temp = await getCompanyAverageSales(productIds);
  //       if (temp) {
  //         const updatedSalesData: any = temp.map((item: any) => ({
  //           productId: item.productId,
  //           productName: productsData.find(
  //             (val) => val.productId === item.productId
  //           )?.productName, // Use the product name from each item
  //           quantity: productsData.find(
  //             (val) => val.productId === item.productId
  //           )?.productQuantity, // Use the product quantity from each item
  //           promoSales: item.promoSales,
  //           nonPromoSales: item.nonPromoSales,
  //         }));
  //         setCompanyAverageSales(updatedSalesData);
  //         // setIsAHSVisible(true);
  //       } else {
  //         handlePassId();
  //       }
  //       // setIsAHSVisible(true); // Show the modal after setting the data
  //     }
  //   } catch (error) {
  //     console.error("Error in handlePassId:", error);
  //     // Handle error here
  //   }
  // };

  // const getCompanyAverageSales = async (
  //   productId: string[]
  //   // productName: string,
  //   // productQuantity: number
  // ) => {
  //   try {
  //     const param = {
  //       companyId: retailerAccess.companyId,
  //       productId: productId,
  //     };
  //     const dataSource = new DataSource(
  //       "invoice/companyAverageSales",
  //       encodeParams(param),
  //       false,
  //     );
  //     const res: any = await dataSource.load();

  //     if (res !== null) {
  //       if (res && res.length > 0) {
  //         // const updatedSalesData = res.map((item: any) => ({
  //         //   productId: item.productId,
  //         //   productName: productName, // Use the product name from each item
  //         //   quantity: productQuantity, // Use the product quantity from each item
  //         //   promoSales: item.promoSales,
  //         //   nonPromoSales: item.nonPromoSales,
  //         // }));
  //         // // Merge the new data with the existing companyAverageSales data
  //         // setCompanyAverageSales(updatedSalesData);
  //         return res;
  //       } else return;
  //     }
  //   } catch (error) {
  //     console.error("Error fetching sales data:", error);
  //     return null;
  //   }
  // };

  const getCompanyAverageSales = async (
    productId: string[] = []
  ): Promise<any[]> => {
    let result: any = [];
    try {
      if (!productId.length) return [];

      while (productId.length) {
        const params = {
          companyId: retailerAccess.companyId,
          productId: productId.splice(0, 50),
        };
        const dataSource = new DataSource(
          "invoice/companyAverageSales",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load();
        if (res?.length) {
          result = result.concat(res);
        }
      }

      return result || [];
    } catch (err) {
      return result || [];
    }
  };

  // Update the compareProductQuantity function to use the ProductExceedQuantity type
  const compareProductQuantity = () => {
    if (AHSReset) {
      setSalesData([]);
      setAHSReset(false);
    }
    try {
      const exceededProducts: ProductExceedQuantity[] = []; // Array to store exceeded products

      // Iterate through each product in the companyAverageSales
      companyAverageSales.forEach((companyAverageSale) => {
        {
          // Calculate the promo quantity as a percentage if promoSales is not 0
          let promoQuantityPercentage = 0;
          if (companyAverageSale.promoSales !== 0) {
            promoQuantityPercentage =
              ((companyAverageSale.quantity ?? 0) /
                (companyAverageSale.promoSales ?? 0)) *
              100;
          }
          // Calculate the non Promo quantity as a percentage if nonPromoSales is not 0
          let nonPromoQuantityPercentage = 0;

          if (companyAverageSale.nonPromoSales !== 0) {
            nonPromoQuantityPercentage =
              ((companyAverageSale.quantity ?? 0) /
                (companyAverageSale.nonPromoSales ?? 0)) *
              100;
          }

          // Check if the compare quantities exceed their respective percentages
          if (
            promoQuantityPercentage > exceededProductsPercentage ||
            nonPromoQuantityPercentage > exceededProductsPercentage
          ) {
            // Push the exceeded product into the array along with exceeded percentage
            exceededProducts.push({
              productName: companyAverageSale.productName,
              promoSalesPercentage: promoQuantityPercentage,
              nonPromoSalesPercentage: nonPromoQuantityPercentage,
            });
          }
        }
      });
      if (exceededProducts.length > 0) {
        setIsAHSVisible(true);
      } else {
        handlePassId();
      }
      // Update state with the filtered data
      setSalesData((prevSalesData) => [...prevSalesData, ...exceededProducts]);
    } catch (error) {
      console.error("Error in compareProductQuantity:", error);
      // Handle error here
      return [];
    }
  };

  // AHS Modal Pop Out
  const showAHSContent = () => {
    return (
      <div>
        <TableUI
          // className="ml-3 mr-3 mb-3"
          columns={AHSTableColumns}
          dataSource={[...salesData]}
        />
        <div className="flex justify-end space-x-3 pt-8">
          <SecondaryButtonUI
            label={t("Common.cancel")}
            onClick={() => {
              setIsAHSVisible(false);
            }}
          />

          <PrimaryButtonUI
            htmlType="submit"
            label={t("Cart.ConfirmCheckout")}
            onClick={() => {
              setIsAHSVisible(false);
              handlePassId();
            }}
          />
        </div>
      </div>
    );
  };

  const AHSTableColumns = [
    {
      title: t("Cart.TableColumns.ProductName"),
      dataIndex: "productName",
      key: "productName",
      width: "80%",
      render: (text: string) => (
        <p className="capitalize tableRowNameDesign">{text.toLowerCase()}</p>
      ),
      sorter: (a: any, b: any) => a.productName.localeCompare(b.productName),
      sortDirections: ["descend", "ascend"],
    },
    {
      title: t("Cart.TableColumns.PercentageExceeded") + " (%)",
      dataIndex: "promoSalesPercentage",
      key: "promoSalesPercentage",
      render: (text: string | null | undefined, record: any) => (
        <p className="text-center tableRowNameDesign">
          {text
            ? NumberThousandSeparator(Number(text))
            : NumberThousandSeparator(
              Number(record.nonPromoSalesPercentage || "0")
            )}
        </p>
      ),
    },
  ];

  const confirmDelete = (val: string) => {
    ModalConfirmUI({
      title: t("Common.confirmDeleteMessage"),
      content: t("Common.confirmDeleteDescription"),
      okText: t("Common.yes"),
      cancelText: t("Common.no"),
      onOk: () => {
        submitDelete(val); //get all the Organisation and exclude the selected one and update the data to status.
      },
      onCancel: () => { },
    });
  };

  const submitDelete = (value: any) => {
    apiHelper
      .DELETE("retailerCart?id=" + value.id, "", "", "v2")
      ?.then((res: any) => {
        MessageSuccessUI(t("Cart.removeSuccess"));
        setGroupByCompanyMap(new Map());
        setDisplayMap(new Map());
        getProductFromCart();
        getCartCount();
      })
      ?.catch(() => {
        MessageErrorUI(t("Cart.removeUnsuccess"));
      });
  };

  const submitAddToCart = (item: any) => {
    return new Promise((resolve, reject) => {
      apiHelper
        .PUT("retailerCart?id=" + item.id, item, "", "v2")
        ?.then(() => {
          // MessageSuccessUI(t("Cart.addToCart") + " " + t("Common.successful"));
          // setGroupByCompanyMap(new Map());
          // setDisplayMap(new Map());
          // getProductFromCart();
          resolve(true);
        })
        ?.catch((err) => {
          MessageErrorUI(
            t("Cart.addToCart") + " " + t("Common.updateUnsuccess")
          );
          reject(err);
        });
    });
  };

  const showPromotionUI = () => {
    const manualApplyPromotions = manualApplyPromotionList.filter((promotion) =>
      doneApplyRowSelectionKey.includes(promotion.id)
    );

    const promotionNeedToShow = removeDuplicateArray(
      promotions.concat(manualApplyPromotions),
      "id"
    );

    if (!promotions.length && !manualApplyPromotions?.length) return null;

    return (
      <>
        <span className="labelTextStyle text-[16px] pb-2">
          {t("SalesOrder.promotion")}
        </span>
        <List
          grid={{
            gutter: 16,
            xs: 1,
            sm: 2,
            md: 3,
            lg: 3,
            xl: 3,
            xxl: 3,
          }}
          className="bg-lightPurple sm:mb-20 mb-3"
          dataSource={promotionNeedToShow}
          renderItem={(promotion: Promotion) => (
            <List.Item key={promotion.id}>
              {RenderPromotionCard(promotion, (event: any) => { }, {
                isShowArrow: false,
                isShowQuestion: false,
              })}
            </List.Item>
          )}
        />
      </>
    );
  };

  const showFoc = (item: any) => {
    return (
      <div className="flex justify-between bg-white border-b-2 border-lightGrey">
        <div className="flex xl:items-center xs:items-start xl:pt-0 xs:pt-[40px] p-[12px] ">
          <CheckboxInput
            className="items-center checkBoxDesign opacity-0"
            key={item.id}
            onChange={(e) =>
              handleCheck(item.id, e.target.checked, item.companyId)
            }
            checked={item.checked}
          />
        </div>

        <div className="flex flex-col w-full">
          <div className="w-full flex justify-between p-2 gap-x-4 pt-2">
            {/* Image */}
            <div className="h-[100px] w-[100px]">
              <img
                className="rounded-md object-contain h-[80px] w-[80px]"
                src={
                  item.picture
                    ? PUBLIC_BUCKET_URL + item.picture
                    : defaultImage.src
                }
                loading="lazy"
              ></img>
            </div>

            <div className="flex lg:flex-row xs:flex-col w-full justify-between pt-1">
              <div className="flex flex-col lg:w-2/3 xs:w-full">
                {/* Product Name */}
                <div className="gap-x-2 items-center">
                  {item.sellingType === "PREORDER" ? (
                    <PreOrder src={PreOrder.src} />
                  ) : null}
                  <p className="font-semibold textLabel capitalize">
                    {item.name.toLowerCase()}&nbsp;
                  </p>
                </div>

                {/* Product Code */}
                <p className="textDescription text-labelGray">
                  <span className="flex">
                    {/* <p className="font-bold pr-2">
                                    {t("Cart.productCode") + ": "}
                                  </p> */}
                    <p>{item.code}</p>
                  </span>
                </p>

                {/* Unit Price, MOQ and Tax Rate*/}
                <div className="flex sm:flex-row xs:flex-col justify-between">
                  <p className="cart-text-details">
                    <span className="flex">
                      <p className="pr-2 text-labelGray">
                        {t("Cart.unitPrice")}
                      </p>
                      <p className="font-semibold">
                        {" "}
                        RM {NumberThousandSeparator(item.unitPrice)}
                      </p>
                    </span>
                  </p>
                  <p className="cart-text-details">
                    <span className="flex">
                      <p className="pr-2 text-labelGray">
                        {t("Cart.moq") + ": "}
                      </p>
                      <div className="flex">
                        <p>{item.moq}</p>
                        &nbsp;
                        <p>{uomMap.get(item.productUOMId)?.name}</p>
                      </div>
                    </span>
                  </p>
                  <p className="flex cart-text-details">
                    <span className="flex">
                      <p className="pr-2 text-labelGray">
                        {t("Cart.taxRate") + ": "}
                      </p>
                      <p className="font-semibold ">
                        {NumberThousandSeparator(item.taxRate)}%
                      </p>
                    </span>
                  </p>
                </div>
              </div>

              <div className="flex lg:w-1/3 xs:w-full items-center justify-between">
                <div className="pt-5 mobile:block xs:hidden text-buttonOrange textDescription font-semibold">
                  <CounterComponent
                    uom={uomMap.get(item.productUOMId)?.name}
                    className="bg-buttonOrange text-white"
                    defaultValue={item.quantity}
                    disabled={true}
                    onCountChange={(val) => {
                      handleAddTotal(item, val);
                    }}
                  />
                </div>
                <div className="text-buttonOrange mobile:block xs:hidden text-[20px]">
                  <Col className="flex items-center flex-col pr-4">
                    <p className="text-buttonOrange textTitle w-full flex font-semibold justify-end ">
                      <span>{t("FREE")}</span>
                    </p>
                  </Col>
                </div>
              </div>
            </div>

            <DeleteFilled
              disabled={true}
              onClick={() => confirmDelete(item)}
              className="text-gray-500 cursor-pointer text-sm mobile:hidden xs:block"
            />
            <div
              className="flex border-l-2 text-gray-500 mobile:p-4 xs:p-1 cursor-pointer mobile:block xs:hidden"
              style={{ alignItems: "center" }} // Add inline style for vertical centering
              onClick={() => (item.type === "FOC" ? null : confirmDelete(item))}
            >
              <DeleteFilled className=" my-auto h-full" />
            </div>
          </div>

          <div className="flex flex-row justify-around w-full">
            <div className="text-buttonOrange xs:block mobile:hidden text-[20px] w-1/2 flex font-semibold">
              <Col>
                <p className="text-buttonOrange textTitle w-full flex font-semibold">
                  <span>{t("FREE")}</span>
                </p>
              </Col>
            </div>

            <div className="flex xs:block mobile:hidden w-1/2 text-buttonOrange textDescription font-semibold">
              <CounterComponent
                uom={uomMap.get(item.productUOMId)?.name}
                className="bg-buttonOrange text-white"
                defaultValue={item.quantity}
                onCountChange={(val) => {
                  handleAddTotal(item, val);
                }}
              />
            </div>
          </div>
        </div>
      </div>
    );
  };

  const showManualApplyPromotionUI = () => {
    return (
      <Row
        className="p-2 flex justify-between bg-white text-buttonPurple mb-10 bottom-0 cursor-pointer w-full"
        onClick={() => {
          getManualPromotion();
        }}
      >
        <Col className="ml-2">{t("Cart.applyPromotion")}</Col>
        <Col>
          <RightOutlined />
        </Col>
      </Row>
    );
  };

  const showManualPromotionListModal = () => {
    return (
      <ModalUI
        className="font-bold w-full"
        title={t("SalesOrder.promotionList")}
        visible={isManualApplyPromotionModalVisible}
        onCancel={() => {
          setIsManualApplyPromotionModalVisible(false);
          // clear selectRows
          setManualApplyRowSelectionKey([]);
        }}
        content={showManualPromotionListModalContent()}
        footer={showManualPromotionModalFooter()}
        maskClosable={false}
      />
    );
  };

  const showManualPromotionModalFooter = () => {
    return (
      <div className="flex gap-x-4">
        <SecondaryButtonUI
          label={t("Common.cancel")}
          className="w-full buttonStyle secondaryButtonBg"
          onClick={() => {
            setIsManualApplyPromotionModalVisible(false);
            // clear selectRows
            setManualApplyRowSelectionKey([]);
          }}
        />
        <PrimaryButtonUI
          htmlType="submit"
          label={t("Common.save")}
          className="w-full"
          onClick={() => {
            handlerPromotionApply();
            setIsManualApplyPromotionModalVisible(false);
          }}
        />
      </div>
    );
  };

  const showManualPromotionListModalContent = () => {
    return (
      <Table
        {...tableProps}
        columns={columns}
        dataSource={manualApplyPromotionList}
      />
    );
  };

  const showContent = () => {
    return (
      <div>
        {/* Select All Section */}
        <div className="pl-3 flex items-center bg-white rounded-md p-2">
          <CheckboxInput
            className="font-bold checkBoxDesign"
            checked={checkAll}
            onChange={(e) => handleSelectAll(e.target.checked)}
            indeterminate={indeterminate}
          />
          <p className="pl-3 textLabel">{t("Common.selectAll")}</p>
        </div>

        {Array.from(displayMap.values()).map((companyProducts, index) => {
          return (
            <div key={index} className="mt-4">
              {/* CheckBox and Image */}
              <div className="pl-3 flex bg-white p-2 border-b-2 border-gray-200">
                <CheckboxInput
                  className="font-semibold checkBoxDesign p-2"
                  checked={companyProducts[0].checkAll}
                  onChange={(e) =>
                    handleSelectAllCompanyProduct(
                      e.target.checked,
                      companyProducts[0].companyId
                    )
                  }
                  indeterminate={companyProducts[0].indeterminate}
                />
                <p className="pl-8 font-semibold textLabel">
                  {companyMap.get(companyProducts[0].companyId)?.name}
                </p>
              </div>

              {companyProducts.map((item: any) => {
                if (item.type === "FOC") return showFoc(item);

                return (
                  <div
                    key={item.id}
                    className="flex bg-white border-b-2 border-gray-200 p-3 hover:bg-slate-50 transition-colors"
                  >
                    {/* Checkbox */}
                    <div className="flex items-center pr-1">
                      <CheckboxInput
                        className="items-center checkBoxDesign"
                        onChange={(e) =>
                          handleCheck(item.id, e.target.checked, item.companyId)
                        }
                        checked={item.checked}
                      />
                    </div>

                    {/* Product Details */}
                    <div className="flex flex-1 gap-4 items-center">
                      <div className="h-[80px] w-[80px]">
                        <img
                          className="rounded-md object-contain h-full w-full"
                          src={
                            item.picture
                              ? PUBLIC_BUCKET_URL + item.picture
                              : defaultImage.src
                          }
                          loading="lazy"
                        />
                      </div>

                      <div className="flex flex-col flex-1">
                        <div className="flex items-center gap-2">
                          {item.sellingType === "PREORDER" && (
                            <PreOrder src={PreOrder.src} />
                          )}
                          <p className="font-semibold text-[16px] capitalize">
                            {item.name?.toLowerCase()}
                          </p>
                        </div>

                        <p className="textDescription text-labelGray">
                          {item.code}
                        </p>

                        <div className="flex justify-between items-start ">
                          <div className="sm:flex hidden flex-wrap items-center justify-between gap-4">
                            <div className="flex items-center whitespace-nowrap">
                              <span className="pr-2 text-labelGray">
                                {t("Cart.unitPrice")}
                              </span>
                              <span className="font-semibold">
                                RM {NumberThousandSeparator(item.unitPrice)}
                              </span>
                            </div>

                            <div className="flex items-center whitespace-nowrap">
                              <span className="pr-2 text-labelGray">
                                {t("Cart.moq") + ":"}
                              </span>
                              <span className="font-semibold">
                                {item.minimumOrder}{" "}
                                {uomMap.get(item.defaultUOMId)?.name}
                              </span>
                            </div>

                            <div className="flex items-center whitespace-nowrap">
                              <span className="pr-2 text-labelGray">
                                {t("Cart.taxRate") + ":"}
                              </span>
                              <span className="font-semibold">
                                {NumberThousandSeparator(item.taxRate)}%
                              </span>
                            </div>
                          </div>

                          <CounterComponent
                            uom={uomMap.get(item.productUOMId)?.name}
                            minimumDisable={
                              item.conversionNumber === item.quantity
                            }
                            className="bg-buttonOrange text-white"
                            defaultValue={item.quantity}
                            onCountChange={(val) => handleAddTotal(item, val)}
                            multiplication={item.minimumIncrement}
                          />

                          <div className="text-buttonOrange text-right">
                            <p className="textTitle font-semibold">
                              RM{" "}
                              {NumberThousandSeparator(
                                item.totalPrice - item.totalDiscount
                              )}
                            </p>
                            {item.totalDiscount !== 0 && (
                              <p className="line-through text-gray-500 text-sm">
                                RM {NumberThousandSeparator(item.totalPrice)}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    <DeleteFilled
                      onClick={() => confirmDelete(item)}
                      className="text-red-500 cursor-pointer text-sm pl-2"
                    />
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
    );
  };

  const showLoading = () => (
    <Modal className="mt-24" closable={false} open={loading} footer={null}>
      <div className="h-160px flex justify-center items-center flex-col">
        <p className="font-bold text-xl mb-4">Loading...</p>
        <Progress type="circle" percent={percent} />
      </div>
    </Modal>
  );

  return (
    <div className="flex flex-col w-full min-h-screen bg-bgOrange">
      <Header items={headerItems} hasSearch={true} values={() => { }} />
      <Content className="flex flex-col mt-3 w-full sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2">
        <BackButtonUI title={t("Cart.myCart")} buttons={[]} />
        {showContent()}
        {showManualApplyPromotionUI()}
        {showPromotionUI()}
        {showLoading()}
        <ModalUI
          title={t("Cart.AHSConfirmation")}
          visible={isAHSVisible}
          onCancel={() => setIsAHSVisible(false)}
          width="70%"
          content={showAHSContent()}
        />
      </Content>
      {showManualPromotionListModal()}
      <FixedBottomBar
        quantityItem={totalItemChecked}
        totalPrice={totalPriceOfMyCart}
        savedPrice={totalDiscountOfMyCart}
        onclick={() => {

          // proceed to checkout page
          handlePassId();
        }}
        buttonDisabled={isCheckoutButton}
        buttonLoading={isCheckoutButtonLoading}
      />
      <div className="app-footer">
        <AppFooter retailerAccessValues={retailerAccess} />
      </div>
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default CartListing;
