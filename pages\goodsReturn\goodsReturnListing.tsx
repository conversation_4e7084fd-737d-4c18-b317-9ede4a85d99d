import React, { useEffect, useState } from "react";
import { Content } from "antd/lib/layout/layout";
import defaultImage from "../../assets/default/emptyImage.png";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import {
  Button,
  Col,
  Drawer,
  Form,
  MenuProps,
  Row,
  Tooltip,
  Upload,
  UploadFile,
  UploadProps,
} from "antd";
import Header, { supportedLocales } from "../../components/header";
import { FormTextInput, SingleDateInput } from "@/components/input";
import {
  BackButtonUI,
  PrimaryButtonUI,
  SecondaryButtonUI,
} from "@/components/buttonUI";
import { useRouter } from "next/router";
import { ArrowUpOutlined, EyeOutlined, PlusOutlined } from "@ant-design/icons";
import {
  ListingTableUI,
  MessageErrorUI,
  statusApproval,
} from "@/components/ui";
import {
  DataSource,
  PicSignedUrl,
  encodeParams,
  PUBLIC_BUCKET_URL,
  NumberThousandSeparator,
  capitalize,
  formateDateAndTime,
  getParamsFromLocalStorage,
  setFilterForm,
  setParamsFromLocalStorage,
  SignedUrl,
  cloneDeep,
} from "@/stores/utilize";
import {
  CompanyGeneralInfo,
  GoodsReturn,
  GoodsReturnProduct,
  Invoice,
  Outlet,
  Product,
  ProductUOM,
  Reason,
  Retailer,
  Staff,
  UOM,
} from "@/components/type";
import { ModalUI } from "@/components/modalUI";
import moment from "moment";
import FilterFormComponent from "@/components/filter";
import { statusFilterOption1 } from "@/components/config";
import useRetailerStore from "@/stores/store";
import { getRetailerData } from "@/stores/authContext";
import { ComponentFilterSelect } from "@/components/filterSelectInput";
import AppFooter from "@/components/footer";
import _ from "lodash";

function GoodsReturnListing() {
  const { t } = useTranslation("common");
  const router = useRouter();
  const [filterForm] = Form.useForm();
  const [filterModalForm] = Form.useForm();
  const [goodsReturnDetailForm] = Form.useForm();
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [cursor, setCursor] = useState("");
  const [tempCursor, setTempCursor] = useState("");
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({});

  const [data, setData] = useState<any[]>([]);
  const [companyMap, setCompanyMap] = useState(new Map());
  const [staffMap, setStaffMap] = useState(new Map());
  const [outletMap, setOutletMap] = useState(new Map());
  const [productMap, setProductMap] = useState(new Map());
  const [invoiceMap, setInvoiceMap] = useState(new Map());
  const [reasonMap, setReasonMap] = useState(new Map());
  const [uomMap, setUomMap] = useState(new Map());
  const [retailerMap, setRetailerMap] = useState(new Map());

  // const [isGoodsReturnDataReady, setIsGoodsReturnDataReady] = useState(false);
  // const [modalData, setModalData] = useState<GoodsReturn[]>([]);
  const [goodsReturnProductData, setGoodsReturnProductData] = useState<
    GoodsReturnProduct[]
  >([]);
  const [allFiles, setAllFiles] = useState<{ [key: string]: any }>({});
  // const [productNameOption, setProductNameOption] = useState<SelectOption[]>(
  //   []
  // );
  // const [productSkuOption, setProductSkuOption] = useState<SelectOption[]>([]);

  const [isModalOpen, setIsModalOpen] = useState(false);
  // const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRowData, setSelectedRowData] = useState<any[]>([]);
  const [pdfLoading, setPDFLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [showButtonLoader, setShowButtonLoader] = useState(false);
  // ======================================================================
  // filter function - state()
  // ======================================================================
  const [showClearFilter, setShowClearFilter] = useState(false);
  const [fuzzySearchFilter, setFuzzySearchFilter] = useState("");
  const [modalFilter, setModalFilter] = useState<any>({});
  const [filterSetting, setFilterSetting] = useState("");
  const [statusValue, setStatusValue] = useState("All");
  const [statusKey, setStatusKey] = useState("ALL");
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [fieldName, setFieldName] = useState("");
  const [recordStatus, setRecordStatus] = useState("");

  const headerItems = [
    {
      label: t("Header.dashboard"),
      route: "/profile/dashboard",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: t("Header.goodsReturn"),
      route: "/goodsReturn/goodsReturnListing",
      className: "labelTextStyle",
    },
  ];

  useEffect(() => {
    // Function to check screen size and update state
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth <= 950); // Define your breakpoint for small screens
    };

    // Add event listener for window resize
    window.addEventListener("resize", handleResize);

    // Call handleResize initially to set initial screen size
    handleResize();

    // Clean up the event listener on component unmount
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollButton(window.pageYOffset > 0);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    if (Object.keys(useRetailerStore.getState()).length) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }
    }
  }, [Object.keys(useRetailerStore.getState()).length]);

  useEffect(() => {
    if (retailerAccess && Object.keys(retailerAccess).length > 0) {
      // getGoodsReturns();
      // getProductData();

      // Get from localStorage
      const filterParams: any = getParamsFromLocalStorage(
        router.pathname,
        "goodsReturnFilter"
      );

      const filterKey: any = {
        fuzzySearch: "",
        goodsReturnNo: null,
        returnDate: null,
        createdDate: "",
        productName: null,
        productSku: null,
      };

      const clonedFilterKey = { ...filterKey };
      delete clonedFilterKey.fuzzySearch;

      Object.keys(clonedFilterKey).forEach((key) => {
        const capitalizedKey = key
          .split(/(?=[A-Z])|\s+/)
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
        clonedFilterKey[capitalizedKey] = clonedFilterKey[key];
        delete clonedFilterKey[key];
      });

      const keysAsString: string = Object.keys(clonedFilterKey).join(", ");
      setFieldName(keysAsString);

      if (filterParams) {
        // Initialize variables to store the values
        // Follow search params' key

        setFilterSetting(filterParams);
        setFilterForm(filterParams, filterKey);
        filterForm.setFieldValue(["fuzzySearch"], filterKey.fuzzySearch);
        filterModalForm.setFieldsValue(filterKey);
        setFuzzySearchFilter(filterKey.fuzzySearch);

        let data = {
          goodsReturnNo: filterKey.goodsReturnNo || "",
          returnDate: filterKey.returnDate
            ? moment(filterKey.returnDate)
            : filterKey.returnDate,
          createdDate: filterKey.createdDate
            ? moment(filterKey.createdDate)
            : filterKey.createdDate,
          productSku: filterKey.productSku || "",
          productName: filterKey.productName || "",
        };
        setModalFilter(data);
        setStatusKey(filterKey.status ?? "ALL");
        const filterStatusLabel: any = statusFilterOption1.find(
          (item: any) => item.key === filterKey.status
        )?.label;
        setStatusValue(filterStatusLabel ?? "All");
      } else {
        getGoodsReturns(true);
      }
    }
  }, [retailerAccess]);

  useEffect(() => {
    const data = {
      fuzzySearch: fuzzySearchFilter || "",
      goodsReturnNo: modalFilter.goodsReturnNo || "",
      returnDate: modalFilter.returnDate || "",
      createdDate: modalFilter.createdDate || "",
      productSku: modalFilter.productSku || "",
      productName: modalFilter.productName || "",
      status: statusKey === "ALL" ? "" : statusKey,
    };

    const allPropertiesEmpty = Object.values(data).every(
      (value) => value === ""
    );

    if (!allPropertiesEmpty) {
      searchGoodsReturn(data);
    } else {
      setFilterSetting("");
    }
  }, [fuzzySearchFilter, statusKey, modalFilter]);

  useEffect(() => {
    // Check params whether is same
    const filterParams = getParamsFromLocalStorage(
      router.pathname,
      "goodsReturnFilter"
    );
    if (filterSetting) {
      getGoodsReturns(true, false);

      if (filterSetting !== filterParams) {
        setParamsFromLocalStorage(
          router.pathname,
          filterSetting,
          "goodsReturnFilter"
        );
      }
      setShowClearFilter(true);
    } else {
      setShowClearFilter(false);
      if (data.length > 0) {
        localStorage.removeItem("goodsReturnFilter");
      }
      getGoodsReturns(true, false);
    }
  }, [filterSetting]);

  // *************************************************************************************
  // *** Scolling Function - useEffect ***
  // *************************************************************************************
  // Check scrolling position
  useEffect(() => {
    if (cursor === "") return;
    const handleScroll = () => {
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight - 50;
      const scrollPosition = window.scrollY;
      if (windowHeight + scrollPosition >= documentHeight) {
        // Stop API calling when cursor is equal to '0'
        if (cursor !== "0") {
          getGoodsReturns();
        }
      }
    };
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [cursor]);

  const handleScrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  let isLoading = false;

  const getGoodsReturns = (isRefresh = false, isClearFilter = false) => {
    // const accessBranches = otherViewBranchAccess === false ? retailerAccess.companyBranchIds : otherCompanyBranchesIds;
    if (isLoading) return; // Skip if a request is already in progress
    setTableLoading(true);
    setShowButtonLoader(true);
    let currentOutletId = localStorage.getItem("currentOutletId");
    isLoading = true;
    setTimeout(() => {
      let params: any = {
        sort: "createdAt",
        sortOrder: "-1",
        companyId: retailerAccess.companyId,
        // companyBranchId: retailerAccess.companyBranchId,
        outletId: currentOutletId,
      };

      if (isRefresh === false) {
        params.cursor = cursor;
      }

      // if (!isAdmin) {
      //   params.companyBranchIds = accessBranches;
      // }
      // const checkAdminRights = isAdmin ? params : params + "&companyId=" + retailerAccess.companyId;
      const checkFilterRights =
        filterSetting && !isClearFilter
          ? filterSetting +
          (cursor && cursor !== "0" ? `&cursor=${cursor}` : "")
          : encodeParams(params);
      const dataSource = new DataSource(
        "goodsReturns",
        checkFilterRights,
        false
      );

      // !isAdmin && !otherViewBranchAccess ? filterForm.setFieldValue("companyId", retailerAccess.companyId) : null;

      // if ((accessBranches && accessBranches.length > 0) || isAdmin) {
      dataSource
        .load()
        .then(async (res: any) => {
          if (res && res.items !== null) {
            let data = res.items;
            const objectMap = data.reduce(
              (accumulator: any, current: GoodsReturn) => {
                accumulator["companyId"] = accumulator["companyId"] || [];
                if (
                  current.companyId &&
                  !companyMap.has(current.companyId) &&
                  !accumulator["companyId"].includes(current.companyId)
                ) {
                  accumulator["companyId"].push(current.companyId ?? "");
                }

                accumulator["outletId"] = accumulator["outletId"] || [];
                if (
                  current.outletId &&
                  !outletMap.has(current.outletId) &&
                  !accumulator["outletId"].includes(current.outletId)
                ) {
                  accumulator["outletId"].push(current.outletId ?? "");
                }

                accumulator["staffId"] = accumulator["staffId"] || [];
                if (
                  current.staffId &&
                  !staffMap.has(current.staffId) &&
                  !accumulator["staffId"].includes(current.staffId)
                ) {
                  accumulator["staffId"].push(current.staffId ?? "");
                }

                accumulator["retailerId"] = accumulator["retailerId"] || [];
                if (
                  current.retailerId &&
                  !retailerMap.has(current.retailerId) &&
                  !accumulator["retailerId"].includes(current.retailerId)
                ) {
                  accumulator["retailerId"].push(current.retailerId ?? "");
                }

                current.products?.reduce(
                  (acc: any, product: GoodsReturnProduct) => {
                    accumulator["productId"] = accumulator["productId"] || [];
                    if (
                      product.productId &&
                      !productMap.has(product.productId) &&
                      !accumulator["productId"].includes(product.productId)
                    ) {
                      accumulator["productId"].push(product.productId ?? "");
                    }

                    accumulator["invoiceId"] = accumulator["invoiceId"] || [];
                    if (
                      product.invoiceId &&
                      !invoiceMap.has(product.invoiceId) &&
                      !accumulator["invoiceId"].includes(product.invoiceId)
                    ) {
                      accumulator["invoiceId"].push(product.invoiceId ?? "");
                    }

                    accumulator["reasonId"] = accumulator["reasonId"] || [];
                    if (
                      product.reasonId &&
                      !reasonMap.has(product.reasonId) &&
                      !accumulator["reasonId"].includes(product.reasonId)
                    ) {
                      accumulator["reasonId"].push(product.reasonId ?? "");
                    }

                    accumulator["productUOMId"] =
                      accumulator["productUOMId"] || [];
                    if (
                      product.productUOMId &&
                      !uomMap.has(product.productUOMId) &&
                      !accumulator["productUOMId"].includes(
                        product.productUOMId
                      )
                    ) {
                      accumulator["productUOMId"].push(
                        product.productUOMId ?? ""
                      );
                    }

                    return acc;
                  },
                  {}
                );

                return accumulator;
              },
              {}
            );

            getCompany(objectMap["companyId"]);
            getOutlets(objectMap["outletId"]);
            getProduct(objectMap["productId"]);
            getInvoice(objectMap["invoiceId"]);
            getReason(objectMap["reasonId"]);
            getUOM(objectMap["productUOMId"]);
            const staff = await getStaffs(objectMap["staffId"]);
            const retailer = await getRetailer(objectMap["retailerId"]);

            // create new key for display name and applicant typex
            data.map((item: GoodsReturn) => {
              if (item.staffId && item.staffId !== "") {
                item.applicantNameNType = `Staff | ${staff.get(item.staffId)?.lastName ?? ""
                  } ${staff.get(item.staffId)?.firstName ?? ""}`;
              }

              if (item.retailerId && item.retailerId !== "") {
                item.applicantNameNType = `Retailer | ${retailer.get(item.retailerId)?.lastName ?? ""
                  } ${retailer.get(item.retailerId)?.firstName ?? ""}`;
              }

              return item;
            });

            // getCompanyBranch(objectMap["companyBranchId"]);
            const nextCursor = res.cursor; // Get the cursor from the last item in the response
            if (nextCursor !== cursor || isRefresh) {
              // Avoid duplicates
              if (!isRefresh) {
                // setFullData((prevData) => [...prevData, ...data]);
                setData((prevData) => [...prevData, ...data]);
                setTableLoading(false);
                setShowButtonLoader(false); //path to check
              } else {
                // setFullData([...data]);
                setData([...data]);
                setTableLoading(false);
                setShowButtonLoader(false); //path to check
              }
              // cursor = nextCursor;
              setCursor(nextCursor);
              setTempCursor(nextCursor);
            }
          }
          isLoading = false;
        })
        .catch(() => {
          isLoading = false;
          //* This Part need re-edit*//
        });
      // } else {
      //   setGoodsReturnListingInfoData([]);
      // }
    }, 500);
  };

  const getCompany = async (id: string[] = []) => {
    const dataSource = new DataSource(
      "companies",
      encodeParams({ id: id }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setCompanyMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: CompanyGeneralInfo) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getOutlets = async (id: string[] = []) => {
    let tempProductMap = new Map(outletMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "outlets",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });
        if (res !== null && res.items.length > 0) {
          setOutletMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Outlet) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Outlet) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getStaffs = async (id: string[] = []) => {
    let tempProductMap = new Map(staffMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "staffs",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setStaffMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Staff) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Staff) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getProduct = async (id: string[] = []) => {
    let tempProductMap = new Map(productMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "productCatalogues",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setProductMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Product) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Product) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getInvoice = async (id: string[] = []) => {
    let tempProductMap = new Map(invoiceMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      // status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "invoices",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setInvoiceMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Invoice) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            // console.log("newDataMap: ", newDataMap);
            return newDataMap;
          });
          res.items?.map((item: Invoice) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getUOM = async (id: string[] = []) => {
    let tempProductMap = new Map(uomMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource("uoms", encodeParams(params), false);
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setUomMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: UOM) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: UOM) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getReason = async (id: string[] = []) => {
    let tempProductMap = new Map(reasonMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "reasons",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setReasonMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Reason) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Reason) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getRetailer = async (id: string[] = []) => {
    let tempRetailerMap = new Map(retailerMap);
    if (!id?.length) return tempRetailerMap;

    const params: any = {
      status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource(
          "retailers",
          encodeParams(params),
          false,
          "v2"
        );
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setRetailerMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Retailer) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Retailer) => {
            tempRetailerMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempRetailerMap;
    }
    return tempRetailerMap;
  };

  // const getProductData = async () => {
  //   const dataSource = new DataSource(
  //     "productCatalogues",
  //     "status=ACTIVE",
  //     true
  //   );
  //   const res: any = await dataSource.load();

  //   if (res !== null) {
  //     let nameList: SelectOption[] = [];
  //     let skuList: SelectOption[] = [];
  //     res.map((value: any) => {
  //       nameList.push({
  //         value: value.id,
  //         label: value.name,
  //       });
  //       skuList.push({
  //         value: value.id,
  //         label: value.sku,
  //       });
  //     });
  //     setProductNameOption(nameList);
  //     setProductSkuOption(skuList);
  //   }
  // };

  const props: UploadProps = {
    name: "file",
    multiple: false,
    maxCount: 1,
    showUploadList: {
      showPreviewIcon: false,
    },
    beforeUpload: (file) => {
      if (
        file.type !== "image/png" &&
        file.type !== "image/jpg" &&
        file.type !== "image/jpeg" &&
        file.type !== "application/pdf"
      ) {
        MessageErrorUI(
          `${file.name} is an invalid file format. Please change the file extension to either .pdf, .png, .jpg, .jpeg.`
        );
        return Upload.LIST_IGNORE;
      } else if (file.size > 5242880) {
        MessageErrorUI(
          `${file.name} is too large. Please upload another document that is smaller than 5MB.`
        );
        return Upload.LIST_IGNORE;
      } else {
        return false;
      }
    },
  };

  const showModal = () => {
    return (
      <div className="w-full">
        <Col>
          <Form
            className="w-full pt-3.5"
            form={goodsReturnDetailForm}
            layout="vertical"
            scrollToFirstError
          >
            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="goodsReturnNo"
                className="flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("GoodsReturn.goodsReturn") + " " + t("Common.no")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={
                    t("Common.eg.") + " " + t("Placeholder.OutletName")
                  }
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="returnDate"
                className="flex-1"
                // rules={[
                //   {
                //     required: true,
                //     message: t("ssm") + " " + t("Validation.requiredField"),
                //   },
                // ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("GoodsReturn.returnDate")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.eg.") + " " + t("Placeholder.Email")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>

            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="returnMode"
                className="flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("GoodsReturn.returnMode")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Common.eg") + " " + t("GoodsReturn.unitNo")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="status"
                className="flex-1"
                // rules={[{ required: true, mess age: t("GPSLocation") + " " + t("Validation.Is.Required") }]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.status")}
                  </p>
                }
              >
                <FormTextInput disabled placeholder={""} maxLength={100} />
              </Form.Item>
            </Row>
            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="returnType"
                className="flex-1"
                // rules={[
                //   {
                //     required: true,
                //     message: t("Address1") + " " + t("Validation.Is.Required"),
                //   },
                // ]}
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("GoodsReturn.returnType")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.11")}
                  maxLength={100}
                />
              </Form.Item>
              <Form.Item
                name="totalAmount"
                className="flex-1"
                /*rules={[{ required: true, message: t("Outlet.Error.18") }]}*/ label={
                  <p className="text-neutral700 text-[12px]">
                    {t("GoodsReturn.totalAmount")}
                  </p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.12")}
                  maxLength={100}
                />
              </Form.Item>
            </Row>
            <Row className="flex md:flex-row flex-col gap-x-4">
              <Form.Item
                name="remark"
                className="flex-1"
                // rules={[
                //   {
                //     required: true,
                //     message: t("City") + " " + t("Validation.Is.Required"),
                //   },
                // ]}
                label={
                  <p className="text-neutral700 text-[12px]">{t("Remark")}</p>
                }
              >
                <FormTextInput
                  disabled
                  placeholder={t("Outlet.Placeholder.15")}
                  maxLength={100}
                />
                {/* disabled={cityList.length !== 0 ? false : true} /> */}
              </Form.Item>
            </Row>
            {recordStatus === "REJECTED" ? (
              <Row className="flex md:flex-row flex-col gap-x-4">
                <Form.Item
                  name="rejectRemark"
                  className="flex-1"
                  label={
                    <p className="text-neutral700 text-[12px]">
                      {t("SalesOrder.rejectRemark")}
                    </p>
                  }
                >
                  <FormTextInput
                    disabled
                    placeholder={t("Outlet.Placeholder.15")}
                    maxLength={100}
                  />
                </Form.Item>
              </Row>
            ) : null}
            <Row className="w-full">
              <Form.Item
                className="flex-1 p-2"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("CreditNote.supportedDocument")}
                  </p>
                }
              >
                <Upload
                  {...props}
                  disabled
                  onPreview={(val: any) => {
                    // only uploaded photo can download and show
                    // new upload wont be able to click
                    if (val.url != undefined) {
                      let link = document.createElement("a");
                      link.target = "_blank";
                      link.href = val.url;
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    }
                  }}
                  onChange={(info) => {
                    if (info.fileList.length > 0) {
                      if (
                        info.fileList.at(-1) !== undefined &&
                        info.file.status !== "removed"
                      ) {
                        let file = info.fileList.at(-1);
                        if (file !== undefined) {
                          file.status = "done";
                          let fileObj = file.originFileObj;
                          setAllFiles({
                            ...allFiles,
                            document: fileObj,
                          });
                        }
                      }
                    }
                  }}
                  onRemove={() => {
                    setAllFiles({
                      ...allFiles,
                      ["document"]: null,
                    });
                  }}
                  fileList={
                    allFiles["document"] === undefined ||
                      allFiles["document"] === null
                      ? []
                      : [allFiles["document"]]
                  }
                >
                  <div className="flex items-center gap-x-4 p-2 text-buttonPurple bg-lightPurple font-semibold">
                    <PlusOutlined />
                    <p>{t("CreditNote.supportedDocument")}</p>
                  </div>
                </Upload>
              </Form.Item>
            </Row>
            <span className="m-1"></span>
            <ListingTableUI
              // EditableCell={EditableCell}
              bordered
              dataSource={goodsReturnProductData}
              columns={productColumn}
              // rowClassName="editable-row"
              rowKey={(record: any) => record.id}
              cursor={false}
              // loader={showButtonLoader}
              pagination={false}
              endMessage={""}
            />
          </Form>
        </Col>
      </div>
    );
  };

  const productColumn = [
    {
      title: t("GoodsReturn.product"),
      dataIndex: "productId",
      sorter: (a: any, b: any) => a.productId.localeCompare(b.productId),
      showSorterTooltip: false,
      key: "id",
      render: (_: any, record: GoodsReturnProduct) => {
        const item = productMap.get(record.productId);
        return (
          <div>
            <Col className="flex items-center w-full">
              <img
                className="object-contain h-[80px] min-w-[80px] p-2"
                src={
                  item.productUOM.find(
                    (item: ProductUOM) =>
                      record.productUOMId === item.productUOMId
                  )?.pictures
                    ? PUBLIC_BUCKET_URL +
                    item.productUOM.find(
                      (item: ProductUOM) =>
                        record.productUOMId === item.productUOMId
                    )?.pictures[1]
                    : defaultImage.src
                }
                loading="lazy"
              ></img>
              <div className="flex flex-col w-full">
                <p className="font-bold text-[14px]">{item.name}&nbsp;</p>
                <p className="text-gray-500 text-[10px] w-full flex ">
                  <span>
                    {t("GoodsReturn.productCode")}: {item.sku}
                  </span>
                </p>
                <p className="text-gray-500 text-[10px] w-full flex ">
                  <span>
                    {t("GoodsReturn.uom")}:{" "}
                    {uomMap.get(record.productUOMId)?.name}
                  </span>
                </p>
                <p className="text-gray-500 text-[10px] w-full flex ">
                  <span>
                    {t("GoodsReturn.unitPrice")}: {record.price}
                  </span>
                </p>
                <p className="text-gray-500 text-[10px] w-full flex ">
                  <span>
                    {t("TaxRate")}: {record.taxRate}%
                  </span>
                </p>
              </div>
            </Col>
          </div>
        );
      },
    },
    {
      title: t("GoodsReturn.invoice") + " " + t("Common.no"),
      dataIndex: "invoiceId",
      sorter: (a: any, b: any) => a.invoiceId.localeCompare(b.invoiceId),
      showSorterTooltip: false,
      key: "invoiceId",
      render: (_: any, record: GoodsReturnProduct) => {
        return (
          <p className="tableRowNameDesign">
            {invoiceMap.get(record.invoiceId)?.invoiceNo}
          </p>
        );
      },
    },
    {
      title: t("GoodsReturn.invoice") + " " + t("GoodsReturn.quantity"),
      dataIndex: "invoiceId",
      //  sorter: (a: any, b: any) =>{
      //  const first= invoiceMap.get(a.invoiceId)?.invoiceProducts
      //  const second= invoiceMap.get(b.invoiceId)?.invoiceProducts
      //  },
      showSorterTooltip: false,
      key: "invoiceId",
      render: (_: any, record: GoodsReturnProduct) => {
        const invoiceProduct = invoiceMap.get(
          record.invoiceId
        )?.invoiceProducts;
        if (invoiceProduct) {
          const thisProduct = invoiceProduct.find((item: any) => {
            return item.productId === record.productId;
          });
          return <p className="tableRowNameDesign">{thisProduct.quantity}</p>;
        }
        return null;
      },
    },
    {
      title: t("GoodsReturn.returnQuantity"),
      dataIndex: "returnQuantity",
      sorter: (a: any, b: any) =>
        a.returnQuantity.localeCompare(b.returnQuantity),
      showSorterTooltip: false,
      key: "returnQuantity",
      render: (_: any, record: GoodsReturnProduct) => {
        return <p className="tableRowNameDesign">{record.returnQuantity}</p>;
      },
    },
    {
      title: t("GoodsReturn.goodsCondition"),
      dataIndex: "productCondition",
      sorter: (a: any, b: any) =>
        a.productCondition.localeCompare(b.productCondition),
      showSorterTooltip: false,
      key: "id",
      render: (_: any, record: GoodsReturnProduct) => {
        return <p className="tableRowNameDesign">{record.productCondition}</p>;
      },
    },
    {
      title: t("GoodsReturn.reason"),
      dataIndex: "reasonId",
      sorter: (a: any, b: any) =>
        a.goodsReturnNo.localeCompare(b.goodsReturnNo),
      showSorterTooltip: false,
      key: "reasonId",
      render: (_: any, record: GoodsReturnProduct) => {
        return (
          <p className="tableRowNameDesign">
            {reasonMap.get(record.reasonId)?.description}
          </p>
        );
      },
    },
  ];

  const column = [
    {
      width: 150,
      title: t("GoodsReturn.applicantName"),
      dataIndex: "applicantNameNType",
      // onFilter: (value: string, record: any) =>
      //   record.applicantNameNType.indexOf(value) === 0,
      sorter: (a: any, b: any) =>
        a?.applicantNameNType?.localeCompare(b?.applicantNameNType),
      showSorterTooltip: false,
      key: "applicantNameNType",
      render: (_: any, record: GoodsReturn) => {
        return (
          <p className="tableRowNameDesign">
            {record.applicantNameNType ? (
              record.applicantNameNType
            ) : (
              <p className="tableRowNameDesign text-center">—</p>
            )}{" "}
          </p>
        );
      },
    },
    {
      // Return note No
      title: t("GoodsReturn.goodsReturn") + " " + t("Common.no"),
      dataIndex: "goodsReturnNo",
      onFilter: (value: string, record: { goodsReturnNo: string }) =>
        record.goodsReturnNo.indexOf(value) === 0,
      sorter: (a: any, b: any) =>
        a.goodsReturnNo.localeCompare(b.goodsReturnNo),
      showSorterTooltip: false,
      key: "goodsReturnNo",
      width: 150,
      render: (_: any, record: GoodsReturn) => {
        return <p className="tableRowNameDesign">{record.goodsReturnNo}</p>;
      },
    },
    {
      // Return Date
      title: t("GoodsReturn.returnDate"),
      dataIndex: "returnDate",
      // onFilter: (value: string, record: { returnDate: string }) => record.returnDate.indexOf(value) === 0,
      sorter: (a: any, b: any) =>
        moment(a.returnDate).unix() - moment(b.returnDate).unix(),
      showSorterTooltip: false,
      key: "returnDate",
      width: 150,
      render: (_: any, record: GoodsReturn) => {
        let date = new Date(record.returnDate ?? "");
        let data =
          ("0" + date.getDate()).slice(-2) +
          "/" +
          ("0" + (date.getMonth() + 1)).slice(-2) +
          "/" +
          date.getFullYear();
        return <p className="tableRowNameDesign">{data}</p>;
      },
    },
    // {
    //   // Staff Code
    //   title: t("GoodsReturn.staff"),
    //   dataIndex: "staffName",
    //   onFilter: (value: string, record: { staffName: string }) =>
    //     record.staffName.indexOf(value) === 0,
    //   sorter: (a: any, b: any) => a?.staffName?.localeCompare(b.staffName),
    //   showSorterTooltip: false,
    //   key: "staffName",
    //   width: 200,
    //   render: (_: any, record: GoodsReturn) => {
    //     return (
    //       <p className="tableRowNameDesign">
    //         {staffMap.get(record.staffId)?.firstName +
    //           " " +
    //           staffMap.get(record.staffId)?.lastName}
    //       </p>
    //     );
    //   },
    // },
    // {
    //   // Outlet Code
    //   title: t("OutletCode"),
    //   dataIndex: "outletCode",
    //   onFilter: (value: string, record: { outletCode: string }) =>
    //     record.outletCode.indexOf(value) === 0,
    //   sorter: (a: any, b: any) => a.outletCode.localeCompare(b.outletCode),
    //   showSorterTooltip: false,
    //   key: "outletCode",
    //   width: 150,
    //   render: (_: any, record: GoodsReturn) => {
    //     return (
    //       <p className="tableRowNameDesign">
    //         {outletMap.get(record.outletId)?.outletCode}
    //       </p>
    //     );
    //   },
    // },
    // {
    //   // Outlet Name
    //   title: t("OutletName"),
    //   dataIndex: "outletName",
    //   onFilter: (value: string, record: { outletName: string }) =>
    //     record.outletName.indexOf(value) === 0,
    //   sorter: (a: any, b: any) => a.outletName.localeCompare(b.outletName),
    //   showSorterTooltip: false,
    //   key: "outletName",
    //   width: 300,
    //   render: (_: any, record: GoodsReturn) => {
    //     return (
    //       <p className="tableRowNameDesign">
    //         {outletMap.get(record.outletId)?.name}
    //       </p>
    //     );
    //   },
    // },
    {
      title: t("Common.status"),
      dataIndex: "status",
      key: "status",
      onFilter: (value: string, record: any) =>
        record.status.indexOf(value) === 0,
      sorter: (a: any, b: any) => a.status.localeCompare(b.status),
      showSorterTooltip: false,
      width: 100,
      render: (_: any, record: any) => {
        return statusApproval(record);
      },
    },
    // {
    //   title: t("Common.approvalStatus"),
    //   dataIndex: "approvalStatus",
    //   key: "approvalStatus",
    //   onFilter: (value: string, record: any) =>
    //     record.approvalStatus.indexOf(value) === 0,
    //   sorter: (a: any, b: any) =>
    //     a.approvalStatus?.localeCompare(b.approvalStatus),
    //   showSorterTooltip: false,
    //   width: 100,
    //   render: (_: any, record: any) => {
    //     return statusApproval(record);
    //   },
    // },
    {
      // Action
      title: t("Common.action"),
      dataIndex: "action",
      key: "action",
      fixed: "right",
      width: 100,
      render: (_: any, record: GoodsReturn) => {
        const product = record.products;
        let totals = 0;

        if (product) {
          product.map((item: GoodsReturnProduct) => {
            if (item) {
              const subtotal = (item.returnQuantity ?? 0) * (item?.price ?? 0);
              const taxAmount = subtotal * (item.taxRate ?? 0);
              const total = subtotal - (item.discount ?? 0) - taxAmount;
              totals = totals + total;
            }
          });
        }

        // const containsRorC = /[RU]/.test(userAccess?.policies?.["goodsReturn"] || "");
        // if ((containsRorC || isAdmin || isCompanyAdmin) && record.status !== "UNVERIFIED") {
        return (
          <div className="flex">
            <Button
              type="link"
              onClick={async () => {
                setIsModalOpen(true);
                // setModalData([record]);
                setGoodsReturnProductData(record.products ?? []);

                let files = cloneDeep(allFiles);

                let hashmap: { [key: string]: UploadFile } = {};
                let doDocumentList: string | string[] | undefined =
                  record.goodsReturnDocuments?.length &&
                    record.goodsReturnDocuments.length > 0
                    ? record.goodsReturnDocuments[0]
                    : record.goodsReturnDocuments;

                if (typeof doDocumentList === "string") {
                  let docs: { [key: string]: any } = {};
                  await SignedUrl(doDocumentList).then((res: any) => {
                    //split and get the file name only from API value.
                    let fileName = doDocumentList.split("/");
                    let preview: UploadFile = {
                      uid: doDocumentList,
                      name: fileName.at(-1)!,
                      url: res,
                      fileName: fileName.at(-1),
                    };
                    goodsReturnDetailForm.setFieldValue("document", preview);
                    docs.doDocument = preview;
                  });
                  setAllFiles(docs);
                } else if (Array.isArray(doDocumentList)) {
                  doDocumentList.forEach((doc) => {
                    SignedUrl(doc).then((res: any) => {
                      // Split and get the file name from the URL if it's a string
                      let fileName = doc.split("/");

                      let preview: UploadFile = {
                        uid: "0",
                        name: fileName.at(-1) ?? "",
                        url: res,
                        fileName: fileName.at(-1) ?? "",
                      };

                      // Add to hashmap or set form field values as needed
                      hashmap["document"] = preview;
                      goodsReturnDetailForm.setFieldValue("document", preview);

                      // Update the file list state
                      files = { ...files, ...hashmap };
                      setAllFiles(files);
                    });
                  });
                }

                goodsReturnDetailForm.setFieldsValue({
                  goodsReturnNo: record.goodsReturnNo,
                  returnDate: formateDateAndTime(record.returnDate),
                  returnMode: capitalize(record.returnMode),
                  status: capitalize(record.status),
                  returnType: record.products
                    ? capitalize(record.products[0].returnType)
                    : "",
                  totalAmount: NumberThousandSeparator(totals),
                  remark: record.remark,
                  rejectRemark: record.rejectRemark,
                });

                setRecordStatus(record?.status ?? "");
              }}
              className="flex items-center  text-xs ml-0 p-2"
            >
              <Tooltip title={t("Common.viewMore")}>
                <EyeOutlined style={{ color: "green" }} />
              </Tooltip>
            </Button>
          </div>
        );
        // }
        // return null;
      },
    },
  ];

  const rowSelection = {
    onChange: (selectedRowKeys: string[], selectedRows: []) => {
      setSelectedRowData(selectedRows);
      // setSelectedRowKeys(selectedRowKeys);
    },

    // getCheckboxProps: (record: { disabled: any; status: any }) => ({
    //   disabled: record.status !== "ACTIVE",
    // }),
  };

  const generatePDF = () => {
    setPDFLoading(true);
    selectedRowData.forEach((item: GoodsReturn) => {
      const dataSource = new DataSource(
        "goodsReturn/pdf",
        encodeParams({ id: item.id }),
        false
      );
      dataSource
        .load()
        .then((res: any) => {
          PicSignedUrl(res)
            .then((picResult: any) => {
              const link = document.createElement("a");
              link.href = picResult;
              link.target = "_blank"; // Open the link in a new tab (optional)
              document.body.appendChild(link);
              link.click();
              // Cleanup: Remove the link from the DOM
              document.body.removeChild(link);
            })
            .catch(() => {
              setPDFLoading(false);
            });
        })
        .catch(() => {
          setPDFLoading(false);
        });
      // }
    });

    setPDFLoading(false);
  };

  const handleFirstButtonClick = () => {
    router.push("/goodsReturn/partialGoodsReturn");
  };

  const buttons = [
    {
      label: t("GoodsReturn.makePartialReturn"),
      onClick: handleFirstButtonClick,
      disabled: selectedRowData.length > 0,
    },

    {
      label: t("GoodsReturn.printGoodsReturn"),
      onClick: generatePDF,
      loading: pdfLoading,
      disabled: !(selectedRowData.length > 0),
    },
  ];

  // *************************************************************************************
  // *** Filter Modal ***
  // *************************************************************************************

  const filterFormOnfinish = (values: any) => {
    setModalFilter(values);
  };

  const searchGoodsReturn = (values: any) => {
    //convert to empty string when no value is entered as default value is undefined.
    for (const key in values) {
      if (values[key] === undefined) {
        values[key] = "";
      }
    }

    // return true or false
    let isAnyKeyFilled = Object.keys(values).some(
      (key) => values[key] !== "" && values[key] !== undefined
    );

    const returnDate = values.returnDate
      ? values.returnDate.format("YYYY-MM-DDT00:00:00") + "Z"
      : "";
    const createdDate = values.createdDate
      ? values.createdDate.format("YYYY-MM-DDT00:00:00") + "Z"
      : "";
    let currentOutletId = localStorage.getItem("currentOutletId");
    const params =
      encodeParams({
        companyId: retailerAccess.companyId,
        // companyBranchId: retailerAccess.companyBranchId,
        outletId: currentOutletId,
        fuzzySearch: values.fuzzySearch,
        goodsReturnNo: values.goodsReturnNo || "",
        returnDate: returnDate || "",
        createdAt: createdDate || "",
        status: values.status,
      }) + "&sort=createdAt&sortOrder=-1";

    if (isAnyKeyFilled) {
      setCursor("0");
      setFilterSetting(params);
    }
  };

  const handleStatusMenuClick: MenuProps["onClick"] = ({ key }) => {
    const items = statusFilterOption1;
    setStatusKey(key);
    // Access the label property of the selected item
    const selectedLabel = items.find(
      (menuItem: any) => menuItem.key === key
    )?.label;
    if (selectedLabel) {
      setStatusValue(selectedLabel);
    }
  };

  const filterModal = () => {
    return (
      <div className="w-full">
        <Form
          onFinish={filterFormOnfinish}
          form={filterModalForm}
          className=""
          layout="vertical"
        >
          <h1 className="font-bold text-base pb-4 hidden sm:flex">
            {t("Filter")}
          </h1>
          {/* First Row of Filter Input */}
          <Row className="filterBlockForm flex-col space-y-3">
            <Row className="flex sm:flex-row flex-col gap-x-4 sm:gap-y-0 gap-y-4">
              <Form.Item
                name="goodsReturnNo"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("GoodsReturn.goodsReturn") +
                      " " +
                      t("Common.no") +
                      "?"}
                  </p>
                }
              >
                <FormTextInput
                  placeholder={
                    t("Common.eg") +
                    " " +
                    t("GoodsReturn.goodsReturn") +
                    " " +
                    t("Common.no")
                  }
                  maxLength={30}
                />
              </Form.Item>
              <Form.Item
                name="createdDate"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("GoodsReturn.createdDate") +
                      "?"}
                  </p>
                }
              >
                <SingleDateInput
                  placeholder={
                    t("Common.eg") + " " + t("GoodsReturn.createdDate")
                  }
                  onChange={() => {
                    filterModalForm.submit();
                  }}
                />
              </Form.Item>
            </Row>

            <Row className="flex sm:flex-row flex-col gap-x-4 sm:gap-y-0 gap-y-4">
              <Form.Item
                name="productSku"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("GoodsReturn.productSKU") +
                      "?"}
                  </p>
                }
              >
                {/* <SelectInput
                  placeholder={
                    t("Common.eg") + " " + t("GoodsReturn.productSKU")
                  }
                  options={productSkuOption}
                /> */}
                <ComponentFilterSelect
                  placeholder={
                    t("Common.eg") + " " + t("GoodsReturn.productSKU")
                  }
                  dbName={"productCatalogues"}
                  customParams={{ status: "ACTIVE" }}
                  displayExpr={"sku"}
                  valueExpr={"id"}
                  userAccess={retailerAccess}
                />
              </Form.Item>
              <Form.Item
                name="productName"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("GoodsReturn.productName") +
                      "?"}
                  </p>
                }
              >
                {/* <SelectInput
                  placeholder={
                    t("Common.eg") + " " + t("GoodsReturn.productName")
                  }
                  options={productNameOption}
                /> */}
                <ComponentFilterSelect
                  placeholder={
                    t("Common.eg") + " " + t("GoodsReturn.productName")
                  }
                  dbName={"productCatalogues"}
                  customParams={{ status: "ACTIVE" }}
                  displayExpr={"sku"}
                  valueExpr={"id"}
                  userAccess={retailerAccess}
                />
              </Form.Item>
            </Row>
            <Row className="flex sm:flex-row flex-col gap-x-4 sm:gap-y-0 gap-y-4">
              <Form.Item
                name="returnDate"
                className="mb-0 flex-1"
                label={
                  <p className="text-neutral700 text-[12px]">
                    {t("Common.whatIsThe") +
                      " " +
                      t("GoodsReturn.returnDate") +
                      "?"}
                  </p>
                }
              >
                <SingleDateInput
                  placeholder={
                    t("Common.eg") + " " + t("GoodsReturn.returnDate")
                  }
                  onChange={() => {
                    filterModalForm.submit();
                  }}
                />
              </Form.Item>
              <Form.Item name="" className="mb-0 flex-1"></Form.Item>
            </Row>
          </Row>
          <Row className="flex pt-8 justify-between">
            <PrimaryButtonUI
              label={t("Common.resetAll")}
              onClick={() => {
                filterModalForm.resetFields();
                // setParamsFromLocalStorage(router.pathname, `status=${statusKey}`, "productFilter");
              }}
            />
            {/* <Row> */}
            <SecondaryButtonUI
              label={t("Common.cancel")}
              htmlType="reset"
              onClick={() => {
                setModalFilter({});
                setIsFilterModalOpen(false);
              }}
            />
            <PrimaryButtonUI
              label={t("Common.applyFilter")}
              htmlType="submit"
              onClick={() => {
                setIsFilterModalOpen(false);
              }}
            />
            {/* </Row> */}
          </Row>
        </Form>
      </div>
    );
  };

  const showContent = () => {
    return (
      <div>
        <BackButtonUI
          title={t("GoodsReturn.goodsReturn")}
          buttons={buttons}
        ></BackButtonUI>
        <Row className="mb-4 w-full">
          <FilterFormComponent
            filterForm={filterForm}
            onDebouncedChange={(value) => {
              if (value === "") {
                filterForm.resetFields();
                setStatusKey("ALL");
                setStatusValue("All");
                setFuzzySearchFilter("");
                setModalFilter({});
                filterModalForm.resetFields();
                // setData([...fullData]);
                setShowClearFilter(false);
                setCursor(tempCursor);
                setFilterSetting("");
                localStorage.removeItem("goodsReturnFilter");
              } else {
                filterModalForm.resetFields();
                setModalFilter({});
                setFuzzySearchFilter(value);
              }
            }}
            fieldName={fieldName}
            clearButtonOnChange={() => {
              filterForm.resetFields();
              setStatusKey("ALL");
              setStatusValue("All");
              setFuzzySearchFilter("");
              setModalFilter({});
              filterModalForm.resetFields();
              // setData([...fullData]);
              setShowClearFilter(false);
              setCursor(tempCursor);
              setFilterSetting("");
              localStorage.removeItem("goodsReturnFilter");
            }}
            filterModalButtonOnClick={() => {
              setIsFilterModalOpen(true);
              filterForm.resetFields();
              setFuzzySearchFilter("");
            }}
            modalFilterValue={modalFilter}
            option={statusFilterOption1}
            handleStatusMenuClick={handleStatusMenuClick}
            clearFilterDisable={showClearFilter === true ? false : true}
            statusValue={statusValue}
            debounceValue={fuzzySearchFilter}
          ></FilterFormComponent>
        </Row>
        <ListingTableUI
          // EditableCell={EditableCell}
          bordered
          dataSource={data}
          columns={column}
          // rowClassName="editable-row"
          rowKey={(record: any) => record.id}
          cursor={cursor}
          pagination={false}
          rowSelection={rowSelection}
          loading={tableLoading}
          loader={showButtonLoader}
        />
      </div>
    );
  };

  return (
    <div className="flex flex-col w-full min-h-screen bg-bgOrange">
      <Header items={headerItems} hasSearch={false} values={() => { }} />
      <Content className="flex flex-col mt-3 w-full sm:w-4/5 sm:mx-auto mb-3 sm:t-0 sm:mb-16 px-2">
        {showContent()}
      </Content>
      <Row className="justify-center w-full pt-4">
        {showScrollButton && (
          <div className="fixed bottom-20 right-8 z-50">
            <button
              className={`flex items-center justify-center rounded-full w-10 h-10 text-white text-lg font-semibold focus:outline-none bg-blue-500 hover:bg-blue-600`}
              onClick={handleScrollToTop}
            >
              <ArrowUpOutlined style={{ fontSize: "24px" }} />
            </button>
          </div>
        )}
      </Row>
      <ModalUI
        title={t("GoodsReturn.goodsReturn") + " " + t("GoodsReturn.details")}
        width="80%"
        visible={isModalOpen}
        // onOk={handleOk}
        onCancel={() => setIsModalOpen(false)}
        content={showModal()}
      ></ModalUI>
      {isSmallScreen ? (
        <Drawer
          title="Filter"
          placement="bottom"
          closable={false}
          onClose={() => setIsFilterModalOpen(false)}
          open={isFilterModalOpen}
          height="80vh"
          className="rounded-t-lg"
        >
          {filterModal()}
        </Drawer>
      ) : (
        <ModalUI
          // title={"More Filter"}
          width="70%"
          className={"modalFilterBody"}
          visible={isFilterModalOpen}
          onOk={() => setIsFilterModalOpen(false)}
          onCancel={() => setIsFilterModalOpen(false)}
          content={filterModal()}
          title={""}
        ></ModalUI>
      )}
      <AppFooter retailerAccessValues={retailerAccess} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default GoodsReturnListing;
