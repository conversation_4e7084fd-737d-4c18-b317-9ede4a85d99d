import moment from "moment";
import apiHelper from "../pages/api/apiHelper";
import _ from "lodash";
import { Address, SelectOption } from "@/components/type";
import { isArray, isEqual, isObject } from "lodash";
import { GOOGLE_PLACE_API_KEY } from "@/components/config";

// export const PUBLIC_BUCKET_URL = "https://storage.googleapis.com/ars-public/";
export const PUBLIC_BUCKET_URL = "https://storage.googleapis.com/zappit-public/"; // zappit dev
// export const PUBLIC_BUCKET_URL = "https://storage.googleapis.com/ylb-public/"; // ylb
// export const PUBLIC_BUCKET_URL = "https://storage.googleapis.com/yltc-public/"; // yltc

export const encodeParams = (data: {
  [x: string]: any;
  id?: any;
  code?: any;
  status?: any;
  jobPostingId?: any;
  outletId?: any;
  staffId?: any;
  isClaimed?: any;
  date?: any;
  jobNo?: any;
  description?: any;
  jobTypeId?: any;
  endDate?: any;
  startDate?: any;
  preorderNo?: any;
  distributorCompanyId?: any;
  companyId?: any;
  supplierCompanyId?: any;
  cpo?: any;
  supplierPONo?: any;
  warehouseTypeId?: any;
  name?: any;
  isTradable?: any;
  warehouseId?: any;
}) => {
  let params = "";
  const length = Object.keys(data).length;
  Object.keys(data).map((key, index) => {
    if (Array.isArray(data[key])) {
      data[key].map((item: any, arrayIndex: number) => {
        if (item) {
          params += `${key}=${item}`;
          if (arrayIndex !== data[key].length - 1) params += "&";
        }
      });
      if (length - 1 !== index) params += "&";
    } else {
      if (data[key]) {
        params += `${key}=${data[key]}`;
        if (length - 1 !== index) {
          params += "&";
        }
      }
    }
  });
  return params.length !== 0 ? params : "";
};

export class DataSource {
  private _cursor = "";
  private isDone = false;
  private url = "";
  private params = "";
  private isAll = false;
  private apiVersion = "v1";
  private dataSource = [];

  constructor(
    url: string,
    params: string,
    isAll: boolean,
    apiVersion?: string
  ) {
    this.url = url;
    this.params = params || "";
    this.isAll = isAll || false;
    this.apiVersion = apiVersion || "v1";
  }

  async handlerRequest() {
    const request_url =
      this._cursor !== "" && this._cursor !== "0"
        ? `${this.url}?${this.params}&cursor=${this._cursor}`
        : `${this.url}${this.params ? "?" + this.params : ""}`;
    const data: any = await apiHelper.GET(
      request_url,
      undefined,
      undefined,
      this.apiVersion
    );
    return data;
  }

  async load() {
    try {
      while (!this.isDone) {
        const data = await this.handlerRequest();
        const { items, cursor, item } = data || {};

        if (this.isAll) {
          this.dataSource = this.dataSource.concat(items || item || []);
        } else {
          this.dataSource = items || item || [];
          this.isDone = true;
          if (cursor) {
            return { items: this.dataSource || [], cursor }; // Return data and cursor
          }
        }
        this._cursor = cursor || "";
        if (cursor === "0" || this._cursor === "") {
          this.isDone = true;
        }
      }

      return this.dataSource;
    } catch (err) {
      throw err;
    }
  }
}

export class DataSourceWithPageNumber {
  private _cursor = "";
  private isDone = false;
  private url = "";
  private params = "";
  private isAll = false;
  private apiVersion = "v1";
  private dataSource = [];
  private isUsePOST = false;
  private payload = "";
  private maxResult = 50;
  private pageNumber = 1;

  constructor(url: string, params: string, isAll: boolean, apiVersion?: string, isUsePOST?: boolean, payload?: any) {
    this.url = url;
    this.params = params || "";
    this.isAll = isAll || false;
    this.apiVersion = apiVersion || "v1";
    this.isUsePOST = isUsePOST || false;
    this.payload = payload || {};
  }

  async handlerRequest() {
    const request_url = this._cursor !== "" && this._cursor !== "0" ? `${this.url}?${this.params}&pageNumber=${this.pageNumber}&maxResultsPerPage=${this.maxResult}` : `${this.url}${this.params ? "?" + this.params : ""}`;
    const data: any = !this.isUsePOST ? await apiHelper.GET(request_url, undefined, undefined, this.apiVersion) : await apiHelper.POST(request_url, this.payload, this.apiVersion);
    return data;
  }

  async load() {
    try {
      while (!this.isDone) {
        const data = await this.handlerRequest();
        const { items, cursor, item } = data || {};

        if (this.isAll) {
          this.dataSource = this.dataSource.concat(items || item || []);
        } else {
          this.dataSource = items || item || [];
          this.isDone = true;
          if (cursor) {
            return { items: this.dataSource || [], cursor }; // Return data and cursor
          }
        }

        this._cursor = cursor || "";
        if (cursor === "0" || this._cursor === "") {
          this.isDone = true;
        }
        this.pageNumber += 1;
      }

      return this.dataSource;
    } catch (err) {
      throw err;
    }
  }
}

export const handleApiError = (err: any) => {
  if (err && err.response && err.response.data && err.response.data.error) {
    const debug = err.response.data.error.debug;
    const message = err.response.data.error.message;
    const stackTrace = err.response.data.error.stackTrace;

    let errorCode = "";
    if (message !== "<nil>") {
      errorCode = message;
    } else if (debug !== "<nil>") {
      errorCode = debug;
    } else if (stackTrace !== "<nil>") {
      errorCode = stackTrace;
    }
    const formattedErrorCode = errorCode
      ? errorCode
        .replace(/_/g, " ")
        .replace(/\b\w/g, (match: string) => match.toUpperCase())
      : "";

    const errorMessage = `${formattedErrorCode}`;
    return errorMessage;
  } else if (err && err.response && err.response.data) {
    const message = err.response.data.message;

    const errorMessage = `${message}`;
    return errorMessage;
  } else {
    // Handle any other cases here
    return "An error occurred.";
  }
};

export const isValidPasswordPattern = (val: string): boolean => {
  const re = /^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*\W).{1,}$/;

  if (re.test(val)) {
    return true;
  }
  return false;
};

export const PicSignedUrl = (value: String) => {
  return new Promise((resolve, reject) => {
    apiHelper
      .GET("signedURL?path=" + value)
      ?.then((res: any) => {
        resolve(res.item);
      })
      ?.catch(() => {
        reject();
      });
  });
};

export const formateDateAndTime = (date: string | Date | undefined) => {
  if (!date || date === "0001-01-01T00:00:00Z") return "";
  const d = new Date(date);
  return moment(d).format("DD/MM/YYYY h:mmA");
};

export const formateDate = (date: string | Date) => {
  if (!date) return "";

  const d = new Date(date);
  return moment(d).format("DD/MM/YYYY");
};

// Format date to "DD Month YYYY"
export function formatDateWithShowingMonth(date: Date): string {
  const day = date.getDate();
  const month = date.toLocaleString('default', { month: 'long' });
  const year = date.getFullYear();
  return `${day} ${month} ${year}`;
}

export const capitalize = (val: string | undefined) => {
  if (val) {
    let result = _.startCase(val.toLowerCase());
    return result;
  }
  // return _.capitalize(val);
};

export const cloneDeep = (val: any) => {
  return _.cloneDeep(val);
};

export function NumberThousandSeparator(number: number) {
  // if (!number) return
  const formatNumberWithSeparator = (number: number) => {
    const fixedNumber = (number ?? 0).toFixed(2);
    const parts = fixedNumber.toString().split(".");
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    return parts.join(".");
  };

  return formatNumberWithSeparator(number);
}

// Restructure the data to become Object as to use to setFieldsValue
export const setFilterForm = (
  filterParams: string,
  filterKey: Record<string, string>
) => {
  if (!filterParams) return;
  if (!filterKey) return;

  const keyValuePairs = filterParams.split("&");
  const keyValueObject: { [key: string]: string } = {};
  const duplicatedKey = [];

  for (const item of keyValuePairs) {
    const [key, value] = item.split("=");
    if (keyValueObject[key]) duplicatedKey.push(key);
    keyValueObject[key] = value; // Store the latest value for the key
  }

  if (duplicatedKey) {
    for (const item of duplicatedKey) {
      delete keyValueObject[item];
    }
  }

  const uniqueKeyValuesArray = Object.keys(keyValueObject).map(
    (key) => `${key}=${keyValueObject[key]}`
  );

  // Loop through the key-value pairs
  for (const pair of uniqueKeyValuesArray) {
    // Split each pair by '=' to get the key and value
    const [key, value] = pair.split("=");
    for (const name of Object.keys(filterKey)) {
      // Assign values to the corresponding variables
      if (key === name) {
        filterKey[key] = value;
      }
    }
  }

  return filterKey;
};

// Save the filterParams into LocalStorage
export const setParamsFromLocalStorage = (
  pathName: string,
  filterSetting: string,
  localKey: string
) => {
  // Serialize the filterParams object to JSON
  const filterParamsJSON = JSON.stringify({ [pathName]: filterSetting });
  // Save it to localStorage with a key
  localStorage.setItem(localKey, filterParamsJSON);
};

// Called back the filterParams from LocalStorage
export const getParamsFromLocalStorage = (
  pathName: string,
  localKey: string
) => {
  // Get from localStorage
  const filterParamsJSON = localStorage.getItem(localKey);
  const filterParamsFromLocal = filterParamsJSON
    ? JSON.parse(filterParamsJSON)
    : {};

  if (filterParamsFromLocal && filterParamsFromLocal[pathName]) {
    return filterParamsFromLocal[pathName];
  } else {
    return "";
  }
};

export function removeDuplicateArray(data: any, key: string) {
  return data.filter(
    (arr: any, index: number) =>
      data.findIndex((item: any) => item[key] === arr[key]) === index
  );
}

export const getSelectOptions = async (
  url: string,
  params: string = "",
  displayExpr: string = "name",
  valueExpr = "id",
  line: boolean = true
) => {
  const dataSource = new DataSourceWithPageNumber(url, params, line);
  const res: any = await dataSource.load();

  const data = line ? res : res.items;
  let options: SelectOption[] = [];
  data.forEach((item: { [x: string]: any; status: any }) => {
    let labelName =
      displayExpr === "fullName"
        ? item["firstName"] + " " + item["lastName"]
        : item[displayExpr];
    if (displayExpr === "priceGroup") {
      labelName = item["name"] + " (" + item["description"] + ")";
    }
    if (displayExpr === "companyNameCode") {
      labelName = item["name"] + " (" + item["code"] + ")";
    }
    if (displayExpr === "productNameAndSku") {
      labelName = item["name"] + " (" + item["sku"] + ")";
    }
    if (displayExpr === "promotionNameAndCode") {
      labelName = item["name"] + " (" + item["promotionCode"] + ")";
    }
    options.push({
      value: item[valueExpr],
      label: labelName,
      status: item.status,
    });
  });
  return options;
};

type Permissions = {
  [key: string]: string;
};

type PermissionMapping = {
  keyword: string;
  permission: string;
}[];

const mapping: PermissionMapping = [
  { keyword: "Listing", permission: "R" },
  { keyword: "view", permission: "R" },
  { keyword: "edit", permission: "U" },
  { keyword: "create", permission: "C" },
];

export const checkAccessControl = (
  pathName: string,
  permissions: Permissions
) => {
  //shouldn't be like this but temporary
  if (permissions && (permissions["admin"] || permissions["companyAdmin"]))
    return true;
  if (
    pathName.includes("dashboard") ||
    pathName.includes("500") ||
    pathName.includes("400")
  )
    return true;

  const accessPagePermission = pathName.split("/")[1]; // Extract the first part of the path as the permissions key
  if (!accessPagePermission) return false;
  if (!permissions[accessPagePermission]) return false;

  const requiredPermission = permissions[accessPagePermission]; // Get the required permission for the key
  if (!requiredPermission) return false;

  const permissionsKey = pathName.split("/").slice(-1)[0]; // Extract the last part of the path as the permissions key
  if (!permissionsKey) return false;

  for (const item of mapping) {
    // const containsRorC = new RegExp(item.permission).test(requiredPermission || "");
    // const containsRorC = new RegExp(item.permission).test(requiredPermission || "");
    if (
      (permissionsKey.includes(item.keyword) &&
        requiredPermission.includes(item.permission)) ||
      (item.keyword === "edit" &&
        permissionsKey.includes(item.keyword) &&
        requiredPermission.includes("R"))
    ) {
      // if (permissionsKey.includes(item.keyword) && (containsRorC || requiredPermission === "R")) || (item.keyword === "edit" && permissionsKey.includes(item.keyword) && requiredPermission.includes("R"))) {
      // if (permissionsKey.includes(item.keyword) && containsRorC) {
      return true;
    }
  }

  return false;
};

// ======================================================================
// *** Handling Data Changes ***
// ======================================================================
export function compareDifferent2(data: any, data2: any) {
  const result: any = {};
  for (const key in data) {
    if (
      (isArray(data[key]) && isArray(data2[key])) ||
      (isObject(data[key]) && isObject(data2[key]))
    ) {
      if (JSON.stringify(data[key]) !== JSON.stringify(data2[key])) {
        result[key] = result[key] || [];
        result[key] = result[key].concat(data2[key]);
      }
    } else if (data[key] !== data2[key] && data2[key]) {
      result[key] = data2[key];
    }
  }
  if (!result["id"] && Object.keys(result).length >= 1) {
    result["id"] = data["id"];
  }
  return result;
}

export const SignedUrl = (value: String) => {
  return new Promise((resolve, reject) => {
    apiHelper
      .GET("signedURL?path=" + value)
      ?.then((res: any) => {
        resolve(res.item);
      })
      ?.catch(() => {
        reject();
      });
  });
};

export const getConversionAmount = async (
  productId: string,
  quantity: number,
  currentUOMId: string,
  targetUOMId: string
) => {
  if (
    !productId ||
    !quantity ||
    quantity === 0 ||
    !currentUOMId ||
    !targetUOMId
  )
    return;
  const params = `targetUOMId=${targetUOMId}&currentUOMId=${currentUOMId}&quantity=${quantity}&productId=${productId}`;
  const dataSouce = new DataSource(
    "productCatalogue/convert/uom",
    params,
    true
  );
  const amountReturned: any = (await dataSouce.load()) || 0;
  // if (Object.keys(item).length === 0) setAmountConverted(amountReturned);
  return amountReturned;
};

// ======================================================================
// Excel function
// ======================================================================

// convert each keys to String
export const convertFieldsToString = (rowData: any, fields: string[]) => {
  fields.forEach((field) => {
    if (rowData[field] !== undefined && rowData[field] !== null) {
      if (typeof rowData[field] === "boolean") {
        rowData[field] = rowData[field].toString().trim().toUpperCase();
      } else {
        rowData[field] = rowData[field].toString().trim();
      }
    }
  });
};

// convert each keys to number
export const convertFieldsToNumber = (rowData: any, fieldsToConvertToNumber: string[]) => {
  fieldsToConvertToNumber.forEach((field) => {
    if (rowData[field] !== undefined && rowData[field] !== null && typeof rowData[field] === "string") {
      (rowData as any)[field] = parseFloat(rowData[field]);
    }
  });
};

// convert each keys to time
export function convertFieldsToTime(rowData: any, fieldsToConvertToTime: string[]) {
  const epochStart = new Date("1900-01-01");

  fieldsToConvertToTime.forEach((field) => {
    if (rowData[field] !== undefined && rowData[field] !== null) {
      if (typeof rowData[field] === "number") {
        rowData[field] = moment(
          new Date(epochStart.getTime() + (rowData[field] - 2) * 24 * 60 * 60 * 1000)
        ).format("YYYY-MM-DDT00:00:00") + "Z";
      } else {
        const formattedDate = formatDateToISOString(rowData[field].toString());
        rowData[field] = formattedDate !== undefined ? formattedDate : null;
      }
    }
  });
}

export const formatDateToISOString = (dateStr: string) => {
  if (!dateStr) return;
  if (!dateStr.includes(".")) return;
  const parts = dateStr.split(".").map((part) => parseInt(part));
  const [day, month, year] = parts;
  const date = new Date(year, month - 1, day);
  return moment(date).format("YYYY-MM-DDT00:00:00") + "Z";
};

interface DataObject {
  [key: string]: any;
  // Add other properties as needed
}

export function getUniqueValues(data: DataObject[]): DataObject[] {
  const uniqueValues: DataObject[] = [];
  const uniqueKeys: { [key: string]: boolean } = {};

  for (const item of data) {
    const key = JSON.stringify(item);
    if (!uniqueKeys[key]) {
      uniqueValues.push(item);
      uniqueKeys[key] = true;
    }
  }
  return uniqueValues.length > 0 ? uniqueValues : data;
}

export const getCoordinateFromAddress = async (address: string) => {
  const res: any = await new DataSourceWithPageNumber("coordinateFromAddress", encodeParams({ address: address, key: GOOGLE_PLACE_API_KEY }), true, "v1")
  const response = await res.load()

  return response;
};

// Outlet autoCheckout function
export function getNextQualifiedDate(schedule: Record<string, number[] | null>, fromDate = new Date()): Date {
  console.log("✈️ ~ getNextQualifiedDate ~ schedule:", schedule);

  for (let weekOffset = 0; weekOffset < 4; weekOffset++) {
    const candidateDate = new Date(fromDate);
    candidateDate.setDate(candidateDate.getDate() + (weekOffset * 7));

    const weekNumber = getWeekOfYear(candidateDate);
    const key = (weekNumber % 4 || 4).toString();
    const validDays = schedule[key];

    console.log(`🔎 Checking week ${key} with validDays:`, validDays);

    if (!validDays || validDays.length === 0) {
      continue; // try next week
    }

    // Check each day within that week
    for (let i = 0; i < 7; i++) {
      const dayCandidate = new Date(candidateDate);
      dayCandidate.setDate(candidateDate.getDate() + i);
      const dayOfWeek = dayCandidate.getDay() === 0 ? 7 : dayCandidate.getDay(); // Sunday=0 → 7

      if (validDays.includes(dayOfWeek)) {
        return dayCandidate;
      }
    }
  }

  throw new Error("No upcoming qualified date found in the next 4 weeks.");
}

// Get week number (ISO-8601 style, where week starts on Monday)
function getWeekOfYear(date: Date): number {
  const target = new Date(date.valueOf());
  target.setHours(0, 0, 0, 0);

  // Set to Thursday of this week (ISO week starts on Monday)
  target.setDate(target.getDate() + 3 - ((target.getDay() + 6) % 7));

  const firstThursday = new Date(target.getFullYear(), 0, 4);
  firstThursday.setDate(firstThursday.getDate() + 3 - ((firstThursday.getDay() + 6) % 7));

  const weekNumber = 1 + Math.round(((target.getTime() - firstThursday.getTime()) / 86400000 - 3) / 7);
  return weekNumber;
}