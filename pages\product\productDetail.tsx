import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import Header, { supportedLocales } from "../../components/header";
import {
  CounterComponent,
  PrimaryButtonUI,
  SecondaryButtonUI,
} from "@/components/buttonUI";
import { Layout, Modal, Row, Card, Col, List, Divider } from "antd";
import { useRouter } from "next/router";
import { useEffect, useRef, useState } from "react";
import { CheckboxInput } from "@/components/input";
import {
  DataSource,
  NumberThousandSeparator,
  PUBLIC_BUCKET_URL,
  PicSignedUrl,
  encodeParams,
  getConversionAmount,
  getSelectOptions,
} from "@/stores/utilize";
import noImage from "../../assets/sampleImage/NoImagePlaceholder.jpg";
import apiHelper from "../api/apiHelper";
import {
  MessageErrorUI,
  MessageSuccessUI,
  ModalConfirmUI,
  ProductBlockAddMinus,
  RenderPromotionCard,
} from "@/components/ui";
import {
  Cart,
  Outlet,
  Product,
  ProductAggreateUI,
  ProductPriceGroup,
  ProductUOM,
  Promotion,
  Retailer,
  SelectOption,
  SelectedProduct,
  Taxes,
  TradeInfoAggregate,
  TradeInfoAggregateUI,
  UomConversion,
} from "@/components/type";
import { PlusOutlined, CheckOutlined, LeftOutlined } from "@ant-design/icons";

import useRetailerStore from "@/stores/store";
import { getRetailerData } from "@/stores/authContext";
import moment from "moment";
import ArrowRight from "../../assets/icon/right.svg";
import { ModalUI } from "@/components/modalUI";
import _, { get } from "lodash";
import Decimal from "decimal.js";
import {
  getPromotionProduct,
  getPromotionRelatedOutlet,
} from "../api/salesOrderHelper";
import Loader from "../pageLoader";
import AppFooter from "@/components/footer";
import ImageGallery from "react-image-gallery";

const { Content } = Layout;

function productDetail() {
  const { t } = useTranslation("common");
  const router = useRouter();
  const [retailerAccess, setRetailerAccess] = useState<Retailer>({});
  const [activeAt, setActiveAt] = useState<string>(
    moment().utc().startOf("day").add(1, "millisecond").toISOString()
  );

  const [price, setPrice] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [minimumIncrement, setMinimumIncrement] = useState(0);
  // const [isPreOder, setIsPreOder] = useState(false);
  const [smallestUOM, setSmallestUOM] = useState("");
  const [selectedUOM, setSelectedUOM] = useState("");
  const [displayMOQ, setDisplayMOQ] = useState("");
  // const [afterConversionMOQ, setAfterConversionMOQ] = useState("");
  const [uomConversion, setUOMConversion] = useState<UomConversion[]>([]);

  const [companyName, setCompanyName] = useState("");
  const [productAverageSalesData, setPrdouctAverageSalesData] = useState<any>();

  const [uomAvailable, setUomAvailable] = useState<any>([]);
  const [currentImage, setCurrentImage] = useState("");
  const [startImageIndex, setStartImageIndex] = useState(0);

  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [productPrice, setProductPrice] = useState<ProductPriceGroup[]>([]);
  const [taxMap, setTaxMap] = useState(new Map());
  const [UOMData, setUOMData] = useState<SelectOption[]>([]);
  const [record, setRecord] = useState<any>([]);
  const [carts, setCarts] = useState<Cart[]>([]);
  const [imageFromIndexing, setImageFromIndexing] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Set Selling Price
  const [sellingPrice, setSellingPrice] = useState<number>(0);

  // Set Smaller Conversion UOM
  const [conversionCalculation, setConversionCalculation] = useState<number>(0);

  // Set Brand
  // const [brandId, setBrandId] = useState<string>("");
  const [brandName, setBrandName] = useState<string>("");

  // Set UOM Name
  const [uomName, setUOMName] = useState<string>("");

  // Set Product Company Count
  const [productCompanyCount, setProductCompanyCount] = useState<number>(0);

  //------------------------------------------------------------------------------//
  //    Re-modify code structure`
  //------------------------------------------------------------------------------//
  const [parsedProductInfo, setParsedProductInfo] =
    useState<TradeInfoAggregateUI>();
  // to store which promotion already applyed.
  // const [promotionApply, setPromotionApply] = useState<string[]>();
  const [defaultUOM, setDefaultUOM] = useState<any>("");
  const [modifiedProductPictures, setModifiedProductPictures] = useState<any>(
    []
  );
  const [isNoImage, setIsNoImage] = useState(false);
  const [productCatalogue, setProductCatalogue] = useState<Product>();
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  const [promotionMap, setPromotionMap] = useState(new Map());
  const [promotionDiscount, setPromotionDiscount] = useState<number>(0);

  const [promotionTypes, setPromotionTypes] = useState<{
    [key: string]: Promotion[];
  }>({});
  const [isPromotionShowAll, setIsPromotionShowAll] = useState<boolean>(false);

  const [promotionSelect, setPromotionSelect] = useState<Promotion>({});
  const [promotionProductInvoled, setPromotionProductInvolved] = useState<
    Product[]
  >([]);
  const [promotionProductPriceGroups, setPromotionProductPriceGroups] =
    useState<ProductPriceGroup[]>([]);
  const [promotionProductAggreateDatas, setPromotionProductAggreateDatas] =
    useState<TradeInfoAggregateUI[]>([]);
  const [isPromotionShowDetailModal, setIsPromotionShowDetailModal] =
    useState<boolean>(false);
  const [promotionSelectProductGroupName, setPromotionSelectProductGroupName] =
    useState("");
  const [promotionProductSelected, setPromotionProductSelected] = useState<{
    [key: string]: TradeInfoAggregateUI[];
  }>({});
  const [promotionAutoApplyId, setPromotionAutoApplyId] = useState<string[]>(
    []
  );

  // Loading State
  const [loading, setLoading] = useState(true);
  const [loadingProductInfos, setLoadingProductInfos] = useState(true);

  const [outletInfo, setOutletInfo] = useState<Outlet>();
  //------------------------------------------------------------------------------//

  const sliderImages = modifiedProductPictures;

  // use the retailStoreState;
  const outletId = useRetailerStore((state) => state.currentOutletData?.id);

  const headerItems = [
    {
      label: t("Header.home"),
      route: "/landing",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    {
      label: "Product Category",
      route: "/product/productCategory",
      className: "clickableLabelTextStyle",
    },
    {
      label: "→",
      className: "mx-1 font-light hover:text-labelGray transition-none",
    },
    ,
    {
      label: productCatalogue?.name || "Product Name",
      route: "/product/productDetail",
      className: "labelTextStyle",
    },
  ];

  useEffect(() => {
    if (router.isReady) {
      const retailer: Retailer = useRetailerStore.getState().retailer || {};
      setRetailerAccess(retailer);
      if (!Object.keys(retailer).length) {
        getRetailerData().then((value) => setRetailerAccess(value));
      }

      // get outlet info to check autoCheckout eligibility
      const outletNameData: any =
        useRetailerStore.getState().outletNameData || {};
      if (!Object.keys(outletNameData).length) {
        getOutletData().then((value: any) => {
          setActiveAt(
            value?.nextQualifiedDate ??
              moment().utc().startOf("day").add(1, "millisecond").toISOString()
          );
        });
      } else {
        setActiveAt(
          outletNameData?.nextQualifiedDate ??
            moment().utc().startOf("day").add(1, "millisecond").toISOString()
        );
      }
    }
  }, [router.isReady]);

  useEffect(() => {
    if (selectedUOM && defaultUOM) {
      const uomIndex = uomConversion.findIndex(
        (item) => item.productUOMId === selectedUOM
      );
      let minimumOrder = 0;
      //selected uom is equal to default uom
      if (selectedUOM === defaultUOM.productUOMId) {
        minimumOrder = parsedProductInfo?.minimumOrder ?? 1;
      }
      //selected uom is biggest?
      if (uomIndex === 0) {
        minimumOrder = parsedProductInfo?.minimumOrder ?? 1;
      }
      //biggest uom to unit
      if (uomIndex === uomConversion.length) {
        minimumOrder =
          parsedProductInfo?.minimumOrder ??
          1 * uomConversion[uomIndex]?.conversionToSmallestUOM;
        setQuantity(minimumOrder);
        return;
      }
      //middle uom to default uom
      if (uomIndex !== uomConversion.length) {
        let result = getConversionAmount(
          productCatalogue?.id ?? "",
          parsedProductInfo?.minimumOrder ?? 1,
          defaultUOM.productUOMId,
          selectedUOM
        );
        result.then((resolvedValue) => {
          minimumOrder = resolvedValue?.[0];
          setQuantity(minimumOrder);
        });
      }
    }
  }, [defaultUOM, selectedUOM]);

  useEffect(() => {
    if (productPrice) {
      calculatePromotion();
      calculatePrice();
    }
  }, [productPrice, selectedUOM, quantity]);

  useEffect(() => {
    if (retailerAccess && Object.keys(retailerAccess).length > 0 && outletId) {
      getOutletData(outletId);
    }
  }, [retailerAccess, outletId]);
  //------------------------------------------------------------------------------//
  //    Re-modify code structure
  //------------------------------------------------------------------------------//
  useEffect(() => {
    if (Object.keys(retailerAccess).length > 0) {
      const { productInfo } = router.query;

      const parsedProductInfo1: TradeInfoAggregate =
        typeof productInfo === "string" ? JSON.parse(productInfo) : null;
      setMinimumIncrement(parsedProductInfo1.minimumIncrement || 1);
      setParsedProductInfo(parsedProductInfo1);
      getProductCompanyCount(parsedProductInfo1.priceGroupId);
      getImageIndexing(parsedProductInfo1.productCatalogueId);
      setIsLoading(true);
      // setDefaultUOM(parsedProductInfo1.defaultUOMId);

      // const productPictures1 = parsedProductInfo1?.productUOM?.map(
      //   (a: any) => a.pictures
      // );

      // const modifiedProductPictures1 = productPictures1?.map(
      //   (picturesArray: any) => {
      //     if (picturesArray === null) {
      //       return [noImage.src];
      //     } else {
      //       return picturesArray.map(
      //         (picture: any) => PUBLIC_BUCKET_URL + picture
      //       );
      //     }
      //   }
      // );
      // setModifiedProductPictures(modifiedProductPictures1);
    }
  }, [retailerAccess]);
  //------------------------------------------------------------------------------//

  useEffect(() => {
    if (
      parsedProductInfo &&
      record?.length === 0 &&
      Object.keys(parsedProductInfo)?.length &&
      isLoading === false
    ) {
      getProductCart();
      getUOMConversion(parsedProductInfo.productCatalogueId);
      // get Product Catalogue details
      let currentProductTrade: any[] = [];
      const getCurrentProductTrade = async () => {
        currentProductTrade = await getTradeByIds(
          parsedProductInfo.tradeInfoId
        );
      };
      getCurrentProductTrade();

      getProductCatalog(parsedProductInfo.productCatalogueId).then(
        async (res) => {
          if (!res) {
            return;
          }

          // const outlet = await getOutlet();

          const defaultPicture = parsedProductInfo.picture
            ? PUBLIC_BUCKET_URL + parsedProductInfo.picture
            : noImage.src;

          const productPictures: any = res?.productUOM

            ?.map((product) => product.pictures)
            .map((pictures) => {
              if (!pictures || !pictures?.length) {
                return "";
              } else {
                return pictures.map((picture) => PUBLIC_BUCKET_URL + picture);
              }
            })
            .filter((element) => element);

          let remap: any[] = []; // Initialize as an empty array
          if (productPictures.length > 0) {
            remap = productPictures[0].map((val: any) => ({
              original: val,
              thumbnail: val,
              originalHeight: 200,
              originalWidth: 200,
            }));
          } else {
            setIsNoImage(true);
            remap.push({
              original: noImage.src,
              thumbnail: noImage.src,
            });
          }

          // find defaultUOM follow the trade info
          // const defaultUOM = res?.productUOM?.find(uom => uom.productUOMId === parsedProductInfo.defaultUOMId);

          getTax(res?.taxCategoryIds);

          // get UOM
          const uoms: SelectOption[] = await getSelectOptions(
            "uoms",
            encodeParams({
              status: "ACTIVE",
            }),
            "name",
            "id",
            true
          );
          // get price Groups for that product
          const priceGroups = await getProductPriceGroup(parsedProductInfo);
          let averageSales = await getCompanyAverageSales();
          if (averageSales) {
            setPrdouctAverageSalesData(averageSales[0]);
          }
          // setup price Groups check box
          const priceGroupCheckboxs = onSetupUOMPriceCheckBox(
            priceGroups,
            uoms,
            res?.productUOM || []
          );

          const promotionRelateds = await getPromotionProduct([
            parsedProductInfo.productCatalogueId,
          ]);

          const promotionRelatedOutlets = await getPromotionRelatedOutlet(
            [...promotionRelateds],
            "TRUE",
            [],
            // outlet?.companyBranchId || ""
            ""
          );

          let bundlePromotions: Promotion[] = [],
            singlePromotion: Promotion[] = [],
            autoApplyPromotionId: string[] = [];

          const tempPromotionMap = new Map();

          promotionRelatedOutlets.map((promotion: Promotion) => {
            if ((promotion?.productGroups?.length || 0) > 1) {
              bundlePromotions.push(promotion);
            }
            if ((promotion?.productGroups?.length || 0) === 1) {
              singlePromotion.push(promotion);
            }
            tempPromotionMap.set(promotion.id, promotion);
            if (promotion.isAutoApply !== "FALSE") {
              autoApplyPromotionId.push(promotion?.id || "");
            }
          });

          setPromotionTypes({
            bundle: bundlePromotions,
            single: singlePromotion,
          });

          setPromotionAutoApplyId(autoApplyPromotionId);
          // set promotions
          setPromotions(promotionRelatedOutlets);
          // set promotion map for the reference on bundle promotion.
          setPromotionMap(tempPromotionMap);

          setProductCatalogue(res);

          if (imageFromIndexing.length > 0) {
            setCurrentImage(imageFromIndexing[0]);
            setModifiedProductPictures(imageFromIndexing ?? []);
          } else {
            // set default image
            setCurrentImage(defaultPicture);
            // set product pictures;
            setModifiedProductPictures(remap);
          }

          // let oriMoq = parsedProductInfo?.minimumOrder
          //   ? `${parsedProductInfo?.minimumOrder}`
          //   : "0";

          // setDisplayMOQ(oriMoq);

          let tradeMOQ = currentProductTrade[0]
            ? currentProductTrade[0].minimumOrder
            : 0;
          setDisplayMOQ(tradeMOQ);

          let reStructure = {
            productUOMId: currentProductTrade[0].defaultUOMId,
            name: UOMData.find((item) => {
              item.value === currentProductTrade[0].defaultUOMId;
            })?.label,
          };

          setDefaultUOM(reStructure);

          if (res.productUOM) {
            // const uomData = res.productUOM.find(
            //   (item) => item.isDefaultUOM === "TRUE"
            // );
            // setDefaultUOM(uomData);
            const smallest = res.productUOM.find(
              (item) => item.isSmallest === "TRUE"
            );
            setSmallestUOM(smallest?.productUOMId ?? "");
          }

          // set default product moq;
          // setDisplayMOQ(
          //   parsedProductInfo?.minimumOrder
          //     ? `${parsedProductInfo?.minimumOrder}`
          //     : "0"
          // );
          // set UOM
          setUOMData(uoms?.length ? uoms : []);
          // set UOM available
          setUomAvailable(
            priceGroupCheckboxs?.length ? [...priceGroupCheckboxs] : []
          );
          if (priceGroupCheckboxs?.length) {
            setSelectedUOM(priceGroupCheckboxs[0].value);
            get;
          }
          setProductPrice([...priceGroups]);

          getProductFromCart();

          setLoadingProductInfos(false);
        }
      );
    }
    if (retailerAccess?.companyId && parsedProductInfo && companyName === "") {
      getCompany();
    }
  }, [
    parsedProductInfo,
    imageFromIndexing,
    isLoading,
    // brandId,
    // productCompanyCount,
  ]);

  // page loading
  useEffect(() => {
    if (!loadingProductInfos) {
      setLoading((prev) => false);
    }
  }, [loadingProductInfos]);

  // useEffect(() => {
  //   if (router.isReady) {
  //     const retailer: Retailer = useRetailerStore.getState().retailer || {};
  //     setRetailerAccess(retailer);
  //     if (!Object.keys(retailer).length) {
  //       getRetailerData().then((value) => setRetailerAccess(value));
  //     }
  //   }
  // }, [router.isReady]);

  // useEffect(() => {
  //   if (imageFromIndexing.length > 0) {
  //     setModifiedProductPictures(imageFromIndexing);
  //   }
  // }, [imageFromIndexing]);

  const getTradeByIds = async (ids: string | string[] = []) => {
    try {
      const dataSource = new DataSource(
        "productTradeInfos",
        encodeParams({ id: ids }),
        false
      );

      const res: any = await dataSource.load();
      if (res.items !== null && res.items.length > 0) {
        return res.items;
      }
    } catch (error) {
      console.error("Error fetching outlets:", error);
    }
  };

  const getOutletData = async (id: string[] = []) => {
    const dataSource = new DataSource(
      "outlets",
      encodeParams({ id: id }),
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          setOutletInfo(res.items?.[0]);
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getUOMConversion = async (id: string = "") => {
    const dataSource = new DataSource(
      "productCatalogue/uom/formula",
      encodeParams({ id: id }),
      false
    );
    const conversion: any = await dataSource.load();
    const conversionData = conversion.items[0].productUOM?.sort(
      (a: any, b: any) => b.conversionToSmallestUOM - a.conversionToSmallestUOM
    );
    setUOMConversion(conversionData);
  };

  const getProductCatalog = async (
    id: string = ""
  ): Promise<Product | undefined> => {
    if (!id) return undefined;
    try {
      const params = {
        id,
      };
      const dataSource = new DataSource(
        "productCatalogues",
        encodeParams(params),
        false
      );
      const res: any = await dataSource.load();
      // setBrandId(res.items[0].brandId);
      if (res.items[0].brandId) {
        getBrand(res.items[0].brandId);
      }
      return res.items?.length ? res.items[0] : undefined;
    } catch (err) {
      return undefined;
    }
  };

  const getMoreProductCatelogy = async (ids: any[] = []) => {
    let products: ProductAggreateUI[] = [];
    if (!ids.length) return products;
    try {
      while (ids?.length) {
        const params = {
          id: ids.splice(0, 50),
        };
        const dataSource = new DataSource(
          "productCatalogues",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load();
        if (res?.items?.length) {
          products = products.concat(res.items);
        }
      }
      return products;
    } catch (err) {
      ids = [];
      return products;
    }
  };

  const getTax = async (id: string[] = []) => {
    let tempProductMap = new Map(taxMap);
    if (!id?.length) return tempProductMap;

    const params: any = {
      //   status: "ACTIVE",
      id: [],
    };
    try {
      while (id?.length) {
        params.id = id?.splice(0, 50);
        const dataSource = new DataSource("taxes", encodeParams(params), false);
        const res: any = await dataSource.load().catch(() => {
          id = [];
          //* This Part need re-edit*//
        });

        if (res !== null && res.items.length > 0) {
          setTaxMap((prevDataMap) => {
            const newDataMap = new Map(prevDataMap);
            res.items.forEach((item: Taxes) => {
              if (!newDataMap.has(item.id)) {
                newDataMap.set(item.id, item);
              }
            });
            return newDataMap;
          });
          res.items?.map((item: Taxes) => {
            tempProductMap.set(item.id, item);
          });
        }
      }
    } catch (err) {
      return tempProductMap;
    }
    return tempProductMap;
  };

  const getProductPriceGroup = async (product: TradeInfoAggregateUI) => {
    try {
      const {
        productCatalogueId,
        productPriceGroupId,
        tradeInfoId,
        priceGroupId,
        companyId,
      } = product;
      if (!productCatalogueId) return [];

      const params = {
        companyId: companyId,
        // id: productPriceGroupId,
        productTradeInfoId: tradeInfoId,
        priceGroupId: priceGroupId,
        productCatalogueId: productCatalogueId,
        activeAt: activeAt,
        status: "ACTIVE",
      };
      const dataSource = new DataSource(
        "productPriceGroups",
        encodeParams(params),
        false
      );
      const res: any = await dataSource.load();
      if (res?.items?.length) {
        setSellingPrice(res.items[0].sellingPrice);
        return res.items;
      }
      return [];
    } catch (err) {
      return [];
    }
  };

  const getProductCompanyCount = async (id: string) => {
    const param = {
      companyId: retailerAccess.companyId,
      activeAt: activeAt,
      priceGroupId: id,
      keyField: "productCatalogueId",
    };
    const dataSource = new DataSource(
      "productPriceGroup/distinct",
      encodeParams(param),
      false,
      "v1"
    );

    const res: any = await dataSource.load();

    if (res !== null) {
      if (res && res.length > 0) {
        setProductCompanyCount(res.length);
      } else return;
    }
  };

  const getBrand = async (brandId: string) => {
    const param = {
      id: brandId,
    };
    const dataSource = new DataSource(
      "productBrands",
      encodeParams(param),
      false,
      "v1"
    );

    const res: any = await dataSource.load();
    if (res !== null) {
      if (res.items && res.items.length > 0) {
        setBrandName(res.items[0].name);
      } else return;
    }
  };

  const getMoreProductPriceGroups = async (
    product: TradeInfoAggregateUI,
    productIds: any[] = [],
    productUOMId: string
  ) => {
    let priceGroups: ProductPriceGroup[] = [];
    if (!productIds.length) return priceGroups;

    try {
      while (productIds.length) {
        const { productCatalogueId, priceGroupId, companyId, tradeInfoId } =
          product;
        if (!productCatalogueId) return [];

        const params = {
          companyId: companyId,
          productUOMId: productUOMId,
          productTradeInfoId: tradeInfoId,
          // id: productPriceGroupId,
          priceGroupId: priceGroupId,
          productCatalogueIds: productIds.splice(0, 50),
          activeAt: activeAt,
          status: "ACTIVE",
        };
        const dataSource = new DataSource(
          "productPriceGroups",
          encodeParams(params),
          false
        );
        const res: any = await dataSource.load().catch((err) => {
          productIds = [];
        });
        if (res?.items?.length) {
          priceGroups = priceGroups.concat(res.items);
        }
      }
      return priceGroups;
    } catch (err) {
      return priceGroups;
    }
  };

  const getCompany = () => {
    const dataSource = new DataSource(
      "companies",
      "id=" +
        // + parsedProductInfo?.filterDefaultUOM[0].companyId,
        retailerAccess.companyId,
      false
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null) {
          setCompanyName(res.items[0].name);
          // setCompanyData(res.items[0]);
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getProductFromCart = () => {
    const params = {
      retailerId: retailerAccess.id,
      productIds: parsedProductInfo?.productCatalogueId,
      companyId: parsedProductInfo?.companyId,
      status: "PENDING",
    };
    const dataSource = new DataSource(
      "retailerCarts",
      encodeParams(params),
      false,
      "v2"
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null) {
          const data = res.items[0];
          setRecord(data);
          setSelectedUOM(data.productUOMId);
          setPromotionDiscount(data?.discount || promotionDiscount);
        }
      })
      .catch(() => {
        //* This Part need re-edit*//
      });
  };

  const getCompanyAverageSales = async () =>
    // productId: string[]
    // productName: string,
    // productQuantity: number
    {
      try {
        const param = {
          companyId: retailerAccess.companyId,
          productId: parsedProductInfo?.productCatalogueId,
        };
        const dataSource = new DataSource(
          "invoice/companyAverageSales",
          encodeParams(param),
          false,
          "v1"
        );
        const res: any = await dataSource.load();

        if (res !== null) {
          if (res && res.length > 0) {
            // const updatedSalesData = res.map((item: any) => ({
            //   productId: item.productId,
            //   productName: productName, // Use the product name from each item
            //   quantity: productQuantity, // Use the product quantity from each item
            //   promoSales: item.promoSales,
            //   nonPromoSales: item.nonPromoSales,
            // }));
            // // Merge the new data with the existing companyAverageSales data
            // setCompanyAverageSales(updatedSalesData);
            return res;
          } else return;
        }
      } catch (error) {
        console.error("Error fetching sales data:", error);
        return null;
      }
    };

  const getProductCart = () => {
    const params = {
      retailerId: retailerAccess.id,
      outletId: localStorage.getItem("currentOutletId"),
      status: "PENDING",
    };
    const dataSource = new DataSource(
      "retailerCart/count",
      encodeParams(params),
      false,
      "v2"
    );
    dataSource
      .load()
      .then((res: any) => {
        const items = res?.items || [];
        useRetailerStore.getState().setCart(res || 0);
        setCarts(items);
      })
      .catch(() => {
        setCarts([]);
        //* This Part need re-edit*//
      });
  };

  const getPromotions = async (promotionIds: string[] = []) => {
    try {
      if (!promotionIds.length) return [];

      const params = {
        activeAt: activeAt,
        status: "ACTIVE",
        id: promotionIds,
      };
      const dataSource = new DataSource(
        "promotions",
        encodeParams(params),
        true
      );
      const res: any = await dataSource.load();
      if (res?.length) {
        const updatedPromotions = await res.reduce(
          async (accumPromise: any, current: Promotion) => {
            const accum = await accumPromise;

            if (current.images?.length) {
              const image: any = await PicSignedUrl(current.images[0]);
              current.images = [image];
            }

            accum.push(current);
            return accum;
          },
          Promise.resolve([])
        );

        return updatedPromotions;
      }
      return [];
    } catch (err) {
      return [];
    }
  };

  const getProductTradeInfo = async (
    product: TradeInfoAggregateUI,
    productIds: any[] = []
  ) => {
    let productTradeInfo: any[] = [];
    if (!productIds.length) return productTradeInfo;

    try {
      while (productIds.length) {
        const { companyId } = product;

        const params = {
          companyId: companyId,
          productCatalogueId: productIds.splice(0, 1),
          status: "ACTIVE",
        };
        const dataSource = new DataSource(
          "productTradeInfos",
          encodeParams(params),
          true
        );
        const res: any = await dataSource.load().catch((err) => {
          productIds = [];
        });
        if (res?.length) {
          productTradeInfo = productTradeInfo.concat(res);
        }
      }
      return productTradeInfo;
    } catch (err) {
      return productTradeInfo;
    }
  };

  const getImageIndexing = (productId: string) => {
    const params = {
      productId: productId,
      companyId: parsedProductInfo?.companyId,
      companyBranchId: retailerAccess.companyBranchId,
      status: "ACTIVE",
    };
    const dataSource = new DataSource(
      "imageIndexings",
      encodeParams(params),
      false,
      "v1"
    );
    dataSource
      .load()
      .then((res: any) => {
        if (res !== null && res.items.length > 0) {
          const data = res.items?.length ? res.items[0] : {};
          const pic = data?.productImage?.map(
            (val: string) => PUBLIC_BUCKET_URL + val
          );

          const images = pic.map((val: any) => ({
            original: val,
            thumbnail: val,
            originalHeight: 200,
            originalWidth: 200,
          }));

          setImageFromIndexing(images || []);
          // setModifiedProductPictures(data.productImage);
          // setRecord(data);
          // setSelectedUOM(data.productUOMId);
          // setQuantity(data.quantity);
          // setPromotionDiscount(data?.discount || 0);
        }
        setIsLoading(false);
      })
      .catch((err) => {
        //* This Part need re-edit*//
      });
  };

  // const getOutlet = async () => {
  //   try {
  //     let currentOutletId = localStorage.getItem("currentOutletId");
  //     let params: any = {
  //       id: currentOutletId,
  //     };
  //     const dataSource = new DataSource("outlets", encodeParams(params), false);

  //     const res: any = await dataSource.load();

  //     const outlet: Outlet = res?.items?.length ? res.items[0] : {};

  //     delete outlet?.outletProductList;

  //     setOutletInfo(outlet);

  //     return outlet;
  //   } catch (err) {
  //     return {};
  //   }
  // };

  const onSetupUOMPriceCheckBox = (
    productPriceGroups: ProductPriceGroup[],
    uoms: SelectOption[],
    productUOMs: ProductUOM[]
  ) => {
    const smallestUOM = productUOMs.find((item) => item.isSmallest);
    // const smallestUOMName = uoms.find(item => item.value === smallestUOM?.productUOMId)?.label
    // setup product uom view
    const temp: { value: string; label: string }[] = [];
    productPriceGroups.map((item: ProductPriceGroup) => {
      const uom = uoms.find((uom) => uom.value === item.productUOMId);
      const productUOM = productUOMs.find(
        (uom) => uom.productUOMId == item.productUOMId
      );
      // conversionToSmallestUOM
      const label =
        productUOM?.productUOMId !== smallestUOM?.productUOMId
          ? `${uom?.label}`
          : `${uom?.label}`;
      temp.push({ value: uom?.value || "", label: label || "" });
    });
    return temp;
  };

  const submitAddToCart = () => {
    if (selectedUOM === "") {
      MessageErrorUI(t("Cart.selectedUOM"));
      return;
    }

    const moqConversionNumber =
      uomConversion.find(
        (item: any) => item.productUOMId === defaultUOM.productUOMId
      )?.conversionToSmallestUOM ?? 0;

    const quantityConversionNumber =
      uomConversion.find((item: any) => item.productUOMId === selectedUOM)
        ?.conversionToSmallestUOM ?? 0;

    let moqResult = 0;
    if (selectedUOM !== defaultUOM.productUOMId) {
      moqResult = moqConversionNumber * Number(displayMOQ);
    }

    let quantityBuyInUnit = quantityConversionNumber * quantity;
    // const result = conversionNumber * Number(displayMOQ);
    if (quantityBuyInUnit < moqResult) {
      MessageErrorUI(
        t("Cart.meetMOQ") +
          " " +
          displayMOQ +
          " " +
          UOMData.find((item) => item.value === defaultUOM.productUOMId)?.label
      );
      return;
    }

    const found = record;
    const product = parsedProductInfo;
    const selectedPromotion = [...promotionAutoApplyId];
    const productCatalogueTax = productCatalogue?.taxCategoryIds || [];
    let rate = 0;
    let totalTax = 0;

    const priceOfSelectedUom: ProductPriceGroup =
      productPrice.find(
        (item: ProductPriceGroup) => item.productUOMId === selectedUOM
      ) || {};

    const unitPriceRound = parseFloat(
      new Decimal(priceOfSelectedUom?.sellingPrice ?? 0)
        .toDecimalPlaces(2)
        .toString()
    );

    if (productCatalogueTax?.length) {
      productCatalogueTax.forEach((item: string) => {
        const selectedTax = taxMap.get(item);
        if (selectedTax) {
          rate = rate + selectedTax.rate;
          totalTax = totalTax + selectedTax.rate * (unitPriceRound ?? 0);
        }
      });
    }

    const productQuantity = quantity ? quantity : 1;
    const price = parseFloat(
      new Decimal(unitPriceRound * productQuantity)
        .toDecimalPlaces(2)
        .toString()
    );
    const promotionDiscountDecimal = parseFloat(
      new Decimal(promotionDiscount).toDecimalPlaces(2).toString()
    );

    if (priceOfSelectedUom) {
      let dataSubmit: Cart = {
        productTradeInfoId: priceOfSelectedUom.productTradeInfoId,
        priceGroupId: priceOfSelectedUom.priceGroupId,
        companyId: retailerAccess.companyId || "",
        companyBranchId: outletInfo?.companyBranchId || "",
        outletId: localStorage.getItem("currentOutletId") || "",
        retailerId: retailerAccess.id || "",
        productId: product!.productCatalogueId,
        productUOMId: selectedUOM,
        quantity: productQuantity,
        unitPrice: unitPriceRound,
        totalPrice: price,
        promotionIds: selectedPromotion,
        type: "SALES", //foc or sales
        minimumOrder: Number(displayMOQ),
        defaultUOMId: defaultUOM.productUOMId,
        totalDiscount: promotionDiscountDecimal,
        totalNetPrice: price - promotionDiscountDecimal + totalTax,
        taxId: productCatalogueTax?.length ? productCatalogueTax[0] : "",
        taxRate: rate,
        totalTax: totalTax,
        supplierCompanyId:
          retailerAccess.companyId || "65a4c09214d0dab78fb96a37", // should use for the perorder setting companyId || else use retailer Access companyId
        sellingType: product!.tradeType ? product!.tradeType : "READYSTOCK", //preorder or selling
        status: "PENDING",
      };

      if (found) {
        dataSubmit.id = found.id;
        apiHelper
          .PUT("retailerCart?id=" + found.id, dataSubmit, "", "v2")
          ?.then(() => {
            MessageSuccessUI(
              t("Cart.addToCart") + " " + t("Common.updateSuccess")
            );
            getProductFromCart();
            getProductCart();
          })
          ?.catch(() => {
            //* This Part need re-edit*//
            MessageErrorUI(
              t("Cart.addToCart") + " " + t("Common.updateUnsuccess")
            );
          });
      } else {
        apiHelper
          .POST("retailerCart", dataSubmit, "", "v2")
          ?.then(() => {
            MessageSuccessUI(
              t("Cart.addToCart") + " " + t("Common.successful")
            );
            getProductFromCart();
            getProductCart();
          })
          ?.catch(() => {
            //* This Part need re-edit*//
            MessageErrorUI(t("Cart.addToCart") + " " + t("Common.failed"));
          });
      }
    }
  };

  // to submit to the cars.
  const submitAddToCartWithBundle = async () => {
    const { products, resultItem } = await calculateBundlePromotion();

    if (!products?.length) {
      // show toast or message to tell user does not has any product selected.
      return;
    }

    for (let i = 0; i < products.length; i++) {
      const product = products[i];
      const productCatalogueTax = product?.taxCategoryIds || [];
      let rate = 0;
      let totalTax = 0;

      if (productCatalogueTax?.length) {
        productCatalogueTax.forEach((item: string) => {
          const selectedTax = taxMap.get(item);
          if (selectedTax) {
            rate = rate + selectedTax.rate;
            totalTax =
              totalTax + selectedTax.rate * (product.sellingPrice ?? 0);
          }
        });
      }

      const price = (product.sellingPrice ?? 0) * (product?.quantity || 0);

      const discount = resultItem[product.productCatalogueId]?.Discount || 0;

      const dataSubmit: Cart = {
        companyId: retailerAccess.companyId || "",
        companyBranchId: outletInfo?.companyBranchId || "",
        outletId: localStorage.getItem("currentOutletId") || "",
        retailerId: retailerAccess.id || "",
        productId: product!.productCatalogueId,
        productUOMId: product.uomId || "",
        quantity: product?.quantity || 0,
        unitPrice: product.sellingPrice ?? 0,
        totalPrice: price,
        promotionIds: product.promotionInitId || [],
        type: "SALES", //foc or sales
        totalDiscount: discount,
        // totalNetPrice: price - totalDiscount + totalTax,
        totalNetPrice: price - discount, // Need to calculate after calculated on api
        taxId: productCatalogueTax?.length ? productCatalogueTax[0] : "",
        taxRate: rate,
        totalTax: totalTax,
        supplierCompanyId:
          retailerAccess.companyId || "65a4c09214d0dab78fb96a37", // should use for the perorder setting companyId || else use retailer Access companyId
        sellingType: product!.tradeType ? product!.tradeType : "READYSTOCK", //preorder or READYSTOCK
        status: "PENDING",
      };

      const found = carts.find(
        (item) => item.id === dataSubmit.id && item.companyId === dataSubmit.id
      );

      try {
        if (found) {
          dataSubmit.id = found.id;
          await apiHelper.PUT(
            "retailerCart?id=" + found.id,
            dataSubmit,
            "",
            "v2"
          );
        } else {
          await apiHelper.POST("retailerCart", dataSubmit, "", "v2");
        }
        MessageSuccessUI(t("Cart.addToCart") + " " + t("Common.updateSuccess"));
      } catch (err) {
        MessageErrorUI(t("Cart.addToCart") + " " + t("Common.failed"));
      }
    }

    getProductFromCart();
  };

  // Image On Hover Change Handle
  const handleImageHover = (image: string) => {
    setCurrentImage(image); // Update the currentImage state when hovering
    setPreviewImage(image); // Update the previewImage state when hovering
  };

  // Image Preview Handle
  const prevImage = () => {
    setStartImageIndex((prevIndex) => Math.max(0, prevIndex - 1));
  };

  // Image Preview Handle
  const nextImage = () => {
    setStartImageIndex((prevIndex) =>
      Math.min(prevIndex + 1, sliderImages.length - 3)
    );
  };

  // Image On Click Change Handle
  const handleImageClick = (index: number) => {
    setPreviewImage(sliderImages[index].original); // Set previewImage to the clicked image
    setPreviewVisible(true); // Show the preview modal
  };

  const calculatePrice = async () => {
    const uom = selectedUOM ? selectedUOM : defaultUOM?.productUOMId;
    const cal = quantity ? quantity : 1;
    const priceOfSelectedUom: number =
      (await productPrice.find(
        (item: ProductPriceGroup) => item.productUOMId === uom
      )?.sellingPrice) || 0;
    const unitPriceRound = parseFloat(
      new Decimal(priceOfSelectedUom).toDecimalPlaces(2).toString()
    );
    const total = cal * unitPriceRound;

    setPrice(parseFloat(total.toFixed(2)));

    const smallestConversionUOMPrice: number =
      productCatalogue?.productUOM?.find(
        (uom) => uom.productUOMId === selectedUOM
      )?.conversionToSmallestUOM ?? 0;
    const conversionPrice =
      priceOfSelectedUom / (smallestConversionUOMPrice ?? 0);
    setConversionCalculation(parseFloat(conversionPrice.toFixed(2)));

    const smallestUom = productCatalogue?.productUOM?.find(
      (val) => val.isSmallest === "TRUE"
    )?.smallerUOMId;

    const selectedUOMName = UOMData.find(
      (item) => item.value === smallestUom
    )?.label;
    setUOMName(selectedUOMName || "");
  };

  // 14/2/2024
  // calculate promotion when adjust +- button on the UI
  // need to add loading when user click the + and -.
  // prevent has the show the wrong promotion calculation.
  const calculatePromotion = async () => {
    const product = parsedProductInfo;
    if (product?.promotionInitId?.length && quantity > 0) {
      const priceOfSelectedUom: ProductPriceGroup =
        productPrice.find(
          (item: ProductPriceGroup) => item.productUOMId === selectedUOM
        ) || {};
      const productQuantity = quantity;
      const totalPrice =
        (priceOfSelectedUom.sellingPrice ?? 0) * productQuantity;
      const payload = {
        outletId: localStorage.getItem("currentOutletId"),
        companyId: retailerAccess.companyId,
        companyBranchId: outletInfo?.companyBranchId,
        productOrdered: [
          {
            productId: product!.productCatalogueId,
            productUOMId: selectedUOM,
            quantity: productQuantity,
            price: priceOfSelectedUom.sellingPrice ?? 1,
            promotionIds: product?.promotionInitId,
            type: "SALES", //preorder or selling
            discount: 0,
            total: totalPrice,
            taxId: null,
            taxRate: 0,
          },
        ],
      };

      apiHelper
        .POST(
          `promotion/calculateExclude?outletId=${localStorage.getItem(
            "currentOutletId"
          )}`,
          { salesOrder: payload, activeAt: activeAt },
          "",
          "v1"
        )
        .then((res: any) => {
          let discount = 0;
          if (res?.item?.Result) {
            Object.keys(res.item?.Result).map((key) => {
              discount = discount + (res.item.Result[key].Discount || 0);
            });
            setPromotionDiscount(discount);
            // setPromoionApplyed(res.item)
          } else {
            setPromotionDiscount(0);
          }
        })
        .catch(() => {});
    }
  };

  const calculateBundlePromotion = async () => {
    let products: TradeInfoAggregateUI[] = [];
    Object.keys(promotionProductSelected).map((key) => {
      products = products.concat(promotionProductSelected[key]);
    });

    const productOrdered = products.map((product) => {
      return {
        productId: product!.productCatalogueId,
        productUOMId: product.uomId,
        quantity: product.quantity,
        price: product.sellingPrice ?? 1,
        promotionIds: product?.promotionInitId,
        type: "SALES", //preorder or selling
        discount: 0,
        total: (product?.quantity || 0) * product.sellingPrice,
        taxId: product.taxCategoryIds,
        taxRate: 0,
      };
    });

    const payload = {
      outletId: localStorage.getItem("currentOutletId"),
      companyId: retailerAccess.companyId,
      companyBranchId: outletInfo?.companyBranchId,
      productOrdered: productOrdered,
    };

    try {
      const result: any = await apiHelper.POST(
        `promotion/calculate?outletId=${payload.outletId}`,
        { salesOrder: payload, activeAt: activeAt },
        "",
        "v1"
      );
      const resultItem = result?.item || {};

      return { products, resultItem };
    } catch (err) {
      return { productS: [], resultItem: {} };
    }
  };

  const onClickPromotionCard = async (promotion: Promotion) => {
    setIsPromotionShowAll(false);
    setPromotionSelect(promotion);

    let fullProducts: Product[] = [];
    let fullPriceGroups: ProductPriceGroup[] = [];
    let tempPromotions: Promotion[] = [];
    let fullProductAggreateData: TradeInfoAggregateUI[] = [];

    const isBundle = (promotion?.productGroups?.length || 0) > 1;

    if (promotionMap.size >= 1) {
      tempPromotions = Array.from(promotionMap, ([id, value]) => value);
    }

    // prepare data form the prductGroups
    await promotion.productGroups?.reduce(async (accum, current) => {
      await accum;
      const productIds = current.selectedProducts
        ?.map((product) => product.productId)
        .filter((item) => item);
      const priceGroups = await getMoreProductPriceGroups(
        parsedProductInfo!,
        _.cloneDeep(productIds),
        current?.uomId || ""
      );
      const products = await getMoreProductCatelogy(_.cloneDeep(productIds));
      if (isBundle) {
        const validProductIds: any[] = productIds ?? []; // This will assign an empty array if productIds is undefined

        const productTradeInfos = await getProductTradeInfo(
          parsedProductInfo!,
          _.cloneDeep(productIds)
        );
        const promotionRelateds = await getPromotionProduct(productIds);
        getTax(validProductIds);

        let promotionRelatedOutlets = await getPromotionRelatedOutlet(
          promotionRelateds,
          "TRUE",
          [],
          // outletInfo?.companyBranchId || ""
          ""
        );

        if (tempPromotions?.length) {
          promotionRelatedOutlets =
            promotionRelatedOutlets.concat(tempPromotions);
        }

        products.map((item) => {
          if (item.status === "ACTIVE" && item.sellingType === "SELLING") {
            const tradeInfo = productTradeInfos.find(
              (tradeInfo) => tradeInfo.productCatalogueId === item.id
            );
            const promotionProduct = promotionRelateds?.find(
              (promotion) => promotion.productId === item.id
            );
            const priceGroup = priceGroups.find(
              (priceGroup) => priceGroup.productCatalogueId === item.id
            );
            const image = item?.productUOM?.find(
              (uom) => uom.productUOMId === current?.uomId || ""
            )?.pictures;

            let isSinglePromo = false,
              isBundlePromo = false;
            let promotionInitId: string[] = [];

            // for upload outlet list
            if (promotionProduct) {
              promotionRelatedOutlets.map((promotion) => {
                if (promotionProduct.promoIds.includes(promotion?.id || "")) {
                  if ((promotion?.productGroups?.length || 0) > 1)
                    isBundlePromo = true;
                  if ((promotion?.productGroups?.length || 0) === 1)
                    isSinglePromo = true;

                  if (promotion.isAutoApply === "FALSE") {
                    promotionInitId = promotionInitId.concat(
                      promotion?.id || ""
                    );
                  }
                }
              });
            }

            const promotionProductByCategoires = getPromotionByProductId(
              item.id,
              promotionRelatedOutlets
            );

            if (promotionProductByCategoires.length) {
              promotionProductByCategoires.map((promotion) => {
                if ((promotion?.productGroups?.length || 0) > 1)
                  isBundlePromo = true;
                if ((promotion?.productGroups?.length || 0) === 1)
                  isSinglePromo = true;

                if (promotion.isAutoApply === "FALSE") {
                  promotionInitId = promotionInitId.concat(promotion?.id || "");
                }
              });
            }

            // manually convert that data to follow that api given
            let productAggreateData: TradeInfoAggregateUI = {
              tradeInfoId: tradeInfo?.id,
              productPriceGroupId: priceGroup?.id || "",
              productCatalogueId: item.id,
              companyId: retailerAccess.companyId || "",
              priceGroupId: priceGroup?.priceGroupId || "",
              sellingPrice: priceGroup?.sellingPrice || 0,
              minimumOrder: tradeInfo.minimumOrder,
              defaultUOMId: tradeInfo.defaultUOMId,
              picture: image?.length ? image[0] : "",
              sku: item.sku,
              name: item.name,
              description: item.description,
              packingInfo: item.packingInfo,
              tradeType: tradeInfo.tradeType,
              tradeLabels: tradeInfo.tradeLabels,
              isSinglePromo,
              isBundlePromo,
              promotionInitId: Array.from(new Set(promotionInitId)),
              uomId: current?.uomId || "",
              taxCategoryIds: item.taxCategoryIds,
            };
            fullProductAggreateData.push(productAggreateData);
          }
        });
      }
      fullPriceGroups = fullPriceGroups.concat(priceGroups);
      fullProducts = fullProducts.concat(products);
    }, Promise.resolve());

    // foc product
    await promotion.discounts?.reduce(async (accum, current) => {
      await accum;
      const productIds = current.focProducts?.map(
        (product) => product.focProductId
      );
      const products = await getMoreProductCatelogy(_.cloneDeep(productIds));
      if (products.length) {
        fullProducts = fullProducts.concat(products);
      }
    }, Promise.resolve());

    // set that data to the state, use it on later.
    setPromotionProductPriceGroups(fullPriceGroups);
    setPromotionProductInvolved(fullProducts);
    setPromotionProductAggreateDatas(fullProductAggreateData);
    // for the bundle promotion selected UI.
    setPromotionSelectProductGroupName(
      promotion.productGroups?.length
        ? promotion.productGroups[0]?.defaultGroupName || ""
        : ""
    );
    setIsPromotionShowDetailModal(true);
  };

  const getPromotionByProductId = (
    productId: string,
    tempPromotions?: Promotion[]
  ) => {
    if (!productId) return [];

    const promotionsCheck = tempPromotions?.length
      ? tempPromotions
      : promotions;

    const productPromotions = promotionsCheck.filter((item) => {
      const productIds = item.productGroups?.flatMap((product) =>
        product.selectedProducts?.flatMap((selected) => selected.productId)
      );
      return productIds?.includes(productId);
    });
    return productPromotions;
  };

  const meetIndividualMOQ = (
    tradeInfoAggreate: TradeInfoAggregateUI[],
    individualMOQ: number,
    groupProducts: SelectedProduct[]
  ) => {
    const groupProductId = groupProducts.map((item) => item.productId);
    return individualMOQ > 0
      ? tradeInfoAggreate.every(
          (product) =>
            (product?.quantity || 0) >= individualMOQ &&
            groupProductId.includes(product.productCatalogueId)
        ) && tradeInfoAggreate.length === tradeInfoAggreate?.length
      : tradeInfoAggreate?.length >= 1
      ? true
      : false;
  };

  const meetGroupMOQ = (
    tradeInfoAggreate: TradeInfoAggregateUI[],
    groupMOQ: number
  ) => {
    const totalQuantity = tradeInfoAggreate.reduce(
      (accum, curr) => (accum = accum + (curr?.quantity || 0)),
      0
    );
    return totalQuantity >= groupMOQ;
  };

  const meetTotalBuyPrice = (
    tradeInfoAggreate: TradeInfoAggregateUI[],
    totalBuy: number
  ) => {
    return (
      tradeInfoAggreate.reduce(
        (accum, current) =>
          (accum =
            accum + (+current?.sellingPrice || 0) * (current?.quantity || 0)),
        0
      ) >= totalBuy
    );
  };

  const meetMinRequirement = () => {
    const productGroups = promotionSelect?.productGroups?.map((item) => {
      return item.defaultGroupName;
    });

    const ticks: boolean[] = [];

    productGroups?.map((_, index) => {
      ticks[index] = false;
    });

    promotionSelect?.productGroups?.map((item, index) => {
      const promotionCriterias = item.promotionCriterias || [];
      if (promotionCriterias.length) {
        const { individualMOQ, groupMOQ, totalBuy } = promotionCriterias[0];
        const selectedProducts =
          promotionProductSelected[item.defaultGroupName || ""];

        ticks[index] =
          meetIndividualMOQ(
            selectedProducts,
            individualMOQ || 0,
            item?.selectedProducts || []
          ) &&
          meetGroupMOQ(selectedProducts, groupMOQ || 0) &&
          meetTotalBuyPrice(selectedProducts, totalBuy || 0);
      }
    });

    return ticks;
  };

  const onCancelBundlePromotionSelect = () => {
    let products: TradeInfoAggregateUI[] = [];
    Object.keys(promotionProductSelected).map((key) => {
      products = products.concat(promotionProductSelected[key]);
    });

    if (products.length) {
      showPromotionConfirmModal();
    } else {
      setIsPromotionShowDetailModal(false);
    }
  };

  const showPromotionUI = () => {
    return (
      <div className="grid lg:grid-cols-2 grid-cols-1 gap-2 w-full">
        {promotions.map((promotion, index) => (
          <div key={index} className="w-full">
            {RenderPromotionCard(
              promotion,
              (event: any) => {
                onClickPromotionCard(event);
              },
              { isShowArrow: true, isShowQuestion: false }
            )}
          </div>
        ))}
      </div>
    );
  };

  const showAllPromotionUI = () => {
    return (
      promotions.length > 2 && (
        <div
          onClick={() => {
            setIsPromotionShowAll(!isPromotionShowAll);
          }}
          className="flex items-center cursor-pointer textLabel"
        >
          {t("SalesOrder.ViewAllPromotion")}
          <ArrowRight />
        </div>
      )
    );
  };

  const showPromotionListModal = () => {
    return (
      <ModalUI
        className="font-bold"
        title={t("SalesOrder.promotionList")}
        visible={isPromotionShowAll}
        onCancel={() => setIsPromotionShowAll(false)}
        width="70%"
        content={showPromotionListModalContent()}
      />
    );
  };

  const showPromotionListModalContent = () => {
    return (
      <div>
        {promotionTypes["single"]?.length >= 1 && (
          <>
            <p>{t("SalesOrder.singlePromotion")}</p>
            <Row gutter={[16, 8]}>{showPromotionListContentCard("single")}</Row>
          </>
        )}

        {promotionTypes["bundle"]?.length >= 1 && (
          <>
            <p>{t("SalesOrder.bundlePromotion")}</p>
            <Row>{showPromotionListContentCard("bundle")}</Row>
          </>
        )}
      </div>
    );
  };

  const showPromotionListContentCard = (key: string = "") => {
    return promotionTypes[key]?.map((promotion) => {
      return (
        <Col span={12} key={key + promotion.id}>
          {RenderPromotionCard(
            promotion,
            (event: any) => {
              onClickPromotionCard(event);
            },
            { isShowArrow: false, isShowQuestion: true }
          )}
        </Col>
      );
    });
  };

  const showPromotionDetailModal = () => {
    const isBundle = promotionTypes["bundle"]?.find(
      (promotion) => promotion.id === promotionSelect.id
    );
    const promotionDiscount = promotionSelect?.discounts ?? [];
    const isFoc =
      promotionDiscount.length > 0
        ? promotionDiscount[0]?.focProducts?.length
          ? true
          : false
        : false;
    const title = !isBundle
      ? t("SalesOrder.singlePromotionDetail")
      : t("SalesOrder.bundlePromotionDetail");

    let content;

    if (isBundle) {
      if (isFoc) {
        content = showPromotioFocDetailModalContent();
      } else {
        content = showPromotionBundleDetailModalContent();
      }
    } else {
      if (isFoc) {
        content = showPromotioFocDetailModalContent();
      } else {
        content = showPromotionSingleDetailModalContent();
      }
    }

    return (
      <ModalUI
        destroyOnClose={true}
        className="font-bold"
        title={isFoc ? t("SalesOrder.focPromotionDetail") : title}
        visible={isPromotionShowDetailModal}
        onCancel={() => {
          if (!isBundle) {
            setIsPromotionShowDetailModal(false);
          } else {
            onCancelBundlePromotionSelect();
          }
        }}
        width="70%"
        content={content}
        footer={isBundle ? showPromotionBundleDetailFooter() : null}
      />
    );
  };

  const showPromotioFocDetailModalContent = () => {
    return (
      <>
        <div>
          {RenderPromotionCard(promotionSelect, () => {}, {
            isShowArrow: false,
            isShowQuestion: false,
          })}
          <p className="rounded-tl-lg	rounded-tr-lg pr-[10px] pt-[5px] pb-[5px] textLabel font-semibold ">
            {t("Product.productInvolved")}
          </p>
          {showPromotionSingleDetailsProductList()}
          <p className="rounded-tl-lg	rounded-tr-lg pr-[10px] pt-[5px] pb-[5px] textLabel font-semibold ">
            {t("Product.focProduct")}
          </p>
          {showPromotionFocDetailsProductList()}
        </div>
      </>
    );
  };

  const showPromotionSingleDetailModalContent = () => {
    return (
      <>
        <div>
          {RenderPromotionCard(promotionSelect, () => {}, {
            isShowArrow: false,
            isShowQuestion: false,
          })}
          <p className="rounded-tl-lg	rounded-tr-lg	pl-[10px] pr-[10px] pt-[5px] pb-[5px] text-[13px] font-normal ">
            Product involve
          </p>
          {showPromotionSingleDetailsProductList()}
        </div>
      </>
    );
  };

  const showPromotionFocDetailsProductList = () => {
    return promotionSelect?.discounts?.map((discount) => {
      return discount?.focProducts?.map((item) => {
        const uomId = item.focProductUOMId;
        const uomName = UOMData.find((uom) => uom.value === uomId)?.label || "";
        const products = promotionProductInvoled.filter(
          (promotionProduct) => item.focProductId === promotionProduct.id
        );

        return products?.map((product) => {
          const productUOM = product?.productUOM?.find(
            (uom) => uom.productUOMId === uomId
          );
          // const priceGroup = promotionProductPriceGroups.find(priceGroup => priceGroup.productCatalogueId === product?.id)
          return (
            <>
              <div
                className="flex border rounded-md w-full mb-2 cursor-pointer"
                key={product?.id}
              >
                <div className="flex items-center w-full p-1">
                  <div className="flex-grow w-11/12 flex items-center">
                    <img
                      src={
                        productUOM?.pictures?.length
                          ? PUBLIC_BUCKET_URL + productUOM?.pictures[0]
                          : noImage.src
                      }
                      alt="Promotion Image"
                      className="rounded-md h-10 w-10 object-cover "
                    />
                    <div
                      style={{ flex: 2 }}
                      className="flex-row ml-2 items-start"
                    >
                      <p className="font-semibold textLabel w-full">
                        {product?.name}
                      </p>
                      <div className="textDescription font-normal w-full">
                        <p className=" w-full flex">
                          <p className="text-gray-400 w-[15%]">
                            {t("SalesOrder.productCode")}
                          </p>
                          <p className="w-[85%]">{productUOM?.barcode}</p>
                        </p>
                        {/* <p className="font-light text-sm">Unit Price: {priceGroup?.sellingPrice}</p> */}
                        <p className=" w-full flex">
                          <p className="text-gray-400 w-[15%]">
                            {t("SalesOrder.uom")}
                          </p>{" "}
                          <p className="w-[85%]">{uomName}</p>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </>
          );
        });
      });
    });
  };

  const showPromotionSingleDetailsProductList = () => {
    return promotionSelect?.productGroups?.map((item) => {
      const uomId = item.uomId;
      const uomName = UOMData.find((uom) => uom.value === uomId)?.label || "";
      const products = item.selectedProducts?.flatMap((product) => {
        return promotionProductInvoled.find(
          (promotionProduct) => product.productId === promotionProduct.id
        );
      });

      return products?.map((product) => {
        const productUOM = product?.productUOM?.find(
          (uom) => uom.productUOMId === uomId
        );
        const priceGroup = promotionProductPriceGroups.find(
          (priceGroup) => priceGroup.productCatalogueId === product?.id
        );
        return (
          <>
            <div
              className="flex border rounded-md w-full mb-2 cursor-pointer"
              key={product?.id}
            >
              <div className="flex items-center w-full p-1">
                <div className="flex-grow w-11/12 flex items-center">
                  <img
                    src={
                      productUOM?.pictures?.length
                        ? PUBLIC_BUCKET_URL + productUOM?.pictures[0]
                        : noImage.src
                    }
                    alt="Promotion Image"
                    className="rounded-md h-10 w-10 object-cover"
                  />
                  <div
                    style={{ flex: 2 }}
                    className="flex-row ml-2 items-start"
                  >
                    <p className="font-semibold textLabel w-full">
                      {product?.name}
                    </p>
                    <div className="textDescription font-normal w-full">
                      <p className=" w-full flex">
                        <p className="text-gray-400 w-[15%]">
                          {t("Product.productCode")}
                        </p>
                        <p className="w-[85%]">{productUOM?.barcode}</p>
                      </p>
                      <p className="w-full flex">
                        <p className="text-gray-400 w-[15%]">
                          {t("Product.unitPrice")}
                        </p>
                        <p className="w-[85%]">{priceGroup?.sellingPrice}</p>
                      </p>
                      <p className="w-full flex">
                        <span className="text-gray-400 w-[15%]">
                          {t("Product.uom")}
                        </span>
                        <span className="w-[85%]">{uomName}</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        );
      });
    });
  };

  const showPromotionBundleDetailModalContent = () => {
    return (
      <>
        <div>
          {RenderPromotionCard(promotionSelect, () => {}, {
            isShowArrow: false,
            isShowQuestion: false,
          })}
          {showProductSelectByProductGroup()}
          {showPromotionBundleDetailsProductList()}
        </div>
      </>
    );
  };

  const showPromotionBundleDetailFooter = () => {
    return (
      <div className="flex">
        <SecondaryButtonUI
          label={t("Common.cancel")}
          className="w-full buttonStyle secondaryButtonBg"
          onClick={() => {
            onCancelBundlePromotionSelect();
          }}
        />
        <PrimaryButtonUI
          htmlType="submit"
          label={t("Common.save")}
          className="w-full"
          onClick={() => {
            submitAddToCartWithBundle();
          }}
        />
      </div>
    );
  };

  const showProductSelectByProductGroup = () => {
    const productGroups = promotionSelect?.productGroups?.map((item) => {
      return item.defaultGroupName;
    });

    const ticks = meetMinRequirement();

    const columns = () => {
      return productGroups?.map((item, index) => {
        const selectedProducts = promotionProductSelected[item || ""];
        const picture = selectedProducts?.length
          ? selectedProducts[0].picture
          : "";

        let className =
          "product-col h-14 w-14 bg-lightGrey2 flex items-center justify-center mr-2 ";
        if (promotionSelectProductGroupName == item) {
          className = className + "border-4 border-sky-500";
        }
        return (
          <div
            onClick={() => {
              setPromotionSelectProductGroupName(item || "");
            }}
            className={className}
            key={item}
          >
            {selectedProducts?.length && picture ? (
              <img
                src={PUBLIC_BUCKET_URL + picture}
                className="bg-white h-12 w-12"
              />
            ) : null}
            {selectedProducts?.length && !picture ? (
              <img src={noImage.src} className="bg-white h-12 w-12" />
            ) : null}
            {!selectedProducts?.length && !picture && (
              <PlusOutlined className="color-blue" />
            )}
            {ticks[index] && (
              <CheckOutlined className="text-emerald-500 absolute" />
            )}
          </div>
        );
      });
    };

    return <div className="flex relative pb-4">{columns()}</div>;
  };

  const showPromotionBundleDetailsProductList = () => {
    const productGroups = promotionSelectProductGroupName
      ? promotionSelect?.productGroups?.filter(
          (item) => item.defaultGroupName === promotionSelectProductGroupName
        )
      : promotionSelect?.productGroups?.length
      ? [promotionSelect?.productGroups[0]]
      : [];

    if (!productGroups?.length) return null;

    return productGroups?.map((item) => {
      // const uomName = UOMData.find(uom => uom.value === uomId)?.label || ''

      if (!item.selectedProducts?.length) return null;

      const uomId = item.uomId;
      const productIds = item.selectedProducts.map(
        (product) => product.productId
      );
      const promotionSelected =
        promotionProductSelected[item.defaultGroupName || ""];
      // filter includes product from productGroups and selling price;
      let products = promotionProductAggreateDatas.filter(
        (product) =>
          productIds.includes(product.productCatalogueId) &&
          product.uomId === uomId &&
          product.sellingPrice
      );

      if (promotionSelected?.length) {
        promotionSelected.map((productSelect) => {
          const index = products.findIndex(
            (p) => p.productCatalogueId === productSelect.productCatalogueId
          );
          if (index >= 0 && products.length) {
            products[index].quantity = productSelect.quantity;
          }
        });
      }

      return (
        <List
          grid={{
            gutter: 2,
            xs: 3,
            sm: 4,
            md: 5,
            lg: 6,
            xl: 7,
            xxl: 9,
          }}
          className="bg-lightPurple"
          dataSource={products}
          renderItem={(product: any) => (
            <List.Item key={product.id}>
              <div className="product-col ">
                <ProductBlockAddMinus
                  className="flex flex-col items-center"
                  productName={product?.name || "EMPTY"}
                  product={product}
                  price={product.sellingPrice || 0.0}
                  image={
                    (product?.picture && PUBLIC_BUCKET_URL + product.picture) ||
                    noImage.src
                  }
                  onclick={(value) => {
                    setPromotionProductSelected((previous) => {
                      const key = item.defaultGroupName || "";
                      // Check if the key exists in the previous state
                      if (previous.hasOwnProperty(key)) {
                        // Check if the product with the same ID exists
                        const existingProduct = previous[key].find(
                          (prevProduct) =>
                            prevProduct.productCatalogueId ===
                            product.productCatalogueId
                        );
                        if (existingProduct) {
                          // If the product with the same ID exists, update its quantity
                          const updatedProducts = previous[key].map(
                            (prevProduct) => {
                              if (
                                prevProduct.productCatalogueId ===
                                product.productCatalogueId
                              ) {
                                // Replace the quantity of the existing product
                                return { ...prevProduct, quantity: value };
                              }
                              return prevProduct;
                            }
                          );
                          return {
                            ...previous,
                            [key]: updatedProducts.filter(
                              (updateProduct) => updateProduct.quantity
                            ),
                          };
                        } else {
                          // If the product with the same key but different ID, add it to the array
                          const prevProduct = product;
                          prevProduct.quantity = value;
                          return {
                            ...previous,
                            [key]: [...previous[key], prevProduct],
                          };
                        }
                      } else {
                        // If the key doesn't exist, add a new key with the product
                        const prevProduct = product;
                        prevProduct.quantity = value;
                        return { ...previous, [key]: [prevProduct] };
                      }
                    });
                  }}
                />
              </div>
            </List.Item>
          )}
        />
      );
    });
  };

  const showOriginalPrice = (price: any, promotionDiscount: any) => {
    if (promotionDiscount > 0) {
      const originalPrice = NumberThousandSeparator(price);
      const discountedPrice = NumberThousandSeparator(
        price - promotionDiscount
      );
      return (
        <div className="flex items-baseline text-gray-400 textLabel mr-2">
          <p className="mr-1 line-through">RM {originalPrice}</p>
        </div>
      );
    } else {
      return null;
    }
  };
  const showPromotionConfirmModal = () => {
    ModalConfirmUI({
      title: t("SalesOrder.warningTitle"),
      content: t("SalesOrder.warningDescription"),
      okText: t("Common.remove"),
      cancelText: t("Common.cancel"),
      onOk: () => {
        // remove it
        const product = promotionProductSelected;
        Object.keys(product).map((key) => {
          delete product[key];
        });
        setPromotionProductSelected(product);
        setIsPromotionShowDetailModal(false);
      },
      onCancel: () => {},
    });
  };

  const refImg = useRef<ImageGallery>(null);

  const showContent = () => {
    return (
      <>
        <div className="flex flex-col">
          {/* <div
            className="flex max-w-fit px-2 cursor-pointer py-2 border-labelGray/20 hover:border-buttonOrange/40 hover:text-buttonOrange hover:bg-buttonOrange/5 shadow-sm transition-colors duration-150 group"
            onClick={() => {
              router.push("/product/productCategory");
            }}
          >
            <LeftOutlined
              size={16}
              className="mr-2 text-labelGray transition-transform duration-200 ease-in-out 
               group-hover:-translate-x-1 group-hover:text-buttonOrange"
            />
            <p className="text-sm font-mulish text-labelGray group-hover:text-buttonOrange">
              Back to Products List
            </p>
          </div> */}
          <div className="flex flex-col sm:flex-row mb-10 px-4 ">
            {/* Product Image */}
            <Col className="py-2 ipad:w-[450px] sm:w-[300px] w-full">
              <Row className="justify-center">
                {/* slider */}
                {/* <div className="animate-fade-in"> */}
                <div className="flex w-full justify-center items-center">
                  {isNoImage ? (
                    <ImageGallery
                      items={sliderImages}
                      showThumbnails={false}
                      showPlayButton={false}
                      showFullscreenButton={false}
                      ref={refImg}
                      onClick={() => {
                        const currentIndex =
                          refImg.current?.getCurrentIndex() ?? 0;
                        handleImageClick(currentIndex);
                      }}
                    />
                  ) : (
                    <ImageGallery
                      items={sliderImages}
                      thumbnailPosition={"bottom"}
                      showPlayButton={false}
                      showFullscreenButton={false}
                      ref={refImg}
                      onClick={() => {
                        const currentIndex =
                          refImg.current?.getCurrentIndex() ?? 0;
                        handleImageClick(currentIndex);
                      }}
                    />
                  )}
                </div>
                {/* </div> */}
                {/* <div className="sm:h-[5%] sm:w-auto sm:h-[90px] sm:w-[5%] flex justify-center sm:items-end sm:items-center">
                    {startImageIndex < sliderImages.length - 3 && ( // Check if there are more images to show
                      <button
                        className="flex text-xl cursor-pointer"
                        onClick={nextImage}
                      >
                      <RightOutlined className="text-buttonOrange sm:hidden sm:block" />
                      <DownOutlined className="text-buttonOrange sm:block sm:hidden" />
                      </button>
                      )}
                      </div> */}
              </Row>

              {/* Company Infos */}
              <div>
                <Card className="hidden sm:flex ipad:w-[450px] sm:w-[300px] w-full my-6 font-semibold displayCard sm:mx-auto md:mx-0 ">
                  <p className="text-lg text-labelGray">{companyName}</p>
                  <p className="text-base text-buttonOrange ">
                    {`${
                      productCompanyCount === undefined
                        ? "0 Products"
                        : productCompanyCount === 0
                        ? "0 Products"
                        : productCompanyCount + " "
                    }`}
                    <span className="text-base text-buttonOrange">
                      {" "}
                      {productCompanyCount === 1 ? "Product" : "Products"}
                    </span>
                  </p>
                </Card>
              </div>
            </Col>

            <div className="py-2 md:ml-4 sm:ml-0 sm:pl-2 xs:pl-0 flex-1 flex flex-col content-between gap-y-2 pl-2  ">
              {/* Order Selection */}
              <Row className="bg-white shadow-sm rounded-lg p-3 gap-y-2 ">
                <Row className=" font-mulish font-semibold text-lg sm:text-xl">
                  {parsedProductInfo?.name}
                </Row>
                <hr
                  className="w-full border-2 border-transparent rounded-full "
                  style={{
                    borderWidth: "2px",
                    borderStyle: "solid",
                    borderImage:
                      "linear-gradient(to right, #FEA6544D, #FEA65480, transparent) 1",
                  }}
                />

                {/* Price */}
                <div className="flex justify-between w-full sm:max-w-sm">
                  <Row className="flex items-baseline">
                    {/* Price Section */}
                    <div className="flex items-baseline gap-x-1 pr-3">
                      <p className="text-buttonOrange textTitle font-mulish ">
                        RM
                      </p>
                      <p className="text-buttonOrange textPriceNumber font-bold font-mulish ">
                        {NumberThousandSeparator(price - promotionDiscount)}
                      </p>
                    </div>

                    {/* Original Price (Strikethrough) */}
                    <span className="text-gray-400 line-through text-sm">
                      {showOriginalPrice(price, promotionDiscount)}
                    </span>
                  </Row>
                  {/* Discount Badge (Centered) */}
                  {promotionDiscount > 0 && (
                    <div className="pt-1 flex items-center h-full ">
                      <div className="bg-buttonOrange px-4 py-1 text-xs font-semibold rounded-full text-white">
                        Save {((promotionDiscount / price) * 100).toFixed(2)}%
                      </div>
                    </div>
                  )}
                </div>
              </Row>

              {/* Product Info */}
              <Row className="bg-white shadow-sm rounded-lg p-3 gap-y-2">
                <span className="text-labelGray font-semibold text-lg">
                  {/* {t("Product.details")} */}
                  Product Details
                </span>
                <hr
                  className="w-full border-2 border-transparent rounded-full "
                  style={{
                    borderWidth: "2px",
                    borderStyle: "solid",
                    borderImage:
                      "linear-gradient(to right, #FEA6544D, #FEA65480, transparent) 1",
                  }}
                />
                <Row className="grid grid-cols-2 gap-y-2 text-sm sm:text-base w-full">
                  <div className="flex flex-col">
                    <span className="text-labelGray/70   font-semibold font-mulish">
                      {t("Product.barcode")}
                    </span>
                    <span className="font-semibold text-labelGray font-mulish">
                      {parsedProductInfo?.barcode}
                    </span>
                  </div>
                  <div className="flex flex-col ">
                    <span className="text-labelGray/70   font-semibold">
                      {t("Product.brand")}
                    </span>
                    <span className="font-semibold text-labelGray ">
                      {brandName}
                    </span>
                  </div>
                  <div className="flex flex-col ">
                    <span className="text-labelGray/70   font-semibold">
                      {t("Product.moq")}
                    </span>
                    <span className="font-semibold text-labelGray ">
                      {displayMOQ +
                        " " +
                        UOMData.find(
                          (val) => val.value === defaultUOM.productUOMId
                        )?.label}
                    </span>
                  </div>
                  <div className="flex flex-col ">
                    <span className="text-labelGray/70   font-semibold">
                      {t("Product.description")}
                    </span>
                    <span className="font-semibold text-labelGray ">
                      {productCatalogue?.description
                        ? productCatalogue?.description
                        : "-"}
                    </span>
                  </div>

                  {productAverageSalesData ? (
                    <div className="flex flex-col ">
                      <span className="text-labelGray/70   font-semibold">
                        {" "}
                        {t("Product.averageSales")}
                      </span>
                      <span className="font-semibold text-labelGray ">
                        {productAverageSalesData?.averageNonPromoSales} {"UNT"}
                      </span>
                    </div>
                  ) : null}

                  {/* Last Stock */}
                  {/* <Row className="tablet:flex-row mobile:flex-col pb-[20px]">
                  <Row className="w-1/3 pr-2">
                    {t("Product.lastStockCount") + " :"}
                  </Row>
                  <Row className="xl:w-2/3 mobile:w-full">5 CTN</Row>
                  </Row> */}

                  {/* Shelf Life */}
                  {/* <Row className="pl-2 pt-2 pb-[10px]">
                <Row className="xl:w-[20%] lg:w-[30%] md:w-[40%] sm:w-[40%] sm:w-[35%] pr-2 textLabel text-gray-500">
                  {t("Product.stockLastFor")}
                </Row>
                <Row className="capitalize flex items-baseline">
                  <span className="font-semibold textNumber">
                    {productCatalogue?.shelfLife}
                  </span> */}
                  {/* <span className="textLabel ml-1">
                    {productCatalogue?.shelfLife === 1 ? " Day" : " Days"}
                  </span> */}
                  {/* </Row>
              </Row> */}
                </Row>
              </Row>

              {/* Promotion */}

              {promotions.length > 0 ? (
                <Row className="bg-white shadow-sm rounded-lg p-3 gap-y-2">
                  <span className="text-labelGray font-semibold text-lg">
                    {t("Product.promotion")}
                  </span>
                  <hr
                    className="w-full border-2 border-transparent rounded-full "
                    style={{
                      borderWidth: "2px",
                      borderStyle: "solid",
                      borderImage:
                        "linear-gradient(to right, #FEA6544D, #FEA65480, transparent) 1",
                    }}
                  />
                  <Row className="flex flex-col w-full">
                    {/* <Row className="text-labelGray/70   font-semibold">
                    {t("Product.promotion")}
                  </Row> */}
                    <Row className="gap-0 sm:gap-2">
                      {showPromotionUI()}
                      {/* {showAllPromotionUI()} */}
                    </Row>
                  </Row>
                </Row>
              ) : null}

              <Row className="bg-white shadow-sm rounded-lg p-3 gap-y-2 flex flex-col">
                <span className="text-labelGray font-semibold text-lg">
                  {t("Product.orderInfo")}
                </span>
                <hr
                  className="w-full border-2 border-transparent rounded-full "
                  style={{
                    borderWidth: "2px",
                    borderStyle: "solid",
                    borderImage:
                      "linear-gradient(to right, #FEA6544D, #FEA65480, transparent) 1",
                  }}
                />
                <div className="flex flex-col">
                  <span className="text-labelGray/70   font-semibold text-sm sm:text-base">
                    {t("Product.unitofMeasurement")}
                  </span>
                  <span className="font-semibold flex flex-row">
                    {uomAvailable.map((uom: any, index: number) => (
                      <CheckboxInput
                        key={index}
                        className="items-start checkBoxDesign textDescription "
                        label={uom.label}
                        checked={selectedUOM === uom.value}
                        onChange={() => {
                          setSelectedUOM(uom.value);
                          setQuantity(1);
                        }}
                        // disabled={record.productUOMId}
                      />
                    ))}
                  </span>
                </div>
                <div className="flex flex-col ">
                  <span className="text-labelGray/70   font-semibold">
                    {t("Product.quantity")}
                  </span>
                  <span className="font-semibold">
                    <CounterComponent
                      className="h-[26px] w-[26px] flex items-center justify-center rounded-xl bg-buttonOrange px-4 border-0 text-white font-bold"
                      defaultValue={quantity}
                      multiplication={
                        selectedUOM === smallestUOM ? minimumIncrement : 0
                      }
                      minimumOrder={parseFloat(displayMOQ)}
                      onCountChange={(val) => {
                        setQuantity(val);
                      }}
                    />
                  </span>
                </div>
              </Row>
              {/* 
              <Row className="flex flex-row pl-2 pt-2 pb-[10px]">
                <Row className="xl:w-[20%] lg:w-[30%] md:w-[40%] sm:w-[40%] sm:w-[35%] pr-2 textLabel text-gray-500">
                  {t("Product.unitofMeasurement")}
                </Row>
                <Row className="items-start gap-x-4 tablet:w-3/5 ">
                 
                </Row>
              </Row>

              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 mt-10">
                <Row className="xl:w-[20%] lg:w-[30%] md:w-[40%] sm:w-[40%] sm:w-[35%] pr-2 textLabel text-gray-500">
                  {t("Product.quantity")}
                </Row>
                <Row className="font-semibold">
                  <CounterComponent
                    className="h-[26px] w-[26px] flex items-center justify-center rounded-xl bg-buttonOrange px-4 border-0 text-white font-bold"
                    defaultValue={quantity}
                    multiplication={
                      selectedUOM === smallestUOM ? minimumIncrement : 0
                    }
                    onCountChange={(val) => {
                      setQuantity(val);
                    }}
                  />
                </Row> */}
              {/* </div> */}

              <Row className="mt-[25px] flex justify-end">
                <div className="flex flex-row gap-x-4 w-full lg:w-2/3 md:justify-between">
                  <SecondaryButtonUI
                    label={t("Common.back")}
                    className="w-full buttonStyle secondaryButtonBg"
                    onClick={() => {
                      router.back();
                      // setSelectAddressVisible(false);
                    }}
                  />
                  <PrimaryButtonUI
                    htmlType="submit"
                    label={t("Product.addToCart")}
                    className="w-full "
                    onClick={() => submitAddToCart()}
                  />
                </div>
              </Row>
            </div>
          </div>
        </div>
      </>
    );
  };

  if (loading) return <Loader />;

  return (
    <div className="flex flex-col w-full min-h-screen bg-bgOrange">
      <Header items={headerItems} hasSearch={true} values={() => {}} />
      <Content className="flex flex-col gap-y-7 w-full sm:w-4/5 sm:mx-auto mb-16  sm:pt-0 sm:mb-0">
        {showContent()}
      </Content>
      <Modal
        open={previewVisible}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
      >
        {previewImage && (
          <img alt="Preview" style={{ width: "100%" }} src={previewImage} />
        )}
      </Modal>
      {showPromotionListModal()}
      {showPromotionDetailModal()}
      <AppFooter retailerAccessValues={retailerAccess} />
    </div>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(
        locale,
        ["common"],
        null,
        supportedLocales
      )),
    },
  };
}

export default productDetail;
