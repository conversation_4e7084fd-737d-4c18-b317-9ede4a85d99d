export type SelectOption = {
  value: string; //| number;
  label: string;
  status?: string;
  disabled?: boolean;
};

export type RoleIds = {
  index?: number;
  value?: string[];
  policies?: string[];
  status?: string;
  name?: string;
};

export type GoodsReturn = {
  id?: string;
  goodsReturnNo?: string;
  returnDate?: string;
  returnMode?: string;
  outletId?: string;
  retailerId?: string;
  staffId?: string;
  companyBranchId?: string;
  remark?: string;
  products?: GoodsReturnProduct[];
  status?: string;
  model?: string;
  goodsReturnDocuments?: string[];
  stockReceiptId?: string;
  createdAt?: string;
  toteboxes?: ToteBoxDetail[];
  companyId?: string;
  document?: string;
  statusReasons?: string[];
  applicantNameNType?: string;
  rejectRemark?: string;
};

export type GoodsReturnProduct = {
  key?: string;
  productId?: string;
  productCode?: string;
  productDesc?: string;
  returnType?: string;
  invoiceId?: string;
  productUOMId?: string;
  price?: number;
  quantity?: number;
  returnQuantity?: number;
  returnUOMId?: string;
  batchDetails?: string[];
  type?: string;
  discount?: number;
  discountPerUnit?: number;
  taxId?: string;
  taxRate?: number;
  reasonId?: string;
  productCondition?: string;
};

export type ToteBoxDetail = {
  toteboxId?: string;
  returnType?: string;
  invoiceId?: string;
  reasonId?: string;
};

export type googleMap = {
  lat?: number;
  lng?: number;
  address?: string;
};

export type Address = {
  name?: string;
  unitNo?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  longitude?: string;
  latitude?: string;
  countryCode?: string;
};

export type CompanyGeneralInfo = {
  id?: string;
  code?: string;
  organisationId?: string;
  unitNo?: string;
  address1?: string;
  address2?: string;
  state?: string;
  city?: string;
  companyRegistrationNo?: string;
  contactPerson?: string;
  country?: string;
  deliveryLeadTime?: number;
  longitude?: string;
  latitude?: string;
  email?: string;
  faxNo?: string;
  gstNo?: string;
  ahsType?: string;
  ahs?: number;
  loadingBay?: number;
  mobilePhone?: string;
  name?: string;
  officePhone?: string;
  postalCode?: string;
  status?: string;
  bankName?: string;
  accountNo?: string;
  swiftCode?: string;
};

export type CompanyBranch = {
  id?: string;
  Address?: Address;
  branchName?: string;
  branchCode?: string;
  contactPerson?: string;
  officePhone?: string;
  faxNo?: string;
  mobilePhone?: string;
  status?: string;
  unitNo?: string;
  address1?: string;
  address2?: string;
  country?: string;
  state?: string;
  city?: string;
  postalCode?: string;
  approvalStatus?: string;
  hqCompanyId?: string;
  companyName?: string;
  bankName?: string;
  accountNo?: string;
  swiftCode?: string;
  longitude?: string;
  latitude?: string;
};

export type OutletNameAPI = {
  id?: string;
  name?: string;
  outletCode?: string;
  companyId?: string;
  companyBranchId?: string;
  priceGroupId?: string;
  image?: string;
  autoCheckoutSchedule?: any;
  nextQualifiedDate?: string | Date;
}

export type Outlet = {
  id?: string;
  outletCode?: string;
  alternativeOutletCode?: string;
  companyId?: string;
  companyBranchId?: string;
  name?: string;
  tradeName?: string;
  image?: string;
  companyRegistrationNo?: string;
  businessType?: string;
  isTaxable?: string;
  gstNo?: string;
  sstNo?: string;
  taxExemptionNo?: string;
  creditTerm?: number;
  creditTermRate?: CreditTermRate[];
  creditTermLastTier?: CreditTermLastTier;
  creditLimit?: number;
  creditLimitBlockType?: string;
  timeWindowEnabled?: string;
  timeWindowRanges?: string[];
  overdueBlockType?: string;
  outstandingPayment?: number;
  priceGroupId?: string;
  deliveryLeadTime?: number;
  outletCategoryIds?: string[];
  longitude?: string;
  latitude?: string;
  unitNo?: string;
  address1?: string;
  address2?: string;
  country?: string;
  state?: string;
  city?: string;
  postalCode?: string;
  billContactPerson?: string;
  billOfficePhone?: string;
  billMobilePhone?: string;
  billEmail?: string;
  alternativeCompanyId?: string;
  region?: string;
  outletInventory?: OutletInventory[];
  outletProductList?: OutletProductList[];
  outletDocument?: OutletDocument[];
  approvalStatus?: string;
  rejectReason?: string;
  status?: string;
  bankName?: string;
  accountNo?: string;
  swiftCode?: string;
  deliveryRouteAreaOne?: string;
  deliveryRouteAreaTwo?: string;
  createdAt?: string;
  userDefineFieldIds?: string[];
  stateCode?: string;
  countryCode?: string;
  taxIncomeNumber?: string;
  deliveryRouteMode?: string;
  deliveryFrequency?: number;
  deliverySchedule?: any;
  suffixCode?: string;
  controlItemList?: string[];
  taxIdentificationNumber?: string;
  requiredEInvoice?: string;
  eInvoiceComplianceStatus?: string;
  businessRegistrationNumber?: string;
  generalTIN?: string;
  isRetailerCPOEnabled?: string;
  companyCode?: string;
  shippingCode?: string;
};

export type CreditTermRate = {
  startDay?: number;
  endDay?: number;
  rate?: number;
  sign?: string;
};

export type CreditTermLastTier = {
  startFrom?: number;
  frequency?: number;
  rate?: number;
  index?: number;
};

export type ProductPriceGroup = {
  id?: string;
  productCatalogueId?: string;
  companyId?: string;
  priceGroupId?: string;
  productUOMId?: string;
  costPrice?: number;
  sellingPrice?: number;
  startDate?: Date;
  endDate?: Date;
  formatStartDate?: string;
  formatEndDate?: string;
  status?: string;
  productTradeInfoId?: string;
};

export type ProductCategorySummary = {
  _id?: string;
  subCategoryIds?: string[];
};

export type ProductCategories = {
  id?: string;
  name?: string;
  parentCategoryIds?: string[];
  picture?: any;
  description?: string;
  status?: string;
};

export type OutletInventory = {
  inventoryProductId?: string;
  inventoryQuantity?: number;
  inventoryLastUpdateDate?: Date;
};
export type OutletPromotion = {
  productId?: string;
  count?: number;
  promoIds: string[];
};

export type OutletProductList = {
  productId?: string;
  mustSellSKU?: string;
  top100?: string;
  averageSalesOrders?: number;
  targetInventoryDay?: number;
  safetyStockDay?: number;
  orderFactor?: number;
  isControlItem?: string;
  isControlDocUploaded?: string;
  productUOM?: ProductUOM[];
};

export type OutletDocument = {
  documentType?: string;
  documentFilename?: string;
  startDate?: Date;
  formatStartDate?: string;
  endDate?: Date;
  formatEndDate?: string;
  isVerified?: string;
};

export type Staff = {
  id?: string;
  firstName?: string;
  lastName?: string;
  name?: string;
  email?: string;
  contact?: number;
  ic?: number;
  icFrontPicture?: string[];
  icBackPicture?: string[];
  staffLevelId?: string;
  roleIds?: string[];
  companyId?: string;
  companyLabel?: string;
  routeIds?: string[];
  profilePicture?: string[];
  status?: string[] | string;
  staffRefCode?: string;
  staffCode?: string;
  staffType?: string;
};

export type Product = {
  signedPicture?: React.ReactNode;
  id: string;
  sku: string;
  name: string;
  description: string;
  categoryIds: string[];
  isNewProduct: string;
  newStartDate?: Date;
  newEndDate?: Date;
  shelfLife?: number;
  packingInfo: string;
  conversionToLayer?: number;
  conversionToPallet?: number;
  conversionToSmallestUOM?: number;
  cubicMeter: number;
  sellingType: string;
  isControlItem: string;
  controlDocType: string;
  isTaxExempt: string;
  taxCategoryIds: string[];
  productUOM?: ProductUOM[];
  listPrice?: Object[];
  status: string;
  brandId?: string;
  // Array of pictures to be removed
  removedPictures?: string[];
};

export type ProductUOM = {
  productUOMId?: string;
  barcode?: string;
  pictures?: string[];
  productUOM?: string | undefined;
  isSmallest?: string | undefined;
  smallerUOMId?: string;
  smallerUOM?: string | undefined;
  conversionToSmallestUOM?: number;
  isDefaultUOM?: string | undefined;
  moq?: number;
  packingDimension?: string;
  packingLength?: number;
  packingWidth?: number;
  packingHeight?: number;
  weightUnit?: string;
  netWeight?: number;
  grossWeight?: number;
  conversionToPallet?: number;
  conversionToLayer?: number;
  preview?: UploadFile[];
  signedPicture?: React.ReactNode;
};

export interface UploadFile<T = any> {
  uid: string;
  size?: number;
  name: string;
  fileName?: string;
  lastModified?: number;
  lastModifiedDate?: Date;
  url?: string;
  // status?: UploadFileStatus;
  percent?: number;
  thumbUrl?: string;
  crossOrigin?: React.ImgHTMLAttributes<HTMLImageElement>["crossOrigin"];
  // originFileObj?: RcFile;
  response?: T;
  error?: any;
  linkProps?: any;
  type?: string;
  xhr?: T;
  preview?: string;
}

export type Invoice = {
  id?: string;
  invoiceNo?: string;
  invoiceDate?: Date | string;
  salesOrderId?: string;
  outletId?: string;
  companyId?: string;
  shippingAddressId?: string;
  doNo?: string;
  doDocument?: string;
  doDate?: Date;
  invoiceProducts?: ProductOrdered[];
  invoiceDocument?: string;
  status?: string;
  createdAt?: Date;
  amount?: number;
  netAmount?: number;
  paidAmount?: number;
  invoiceToteboxes?: InvoiceToteBoxes[];
  shippingFee?: number;
  totalTax?: number;
  companyBranchId?: string;
  doSignedDocument?: string;
  invoiceSignedDocument?: string;
  eInvoiceSignature?: string;
  eInvoiceSubmissionType?: string;
  eInvoiceStatus?: string;
  eInvoiceStatusReason?: string;
  eInvoiceUuid?: string;
  eInvoiceSubmissionUid?: string;
  eInvoiceSubmittedAt?: string;
  eInvoiceValidatedAt?: string;
  eInvoiceRespondedAt?: string;
  eInvoiceRequestCancelAt?: string;
  eInvoiceCancelledAt?: string;
  einvoiceLastPrintTime?: string;
  processingAmount?: number;
  companyCode?: string;
  companyName: string;
  unloadedProductImages?: string[];
  unloadedAt?: string;
  unloadStatus?: string;
  unloadRemark?: string;
  pickStatus?: string;
  pickedAt?: string;
};

export type InvoiceToteBoxes = {
  toteboxId?: string;
  sealId?: string;
  toteboxProducts?: ProductOrdered[];
};

export type ProductOrdered = {
  productUOMId: string;
  productId?: string;
  productName?: string;
  productUOMName?: string;
  promotionIds?: string[];
  promotionCodes?: string[];
  productSku?: string;
  price?: number;
  quantity?: number;
  batchDetails?: BatchDetail[];
  type?: string;
  discount?: number;
  total?: number;
  taxId?: string;
  taxRate?: number;
  picture?: string;
  uom?: string;
  returnableUOMId?: string;
  returnableQuantity?: number;
  processingQuantity?: number;
  disable?: string;
};

export type BatchDetail = {
  inventoriesId?: string;
  batchNo?: string;
  batchQuantity?: number;
  inventoryId?: string;
};

export type Reason = {
  id?: string;
  description?: string;
  rootReasonId?: string[];
  department?: string;
  nonReturnable?: string;
  skipApproval?: string;
  status?: string;
};

export type UOM = {
  id?: string;
  name?: string;
  description?: string;
  status?: string;
  approvalStatus?: string;
};

export type SalesOrder = {
  key: string;
  id?: string;
  salesOrderNo?: string;
  outletId?: string;
  retailerId?: string;
  estimatedDeliveredAt?: string;
  invoiceId?: string;
  staffId?: string;
  deliveredAt?: string;
  subTotal?: number;
  subTotalDiscount?: number;
  totalTax?: number;
  currencyId?: string;
  currencyRate?: number;
  companyId?: string;
  companyBranchId?: string;
  cpo?: string;
  shippingAddressId?: string;
  productOrdered?: ProductOrdered[];
  status?: string;
  statusReason?: string;
  createdAt?: Date | string;
  reasonId?: string;
  remark?: string;
  shippingFee?: number;
  originalSalesOrder?: SalesOrder;
  originalSalesOrderId?: string;
  applicantNameNType?: string;
  manualAddress?: string;
  orderDate?: string
};

export type OutletShippingAddress = {
  id?: string;
  shippingAddressDescription?: string;
  unitNo?: string;
  address1?: string;
  address2?: string;
  country?: string;
  state?: string;
  city?: string;
  postalCode?: string;
  shippingContactPerson?: string;
  shippingOfficePhone?: string;
  shippingMobilePhone?: string;
  shippinglongitude?: string;
  shippinglatitude?: string;
  isDefaultShipping?: string;
  replenishData?: ShippingReplenishmentSetting;
  outletId?: string;
  status?: string;
  edited?: boolean;
  index?: number;
  description?: string;
};

export type ShippingReplenishmentSetting = {
  id?: string;
  shippingId?: string;
  frequency?: string;
  weeks?: string[];
  days?: string[];
  productIds?: string[];
};

export type CreditNote = {
  id?: string;
  creditNoteNo?: string;
  invoiceId?: string;
  debitNoteId?: string;
  type?: string;
  outletId?: string;
  companyId?: string;
  warehouseId?: string;
  staffId?: string;
  goodsReturnId?: string;
  glCode?: string;
  reasonId?: string;
  grossAmount?: number;
  taxAmount?: number;
  remark?: string;
  creditNoteProducts?: CreditNoteProduct[];
  creditNoteDocument?: string;
  uploadDocuments?: string[];
  status?: string;
  createdAt?: Date;
  usedAmount?: number;
  companyBranchId?: string;
};

export type CreditNoteProduct = {
  productId?: string;
  quantity?: number;
  returnQuantity?: number;
  productUOMId?: string;
  discount?: number;
  price?: number;
  taxId?: string;
  taxRate?: number;
  type?: string;
  batchNo?: string;
  expiryDate?: Date;
  returnableQuantity?: number;
  batchDetail?: BatchDetail[];
};

export type DebitNote = {
  id?: string;
  debitNoteNo?: string;
  invoiceId?: string;
  creditNoteId?: string;
  type?: string;
  outletId?: string;
  companyId?: string;
  warehouseId?: string;
  glCode?: string;
  staffId?: string;
  reasonId?: string;
  grossAmount?: number;
  taxAmount?: number;
  remark?: string;
  debitNoteProducts?: DebitNoteProduct[];
  debitNoteDocument?: string;
  uploadDocuments?: string[];
  status?: string;
  createdAt?: Date;
  paidAmount?: number;
  companyBranchId?: string;
};

export type DebitNoteProduct = {
  key?: string;
  productId?: string;
  quantity?: number;
  returnQuantity?: number;
  productUOMId?: string;
  discount?: number;
  price?: number;
  taxId?: string;
  taxRate?: number;
};

export type StatementList = {
  key?: React.ReactNode;
  id?: string;
  date?: Date;
  outletId?: string;
  outletCode?: string;
  outletName?: string;
  statementNo?: string;
  statementDate?: string;
  statementAmount?: number;
  paidAmount?: number;
  outstandingAmount?: number;
  lastPaymentStatus?: string;
  statementStatus?: string;
  statementDueDate?: string;
  document?: string;
  invoiceIds?: string[];
  paymentIds?: string[];
  invoice?: Invoice[];
  creditNote?: CreditNote[];
  documentList?: DocumentList[];
  status?: string;
  invoices?: Invoice[];
  creditNotes?: CreditNote[];
  debitNotes?: DebitNote[];
};

export type DocumentList = {
  key?: React.ReactNode;
  documentNo?: string;
  documentDate?: string;
  documentType?: string;
  documentAmount?: number;
  paidAmount?: number;
};

export type Payment = {
  key?: React.ReactNode;
  id?: string;
  date?: string | Date;
  outletId?: string;
  staffId?: string;
  receiptNo?: string;
  amount?: number;
  method?: string;
  status?: string;
  invoiceDocuments?: InvoiceDocument[];
  creditNoteDocuments?: CreditNoteDocument[];
  debitNoteDocuments?: DebitNoteDocument[];
  paymentDetails?: PaymentDetail[];
  paymentProcessStatus?: string;
  officialReceipt?: string;
  createdAt?: string;
  retailerId?: string;
  userId?: string;
  applicantNameNType?: string;
};

export type InvoiceDocument = {
  statementId?: string;
  invoiceId?: string;
  amount?: number;
  approvedAmount?: number;
};

export type CreditNoteDocument = {
  creditNoteId?: string;
  amount?: number;
  paymentStatus?: string;
};

export type DebitNoteDocument = {
  debitNoteId?: string;
  amount?: number;
};

export type PaymentDetail = {
  receipt?: string[];
  referenceNo?: string;
  referenceDate?: string;
  type?: string;
  amount?: number;
  paymentStatus?: string;
  reasonId?: string;
  approvedDate?: string;
  bankName?: string;
  receiptImage?: UploadFile[];
};

export type User = {
  id?: string;
  index?: number;
  firstName?: string;
  lastName?: string;
  email?: string;
  contact?: string;
  roleIds?: string[];
  policies?: any;
  // {
  //   [key: string]: string;
  // };
  companyId?: string;
  companyBranchIds?: string[];
  gender?: string;
  race?: string;
  country?: string;
  password?: string;
  status?: string;
  organisationId?: string;
  viewSubDistributor?: string;
};

export type PromotionCriteria = {
  tier: number;
  individualMOQ?: number;
  groupMOQ?: number;
  totalBuy?: number;
};

export type BatchDetails = {
  inventoryId?: string;
  batchNo?: string;
  expiryDate?: Date;
  quantityAllocated?: number;
  quantityUsed?: number;
};

export type SelectedProduct = {
  productId?: string;
  productBatch?: BatchDetails[];
  claimFromCompanyDetails?: ClaimFromCompanyDetails[];
};

export type ClaimFromCompanyDetails = {
  companyId?: string;
  companyName?: string;
  supplierCompanyId?: string;
  supplierCompanyName?: string;
  // inventoryId?: string;
  productId?: string;
  claimAmount?: number;
  miscAmount?: number;
};

export type BatchInfo = {
  key?: React.ReactNode;
  productKey?: string;
  groupName?: string;
  companyId?: string;
  batchNo?: string;
  quantity?: number;
  quantityUsed?: number;
  originalQuantity?: number;
  quantityAvailable?: number;
  quantityAllocated?: number;
  expiryDate?: Date;
  productCode?: string;
  productId?: string;
  id?: string;
  selectedStatus?: string;
  claimFromCompanyId?: string;
};

export type FOCProduct = {
  group?: number;
  focProductId?: string;
  focProductUOMId?: string;
  focQuantity?: number;
  focBatch?: BatchInfo[];
  claimFromCompanyDetails?: ClaimFromCompanyDetails[];
};

export type DiscountRecurring = {
  everySet?: number;
  everySetType?: string;
  productGroup?: string;
};

export type Discount = {
  type?: string;
  tier?: number;
  quantityFrom?: number;
  quantityTo?: number;
  everyRecurring?: DiscountRecurring[];
  maxCap?: number;
  maxCapType?: string;
  discountValue?: number;
  focProducts?: FOCProduct[];
};

export type ProductGroup = {
  defaultGroupName?: string;
  productGroupName?: string;
  isPromotionApplied?: string;
  individualMOQ?: number;
  groupMOQ?: number;
  totalBuy?: number;
  uomId?: string;
  productGroupDiscounts?: Discount[];
  selectedProducts?: SelectedProduct[];
  isRecurring?: string;
  promotionCriterias?: PromotionCriteria[];
};

export type BudgetUsedDetail = {
  salesOrderId?: string;
  date?: Date;
  allocationTimes?: number;
  amount?: number;
};

export type ParticipatedOutlet = {
  outletId?: string;
  budgetAllocated?: number;
  isUnlimitedOutletAllocation?: string;
  budgetUsed?: number;
  budgetUsedDetails?: BudgetUsedDetail[];
};

export type Promotion = {
  id?: string;
  companyIds?: string[] | React.Key[];
  name?: string;
  chargeCode?: string;
  promotionType?: string;
  discountType?: string;
  discountVolume?: string;
  discountCalculationType?: string;
  promotionCode?: string;
  promoType?: string;
  isClaimable?: string;
  isAutoClaim?: string;
  claimFrequency?: string;
  claimDate?: Date[];
  isUnlimitedAllocation?: string;
  isUnlimitedBudget?: string;
  budget?: number;
  promotionAllocation?: number;
  cappingDuration?: number;
  cappingDurationType?: string;
  startDate?: Date | string;
  endDate?: Date | string;
  claimStartDate?: Date | string;
  claimEndDate?: Date | string;
  totalBuy?: number;
  discounts?: Discount[];
  productGroups?: ProductGroup[];
  participatedOutlets?: ParticipatedOutlet[];
  uploadDocuments?: string[];
  images?: string[];
  createdByCompanyId?: string;
  status?: string;
  maxCapRepeat?: number;
  maxCap?: number;
  maxCapType?: string;
  tieringType?: string;
  outletCategoryIds?: string[];
  companyBranchIds?: string[];
  isAutoApply?: string;
  isEmptyOutlets?: string;
};

export type Taxes = {
  id?: string;
  name?: string;
  rate?: number;
  status?: string;
  approvalStatus?: string;
};

export type Cart = {
  id?: string;
  companyBranchId: string;
  companyId: string;
  outletId: string;
  retailerId: string;
  productId: string;
  productUOMId: string;
  promotionIds: string[];
  quantity: number;
  unitPrice: number;
  totalDiscount: number;
  totalPrice: number;
  totalNetPrice: number;
  sellingType: string; //PERORDER or SELLING
  status: string;
  supplierCompanyId: string;
  totalTax: number;
  taxId?: string;
  taxRate: number;
  type: string; // "SALES or FOC"
  minimumOrder?: number;
  defaultUOMId?: string;
  tradeType?: string;
  productTradeInfoId?: string;
  priceGroupId?: string;
  unitPriceChanged?: boolean;
};

export type ProductExceedQuantity = {
  productName: string;
  promoSalesPercentage: number;
  nonPromoSalesPercentage: number;
};

export type CompanyAverageSales = {
  productId: string;
  productName: string;
  promoSales?: number;
  quantity?: number;
  promoDateCount?: number;
  nonPromoSales?: number;
  nonPromoDateCount?: number;
};

export type Retailer = {
  id?: string;
  index?: number;
  firstName?: string;
  lastName?: string;
  email?: string;
  contact?: string;
  ic?: string;
  icFrontPicture?: string[];
  icBackPicture?: string[];
  roleIds?: string[];
  policies?: any;
  companyId?: string;
  companyBranchId?: string;
  companyBranchIds?: string[];
  gender?: string;
  race?: string;
  country?: string;
  password?: string;
  status?: string;
  organisationId?: string;
  viewSubDistributor?: string;
  outletIds?: string[];
  retailerRefCode?: string;
  companyName?: string;
  companyCode?: string;
};

export type ProductTradeInfo = {
  id: string;
  productCatalogueId: string;
  companyId: string;
  top100?: string;
  receivingShelfLife: number;
  targetInventoryDay: number;
  safetyStockDay: number;
  orderFactor: number;
  minimumOrder: number;
  defaultUOMId: string;
  tradeType?: string;
  tradeLabels?: string;
  status?: string;
};
export type Currency = {
  id?: string;
  companyId?: string;
  baseCountry?: string;
  baseCurrencyCode?: string;
  foreignCountry?: string;
  foreignCurrencyCode?: number;
  currencyRates?: CurrencyRate[];
  status?: string;
  approvalStatus?: string;
};

export type CurrencyRate = {
  index?: number;
  sellingRate?: number;
  buyingRate?: number;
  averageRate?: number;
  formatStartDate?: string;
  formatEndDate?: string;
  startDate?: Date;
  endDate?: Date;
};

export type PreOrderSetting = {
  id?: string;
  name?: string;
  companyId?: string;
  companyBranchId?: string;
  outletIds?: string[];
  outletCategoryIds?: string[];
  supplierCompanyId?: string;
  productIds?: string[];
  preOrderType?: string;
  startDate?: string;
  endDate?: string;
  weeklySchedule?: {
    [day: string]: number[];
  };
  orderLeadTime?: number;
  status?: string;
};

export type FPX = {
  id?: string;
  companyId?: string;
  companyBranchId?: string;
  retailerId?: string;
  outletId?: string;
  date?: string;
  bankName?: string;
  referenceNo?: string;
  amount?: number;
  invoiceDocuments?: InvoiceDocument[];
  status?: string;
};

export type TradeInfoAggregate = {
  tradeInfoId: string;
  productPriceGroupId: string;
  productCatalogueId: string;
  companyId: string;
  priceGroupId: string;
  sellingPrice: number;
  minimumOrder: number;
  receivingShelfLife?: number;
  targetInventoryDay?: number;
  safetyStockDay?: number;
  deliveryLeadTime?: number;
  defaultUOMId: string;
  picture?: string;
  sku?: string;
  barcode?: string;
  name: string;
  description?: string;
  packingInfo?: string;
  tradeType?: string;
  tradeLabels?: string;
  minimumIncrement?: number;
  isNewProduct?: boolean;
};

export type TradeInfoAggregateSub = {
  isSinglePromo?: boolean;
  isBundlePromo?: boolean;
  outletCompanyId?: string;
  promotionInitId?: string[];
  uomId?: string;
  quantity?: number;
  taxCategoryIds?: string[];
};

export type TradeInfoAggregateUI = TradeInfoAggregate & TradeInfoAggregateSub;

export type PromotionOutlets = {
  id: string;
  companyId: string;
  companyBranchId: string;
  promotionId: string;
  promotionCode: string;
  outletId: string;
  outletCategoryIds: string[];
  budgetAssigned: number;
  budgetAllocated: number;
  isUnlimitedOutletAllocation: string;
  budgetUsed: number;
  status: string;
};

export type ProductAggreateUI = Product & TradeInfoAggregateSub;

export type ConfirmModalProps = {
  title: any;
  content: React.ReactNode;
  okText?: string;
  cancelText?: string;
  onOk?: () => void;
  onCancel?: () => void;
  className?: string;
  maskClosable?: boolean;
};

export type ProductCartChecked = {
  checkAll?: boolean;
  checked?: boolean;
  name: string;
  code: string;
  picture?: string;
  uom: string;
  promotionApplyIds?: string[];
  promotionManualApplyIds?: string[];
};

export type ProductCartUI = Cart & ProductCartChecked;

export type PromotionFOCReturnResult = {
  BatchNo: string;
  InventoryId: string;
  ProductId: string;
  PromotionId: string;
  Quantity: number;
  ProductUOMId?: string;
  companyId?: string;
  companyBranchId?: string;
  sellingType?: string;
};

export type PreOrder = {
  key?: string;
  id?: string;
  preorderSettingId?: string;
  preorderNo?: string;
  retailerId?: string;
  outletId?: string;
  estimatedDeliveredAt?: string;
  shippingAddressId?: string;
  companyId?: string;
  companyBranchId?: string;
  supplierCompanyId?: string;
  totalPrice?: number;
  totalDiscount?: number;
  totalTax?: number;
  currencyId?: string;
  totalNetPrice?: string;
  productsPreordered?: ProductsPreordered[];
  status?: string;
  statusReason?: string[];
  createdAt?: Date | string;
};

export type ProductsPreordered = {
  type?: string;
  productId?: string;
  productUOMId?: string;
  price?: number;
  quantity?: number;
  promotionIds?: string[];
  discount?: number;
  total?: number;
  taxId?: string;
  taxRate?: number;
  originalUnitPrice?: number;
  unitPrice?: number;
  originalQuantity?: number;
  originalPromotionIds?: string[];
  originalUnitDiscount?: number;
  unitDiscount?: number;
  originalTotalDiscount?: number;
  totalDiscount?: number;
  originalTotalPrice?: number;
  totalPrice?: number;
  originalTotalNetPrice?: number;
  totalNetPrice?: number;
  originalTotalTax?: number;
  totalTax?: number;
};

export type OutletAverageSale = {
  outletId: string;
  productSales: OutletAverageSaleProductSales[];
};

export type OutletAverageSaleProductSales = {
  productId: string;
  promoSales: number;
  promoDateCount: number;
  totalPromoDateRange: number;
  averagePromoSales: number;
  nonPromoSales: number;
  nonPromoDateCount: number;
  totalNonPromoDateRange: number;
  averageNonPromoSales: number;
};

export type UomConversion = {
  conversionToSmallestUOM: number;
  productUOMId: string;
};

export type Totebox = {
  id?: string;
  code?: string;
  description?: string;
  companyId?: string;
  currentWarehouseId?: string;
  currentOutletId?: string;
  status?: string;
  approvalStatus?: string;
};

export type RetailerBanner = {
  companyId: string;
  bannerType: string;
  images: string[];
  status: string;
  preview?: any;
};

export interface Fpx {
  id: string;
  companyId: string;
  userId: string;
  paymentID: string;
  boxID: string;
  userID: string;
  cashierID: string;
  invNo: string;
  currencyID: string;
  totalAmt: number;
  discAmt: number;
  remark: string;
  buyerBankId: string;
  buyerEmail: string;
  invDetail: FpxInvoiceDetail[];
  resultCode: string;
  errorDes: string;
  payPartnerId: string;
  tradeNo: string;
  orderCode: string;
  totalAmount: number;
  paidAmount: number;
  discountAmount: number;
  feeType: string;
  paymentTime: string;
  closeTime: string;
  notifyTime: string;
  checkCode: string;
  ResultDetails: FpxResultDetails;
  responsedAt: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export interface FpxInvoiceDetail {
  sku?: string;
  qty?: number;
}

export interface FpxResultDetails {
  TransactionStatus: string;
  BingoResult: string;
  PaymentResultGuid: string;
  CallDate: string;
  MerchantId: string;
  InvoiceNo: string;
  Amount: string;
  McpAmount: string;
  McpFxRate: string;
  McpCurrencyCode: string;
  CurrencyCode: string;
  TransactionDateTime: string;
  AgentCode: string;
  ChannelCode: string;
  ApprovalCode: string;
  ReferenceNo: string;
  TranRef: string;
  Pan: string;
  CardToken: string;
  IssuerCountry: string;
  Eci: string;
  InstallmentPeriod: string;
  InterestType: string;
  InterestRate: string;
  InstallmentMerchantAbsorbRate: string;
  RecurringUniqueID: string;
  FxAmount: string;
  FxRate: string;
  FxCurrencyCode: string;
  UserDefined1: string;
  UserDefined2: string;
  UserDefined3: string;
  UserDefined4: string;
  UserDefined5: string;
  RespCode: string;
  RespDesc: string;
  BoxID: string;
  UserID: string;
  CashierID: string;
  InvNo: string;
  CurrencyID: string;
  TotalAmt: string;
  DiscAmt: string;
  PaymentID: string;
}

export type ConfigurableField = {
  id: string;
  name: string;
  value: string;
  status: string;
}